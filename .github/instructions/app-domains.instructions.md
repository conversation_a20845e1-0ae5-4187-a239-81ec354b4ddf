---
applyTo: "*.rb"
description: ""
---

Classes that contain commands and queries should be placed in the `app/domains/` directory. 
This directory is intended to hold domain-specific logic that expresses our application's core functionality independetly
of the Rails framework.

The classes within `app/domains/` are organised into subdirectories based on their purpose:
- `commands/`, for classes that effect some change or action
- `queries/`, for classes that read data from the system
Each directory should map to its own Ruby module. For example, `app/domains/questions/` would contain classes exclusively within the `Questions` module. The `domains` segment of the file path does _not_ map to a module.
Any supporting code for the command and query classes should live inside the same domain directory. Further subdirectories (and corresponding module namespaces) can be used to organise this code.

An example of how to name a query class that live in `app/domains/responses/queries/find_response.rb`:

```ruby
module Responses
  module Queries
    class FindResponse
      # Class implementation
    end
  end
end
```
