---
applyTo: "app/domains/**/commands/*.rb"
description: ""
---

Command classes should follow the structure outlined below. This structure is designed to ensure clarity, maintainability, and consistency across the codebase.

```ruby
class MyCommand
  # Class-level documentation
  # This command performs a specific action based on the provided parameters.

  # Constructor
  # Use dependency injection to pass parameters into the command.
  def initialize(my_dependency: MyDependency.new )
    @my_dependency = my_dependency
  end

  # Public method to execute the command
  def call(some_aggregate_id:, new_value:)
    # Implementation of the command logic
    # Example:
    # record = MyModel.find(some_aggregate_id)
    # record.update!(some_attribute: new_value)

    # Use the dependency to perform some action
    # my_dependency.perform_action(some_aggregate_id: some_aggregate_id)
  end

  private
  
  # Class attributes
  attr_reader :my_dependency

  # Private methods can be defined here to support the public interface
end
```

# Command Class Structure
Command classes should adhere to the following structure:
1. Class attributes: Use `attr_reader` for any dependencies that need to be accessed.
2. Constructor: Initialize the command with any necessary dependencies.
3. Public method `call`: This method should contain the logic to execute the command and take keyword arguments as needed. It should be the only public method.
4. Private methods: Use private methods to encapsulate any helper logic that supports the public interface.
5. Dependencies: Use dependency injection to pass in any required dependencies, such as services or repositories, to the command class.
6. Exceptions that are raised should be raised to the caller, not handled within the command class as Failures.
7. If a command fails, it should raise an exception that is meaningful to the caller.
8. Command classes should have self-descriptive names that indicate their purpose, such as `Participants::Commands::BulkInviteParticipants` or `SurveyDesign::Commands::CreateSurvey`.
9. Commands must be stateless and should not maintain any internal state between calls. Each call to the `call` method should be independent and not rely on any previous state.
10. Commands should not return any data. Instead, they should perform an action and return `nil` or raise an exception if something goes wrong.
