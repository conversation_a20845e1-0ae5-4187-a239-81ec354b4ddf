---
applyTo: "*.rb"
description: ""
---

- Most Mongoid models have an `aggregate_id` as well as an `id` field. The `aggregate_id` is preferred when exposing an identifier to other parts of the system.
- The `id` field is used internally by Mongoid and should not be exposed outside of the model.
- After a change is made on a Ruby file, run `standardrb <filename> --fix` to fix any linting issues.
- When adding new files, ensure they are placed in the correct directory structure and follow the naming conventions outlined in the documentation.
- Queries are to be placed in `app/domains/**/queries/*.rb` and should follow the structure outlined in the query class structure documentation.
- Commands are to be placed in `app/domains/**/commands/*.rb` and should follow the structure outlined in the command class structure documentation.
