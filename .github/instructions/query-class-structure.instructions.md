---
applyTo: "app/domains/**/queries/*.rb"
description: ""
---

Query classes should follow the structure outlined below. This structure is designed to ensure clarity, maintainability, and consistency across the codebase.

```ruby
class MyQuery
  # Class-level documentation
  # This query retrieves a specific set of records based on the provided parameters.

  module Types
    include Dry.Types()
  end

  class MyResultModel < Dry::Struct
    # Define the structure of the result model if needed
    # Example: attribute :name, Types::String
  end

  # Constructor
  # Use dependency injection to pass parameters into the query.
  def initialize(params = {})
    @params = params
  end

  # Public method to execute the query
  def call(some_aggregate_id:)
    # Implementation of the query logic
    # Example: 
    # my_records = Model.where(some_aggregate_id: some_aggregate_id)
    # my_records.map { |record| MyResultModel.new(record.attributes) }
  end

  private
  
  # Class attributes
  attr_reader :params

  # Private methods can be defined here to support the public interface
end
```

# Query Class Structure
Query classes should adhere to the following structure:
1. Class attributes: Use `attr_reader` for any parameters that need to be accessed.
2. Constructor: Initialize the query with any necessary parameters.
3. Public method `call`: This method should contain the logic to execute the query and take keyword arguments as needed. It should be the only public method.
4. Private methods: Use private methods to encapsulate any helper logic that supports the public interface.
5. Result model: If the query returns a structured result, define a nested `MyResultModel` class using `Dry::Struct` to represent the output. This ensures the result is well-defined and removes the connection to the database from the result.
5a. The result model should not allow the assignment of database objects as attributes.
    e.g. `attribute :user, Types::Instance(User)` should not be used, instead use `attribute :user_id, Types::Integer` or `attribute :user, Types::Instance(MyUserModel)`.
5b. Queries should not directly return ActiveRecord objects. Instead, they should return instances of the result model or simple data structures like arrays or hashes.
6. Exceptions that are raised should be raised to the caller, not handled within the query class as Failures.
6a. If a query fails, it should raise an exception that is meaningful to the caller, such as `ActiveRecord::RecordNotFound` or a custom exception that indicates the nature of the failure.
7. Query classes should have self descriptive names that indicate their purpose, such as `Responses::Queries::FindResponse` or `Responses::Queries::ResponsesForUser`.
8. Queries must be stateless and should not maintain any internal state between calls. Each call to the `call` method should be independent and not rely on any previous state.
