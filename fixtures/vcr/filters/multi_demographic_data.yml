---
http_interactions:
- request:
    method: get
    uri: http://localhost:8081/snapshot_reporting_engine/participation/<%= survey_id %>?demographic_spreads%5B%5D=<%= spread_stq_id %>&filters%5B%5D=<%= stq_id_1 %>-<%= so_id_1 %>&filters%5B%5D=<%= stq_id_2 %>-<%= so_id_2 %>&hierarchy_filters&include_overall=true&previewing=true
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json;charset=ISO-8859-1
      Date:
      - Tue, 27 May 2025 09:27:03 GMT
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"overall":{"invitation_count":1000,"submission_count":850},"filtered":{"invitation_count":1000,"submission_count":850},"spreads":{"<%= spread_stq_id %>":{"<%= spread_so_id %>":{"invitation_count":1000,"submission_count":850}}},"hierarchy_spreads":{},"cross_spreads":{},"privacy":{"original_responses":850,"removed_responses":0,"removed_groups":{},"spreads":{"<%= spread_stq_id %>":{"original_responses":850,"removed_responses":0,"removed_groups":{}}}}}'
    http_version:
  recorded_at: Tue, 27 May 2025 09:27:03 GMT
recorded_with: VCR 6.1.0
