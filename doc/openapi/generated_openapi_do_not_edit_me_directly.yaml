openapi: 3.0.0
info:
  title: Murmur APIs
  description: "Specification/documentation for the endpoints available in murmur,\
    \ starting with the new survey insight report and process for importing report\
    \ sharing permissions using a v2 controller."
  version: 0.0.1
servers:
- url: http://localhost:3030
  description: Web Gateway running as proxy
- url: http://localhost:3000
  description: Directly to Murmur
- url: /
  description: Directly to murmur for frontend compatibility with frontend-api lib
- url: sgw://murmur
  description: Access to murmur via the service gateway protocol
paths:
  /api/murmur/employee_integration/app_data:
    get:
      tags:
      - app_data
      summary: app data payload
      operationId: app_data
      parameters:
      - name: active_account_id
        in: query
        required: false
        schema:
          type: string
      responses:
        "200":
          description: OK response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AppDataResponse'
              examples:
                no integration:
                  value:
                    account:
                      admins:
                      - id: 549109-1000-100
                        email: <EMAIL>
                      - id: 749109-1000-100
                        email: <EMAIL>
                      credentials:
                        sftp:
                          username: murmur
                          hostname: secure.employee-import.integrations.panther.development.cultureamp.net
                        legacy_sap:
                          username: murmur
                          password: secure.employee-import
                      elmo_enabled: true
                      ceridian_enabled: true
                      employee_import_link: /app/employee-import-workflow/account/449c77c7-4ec1-4b91-a1ea-dd7c48171a57/welcome
                      hibob_enabled: true
                      id: 61829f50-5119-4c35-b0da-22232f562433
                      legacy_sap_enabled: false
                      legacy_sap_icd_download_path: /murmur/current/lib/hris/successfactors/culture_amp_definition.icd
                      paylocity_enabled: true
                      time_zone:
                        name: Australia/Melbourne
                        abbreviated: AEST
                    active_integration: null
                    current_user:
                      is_superuser: true
                hris integration:
                  value:
                    account:
                      admins:
                      - id: 549109-1000-100
                        email: <EMAIL>
                      - id: 749109-1000-100
                        email: <EMAIL>
                      credentials:
                        sftp:
                          username: murmur
                          hostname: secure.employee-import.integrations.panther.development.cultureamp.net
                        legacy_sap:
                          username: murmur
                          password: secure.employee-import
                      elmo_enabled: true
                      ceridian_enabled: true
                      employee_import_link: /app/employee-import-workflow/account/449c77c7-4ec1-4b91-a1ea-dd7c48171a57/welcome
                      hibob_enabled: true
                      id: 61829f50-5119-4c35-b0da-22232f562433
                      legacy_sap_enabled: false
                      legacy_sap_icd_download_path: /murmur/current/lib/hris/successfactors/culture_amp_definition.icd
                      paylocity_enabled: true
                      time_zone:
                        name: Australia/Melbourne
                        abbreviated: AEST
                    active_integration:
                      adaptor: bamboo
                      status: enabled
                      auth_user_id: 593891-30032-1001-2222
                      credentials: {}
                      daily_sync: true
                      notify_user_email: <EMAIL>"
                      demographics: {}
                      filters: {}
                      last_data_sync:
                        status: success
                        date: 2021-09-01T00:00:00Z
                        summary_link: /app/hris-integrations/593891-30032-1001-2222
                    current_user:
                      is_superuser: true
  /api/murmur/employee_integration/set_integration:
    put:
      tags:
      - set-integration
      summary: set integration
      operationId: set-integration
      parameters:
      - name: active_account_id
        in: query
        required: false
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetIntegrationRequest'
            example:
              adaptor: securefile
              notification_receiver_id: 593891-30032-1001-2222
              credentials:
                alias: Employee Hero
                hostname: http://ca.murmur
                username: hooli
              publicKey: shh erowiWooakDGkraQQ
        required: true
      responses:
        "200":
          description: OK response.
  /api/murmur/employee_integration/disable_integration:
    post:
      tags:
      - disable-integration
      summary: disable integration
      operationId: disable-integration
      parameters:
      - name: active_account_id
        in: query
        required: false
        schema:
          type: string
      - name: adaptor
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK response.
          content:
            application/json:
              schema:
                type: object
  /dimensions_api/v1/survey/{surveyAggregateId}/performance_rating_cycles:
    get:
      tags:
      - Dimensions API
      summary: Active performance rating cycles
      description: "Get all active performance rating cycles on the account, available\
        \ to be linked/unlinked from the given survey."
      operationId: getPerformanceRatingCycles
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/performanceRatingCycles'
        "403":
          description: Error response when the survey type is not supported or the
            feature flag is disabled or the user does not have the appropriate permissions
        "404":
          description: Error response when survey or the account belonging to the
            survey is not found
        "500":
          description: Error response when an unhandled exception occurs
  /dimensions_api/v1/survey/{surveyAggregateId}/link_performance_rating_cycle/{performanceCycleAggregateId}:
    post:
      tags:
      - Dimensions API
      summary: Link performance rating cycle to survey
      description: Links the performance rating cycle to the given survey.
      operationId: linkPerformanceRatingCycle
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: performanceCycleAggregateId
        in: path
        description: An aggregate id (UUID) that identifies a performance rating cycle
        required: true
        schema:
          type: string
          format: uuid
        example: 9d89f6c0-3dae-4c9f-82f2-8069d7b7d9b1
      responses:
        "200":
          description: Successful response
        "403":
          description: Error response when the survey type is not supported or the
            feature flag is disabled or the user does not have the appropriate permissions
        "404":
          description: Error response when survey/account is not found or the performance
            rating cycle is not found.
        "500":
          description: Error response when an unhandled exception occurs
  /dimensions_api/v1/survey/{surveyAggregateId}/unlink_performance_rating_cycle/{performanceCycleAggregateId}:
    post:
      tags:
      - Dimensions API
      summary: Unlink performance rating cycle to survey
      description: Unlinks the given performance rating cycle from the given survey.
      operationId: unlinkPerformanceRatingCycle
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: performanceCycleAggregateId
        in: path
        description: An aggregate id (UUID) that identifies a performance rating cycle
        required: true
        schema:
          type: string
          format: uuid
        example: 9d89f6c0-3dae-4c9f-82f2-8069d7b7d9b1
      responses:
        "200":
          description: Successful response
        "403":
          description: Error response when the survey type is not supported or the
            feature flag is disabled or the user does not have the appropriate permissions
        "404":
          description: Error response when survey/account is not found or the performance
            rating cycle is not found.
        "500":
          description: Error response when an unhandled exception occurs
  /survey-insights-api/v1/survey/{surveyId}/report/{reportId}/question/{stqId}:
    get:
      tags:
      - POC question component
      summary: Get Insight report question data
      description: Endpoint to retrieve the label and scores for a specific question
        within an insight report
      operationId: getInsightReportQuestion
      parameters:
      - name: stqId
        in: path
        description: "The active record instance/object's mongo id of the question\
          \ to retrieve. \nThis is the STQ (survey to question) id, rather than the\
          \ question id."
        required: true
        schema:
          type: string
      - name: reportId
        in: path
        description: The active record instance/object's mongo id of the report for
          a given survey
        required: true
        schema:
          type: string
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: locale
        in: query
        description: "The language to use to use when providing localisable data,\
          \ e.g. question label."
        required: true
        schema:
          type: string
          example: en
      - name: anchor
        in: query
        description: "This is the anchor filter - an un-removable demographic filter\
          \ imposed as part of a report. \n\nA single tuple stq id - select option\
          \ id pair, where the stq represents the demographic, and the the select\
          \ option represents the demographic value.\n\nOptionally, in certain administrator\
          \ scenarios, this anchor can be sent the special value \"ALL_RESULTS\".\n\
          \nAll supplied ids should be mongo style bson ids. "
        required: false
        schema:
          type: string
          example: demographicId1-optionId1
      - name: filters
        in: query
        description: |-
          A comma-separated list of dash-seperated tuples, with each tuple being a stq id - select option id pair, where the stq represents the demographic, and the the select option represents the demographic value.

          In situations where there are multiple values selected for a particular demographic, multiple tuples with the same demographic id should be provided.

          All supplied ids should be mongo style bson ids.
        required: false
        schema:
          type: string
          example: "demographicId1-optionId1,demographicId1-optionId2,demographicId3-optionId7"
      - name: comparison
        in: query
        description: |-
          Indicate a target to compare survey results against. This could be the overall survey, a benchmark, or another survey.

          This parameter controls what is returned as part of comparisonDeltaScore

          The value is a magical string, which indicates the type and source of comparison. Some examples:

          .overall - compare to the overall survey

          .auto_trend.<survey id> - compare to another survey

          <a summary bson id> - compare against a survey or benchmark.
        required: false
        schema:
          type: string
          example: .overall
      - name: preview
        in: query
        description: "Supply fake response data, used for testing reports before response\
          \ data is available."
        required: false
        schema:
          type: boolean
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Question'
  /surveys/{surveyId}/import_report_sharing_permissions_file:
    get:
      tags:
      - Import report sharing permissions file
      summary: get all import report sharing permission file background jobs for the
        user.
      description: get a list of all importReportSharingPermissionsFileJobs for the
        current user.
      operationId: getIndexSurveyImportReportSharingPermissionsFileJob
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: dismissed
        in: query
        description: Filters returned job collection to only those that have not been
          dismissed by the user.
        required: false
        schema:
          type: boolean
          example: false
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/index'
        "404":
          description: Error response when survey is not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/not_found'
    post:
      tags:
      - Import report sharing permissions file
      summary: Create background job to import report sharing permissions from validated
        file.
      description: Creates a importReportSharingPermissionsFileJob to set the permissions
        specified in a previously validated validateReportSharingPermissionsFileJob
        background job
      operationId: postCreateSurveyImportReportSharingPermissionsFileJob
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      requestBody:
        description: Body of the request must be json format.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/create'
        required: true
      responses:
        "201":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/create_1'
        "404":
          description: Error response when survey is not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/not_found'
        "400":
          description: Error response when job id is incorrect.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bad_request'
    patch:
      tags:
      - Import report sharing permissions file
      summary: Update the dismissed status on an import report sharing permissions
        job.
      description: "When a user dismisses an inline notification on the report sharing\
        \ page informing them of a import report sharing permissions job, the dismissed\
        \ field on the background job is set to true so that in future it will not\
        \ be displayed to users on the report sharing page."
      operationId: patchSurveyImportReportSharingPermissionsFileJob
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: backgroundJobId
        in: path
        description: The id for the background job that is executing the report sharing
          permissions import
        required: true
        schema:
          type: string
      requestBody:
        description: Body of the request must be json format.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/update'
        required: true
      responses:
        "204":
          description: Expected response to a valid request
        "404":
          description: Error response when survey is not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/not_found'
        "400":
          description: Error response when job id is incorrect.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bad_request'
  /surveys/{surveyId}/import_report_sharing_permissions_file/{backgroundJobId}:
    get:
      tags:
      - Import report sharing permissions file
      summary: Get the current status of the background job for a import report sharing
        permission file job.
      description: "Retrieves a payload containing status and progress of the import\
        \ report sharing permission file background job. Once the job is complete,\
        \ the payload object contains a boolean value indicating if there were any\
        \ errors that would block the import and an integer indicating the number\
        \ of lines with non-blocking errors."
      operationId: getSurveyImportReportSharingPermissionsFileJobStatus
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: backgroundJobId
        in: path
        description: The id for the background job that is executing the report sharing
          permissions import
        required: true
        schema:
          type: string
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/show'
        "404":
          description: Error response when survey is not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/not_found'
        "400":
          description: Error response when job id is incorrect.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bad_request'
  /surveys/{surveyId}/validate_report_sharing_permissions_file:
    post:
      tags:
      - Validate report sharing permissions file upload
      summary: Create a background job to validate the report sharing permissions
        files that will be used in an import
      description: "Creates a background job to validate the uploaded file which will\
        \ be used to import report sharing permissions. It returns a payload containing\
        \ the status and progress of the background job, and URL to poll for updates."
      operationId: postValidateReportSharingPermissionsFileUpload
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/octet-stream:
            schema:
              type: string
              format: binary
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/create_1'
        "404":
          description: Expected response when survey is not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/not_found'
        "422":
          description: Error response file upload to storage was not possible
  /surveys/{surveyId}/validate_report_sharing_permissions_file/new:
    get:
      tags:
      - Validate report sharing permissions file upload
      summary: Get survey information to start the validate report sharing permissions
        job.
      description: Retrieves the survey details needed for the Validate report sharing
        permissions workflow such as survey name.
      operationId: getSurveyvalidateReportSharingPermissionsFile
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/new'
        "404":
          description: Expected response when survey is not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/not_found'
  /surveys/{surveyId}/validate_report_sharing_permissions_file/{backgroundJobId}:
    get:
      tags:
      - Validate report sharing permissions file upload
      summary: Get the current status of the background job for validating the report
        sharing permissions file
      description: "Retrieves a payload containing status and progress of the validate\
        \ report sharing permission file background job. Once the job is complete,\
        \ the payload object contains a boolean value indicating if there were any\
        \ errors that would block the import and an integer indicating the number\
        \ of lines with non-blocking errors"
      operationId: getValidateSurveyReportSharingPermissionsFileJobStatus
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: backgroundJobId
        in: path
        description: The id for the background job that is executing the report sharing
          permissions import
        required: true
        schema:
          type: string
      responses:
        "200":
          description: "Response for retrieving status of the job, if the job is complete\
            \ then the response will contain a field outing if there were any fatal\
            \ errors found in the file and the number of warnings found for invalid\
            \ lines in the file intended for import."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/show_1'
        "404":
          description: Expected response when survey or background job is not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/not_found'
  /surveys/{surveyId}/validate_report_sharing_permissions_file/{backgroundJobId}/errors_download:
    get:
      tags:
      - Validate report sharing permissions file upload
      summary: Downloads the spreadsheet of non-blocking errors found validating the
        report sharing permissions file.
      description: Retrieves a spreadsheet containing the non-blocking errors found
        with the permissions file.
      operationId: getValidateSurveyReportSharingPermissionsFileJobErrors
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: backgroundJobId
        in: path
        description: The id for the background job that is executing the report sharing
          permissions import
        required: true
        schema:
          type: string
      responses:
        "200":
          description: The spreadsheet containing the non-blocking errors found in
            the file to import.
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
        "404":
          description: Expected response when survey or background job is not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/not_found'
  /reporting_api/survey/{surveyId}/responses:
    get:
      tags:
      - reporting_api
      - engagement
      - responses
      summary: Get responses for a survey
      description: Endpoint to get Questions and Answers for a SurveyId
      operationId: getInsightReportQuestion
      parameters:
      - name: reportId
        in: path
        description: The active record instance/object's mongo id of the report for
          a given survey
        required: true
        schema:
          type: string
      - name: limit
        in: query
        description: A maximum number of results to fetch
        required: false
        schema:
          type: integer
          example: 250
      - name: cursor
        in: query
        description: A string representing an id that points to the next page of results.
          This should have been sent in the response to a previous request.
        required: false
        schema:
          type: string
          example: NTg3OWJlNWI2MjVmMTUwMDdlMDAxYmY3
      - name: start_date
        in: query
        description: The start date to filter responses by
        required: false
        schema:
          type: string
          format: date-time
          example: 2010-01-01T06:00:00.999Z
      - name: end_date
        in: query
        description: End date to filter the responses by
        required: false
        schema:
          type: string
          format: date-time
          example: 2010-01-01T06:00:00.999Z
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_200'
  /roles_api/employees/{employeeId}/account_admin:
    get:
      tags:
      - roles_api
      summary: Show the account admin assignment data for the employee
      description: Endpoint to determine whether a given employee has the account
        admin role assigned
      operationId: showAccountAdmin
      parameters:
      - name: employeeId
        in: path
        description: An aggregate id (UUID v4) that identifies a given employee
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/account_admin'
    put:
      tags:
      - roles_api
      summary: Update the account admin assignment data for the employee
      description: Endpoint to update account admin role assignment for a given employee
      operationId: updateAccountAdmin
      parameters:
      - name: employeeId
        in: path
        description: An aggregate id (UUID v4) that identifies a given employee
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/account_admin'
      responses:
        "204":
          description: Expected response to a valid request
  /roles_api/employees/{employeeId}/survey_creator:
    get:
      tags:
      - roles_api
      summary: Show the survey creator assignment data and scopes for the employee
      description: "Endpoint to determine whether a given employee has the survey\
        \ creator role assigned, and for which scopes"
      operationId: showSurveyCreator
      parameters:
      - name: employeeId
        in: path
        description: An aggregate id (UUID v4) that identifies a given employee
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/survey_creator'
    put:
      tags:
      - roles_api
      summary: Update the survey creator assignment data for the employee
      description: Endpoint to update survey creator role assignment for a given employee
      operationId: updateSurveyCreator
      parameters:
      - name: employeeId
        in: path
        description: An aggregate id (UUID v4) that identifies a given employee
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/update_survey_creator'
      responses:
        "204":
          description: Expected response to a valid request
  /roles_api/employees/{employeeId}/survey_data_analyst:
    get:
      tags:
      - roles_api
      summary: Show the survey data analyst assignment data for the employee
      description: Endpoint to determine whether a given employee has the survey data
        analyst role assigned
      operationId: showSurveyDataAnalyst
      parameters:
      - name: employeeId
        in: path
        description: An aggregate id (UUID v4) that identifies a given employee
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/survey_data_analyst'
    put:
      tags:
      - roles_api
      summary: Update the survey data analyst assignment data for the employee
      description: Endpoint to update survey data analyst role assignment for a given
        employee
      operationId: updateSurveyDataAnalyst
      parameters:
      - name: employeeId
        in: path
        description: An aggregate id (UUID v4) that identifies a given employee
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/survey_data_analyst'
      responses:
        "204":
          description: Expected response to a valid request
  /roles_api/employees/{employeeId}/survey_comment_replier:
    get:
      tags:
      - roles_api
      summary: Show the survey comment replier assignment data for the employee
      description: Endpoint to determine whether a given employee has the survey comment
        replier role assigned
      operationId: showSurveyCommentReplier
      parameters:
      - name: employeeId
        in: path
        description: An aggregate id (UUID v4) that identifies a given employee
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/survey_comment_replier'
    put:
      tags:
      - roles_api
      summary: Update the survey comment replier assignment data for the employee
      description: Endpoint to update survey comment replier role assignment for a
        given employee
      operationId: updateSurveyCommentReplier
      parameters:
      - name: employeeId
        in: path
        description: An aggregate id (UUID v4) that identifies a given employee
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/update_survey_comment_replier'
      responses:
        "204":
          description: Expected response to a valid request
  /roles_api/accounts/{accountId}/account_admins:
    get:
      tags:
      - roles_api
      summary: List the account admin role assignments for the account
      description: Endpoint to return the list of all account admins for the account
      operationId: indexAccountAdmins
      parameters:
      - name: accountId
        in: path
        description: An aggregate id (UUID v4) that identifies a given account
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/account_admins'
  /roles_api/accounts/{accountId}/survey_creators:
    get:
      tags:
      - roles_api
      summary: List the survey creator role assignments for the account
      description: Endpoint to return the list of all survey creators for the account
      operationId: indexSurveyCreators
      parameters:
      - name: accountId
        in: path
        description: An aggregate id (UUID v4) that identifies a given account
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/survey_creators'
  /roles_api/accounts/{accountId}/survey_data_analysts:
    get:
      tags:
      - roles_api
      summary: List the survey data analyst role assignments for the account
      description: Endpoint to return the list of all survey data analysts for the
        account
      operationId: indexSurveyDataAnalysts
      parameters:
      - name: accountId
        in: path
        description: An aggregate id (UUID v4) that identifies a given account
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/survey_data_analysts'
  /roles_api/accounts/{accountId}/survey_comment_repliers:
    get:
      tags:
      - roles_api
      summary: List the survey comment replier role assignments for the account
      description: Endpoint to return the list of all survey comment repliers for
        the account
      operationId: indexSurveyCommentRepliers
      parameters:
      - name: accountId
        in: path
        description: An aggregate id (UUID v4) that identifies a given account
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/survey_comment_repliers'
  /roles_api/accounts/{accountId}/departments:
    get:
      tags:
      - roles_api
      summary: List the current active departments for the account
      description: Endpoint to return the list of all active departments for the account
      operationId: indexAccountDepartments
      parameters:
      - name: accountId
        in: path
        description: An aggregate id (UUID v4) that identifies a given account
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/departments'
  /roles_api/permissions:
    get:
      tags:
      - roles_api
      summary: Query a set of permissions for the current user
      description: |
        This endpoint can be used to query the set of permissions for the current user.
        The user is inferred from the JWT, and the the account aggregate id and permissions are passed as query parameters.
        It returns a list of objects that contain the name of the permission and whether it is granted to that user.
        If this endpoint cannot determine the identity of the account or does not know the permission string, it will
        consider that permission to not be granted.
      operationId: showPermissions
      parameters:
      - name: account_id
        in: query
        description: An aggregate id (UUID v4) that identifies a given account
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      - name: "permissions[]"
        in: query
        description: An array of permissions to check
        required: false
        schema:
          type: array
          items:
            type: string
        example: administration.account.roles.administer
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/permissions'
  /roles_api/accounts/{accountId}/demographics:
    get:
      tags:
      - roles_api
      summary: List the demographics for the account
      description: Endpoint to return the list of all demographics for the account
      operationId: indexAccountDemographics
      parameters:
      - name: accountId
        in: path
        description: An aggregate id (UUID v4) that identifies a given account
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/demographics'
  /roles_api/accounts/{accountId}/demographics/{demographicId}/values:
    get:
      tags:
      - roles_api
      summary: List the values for the demographic
      description: "Endpoint to return the list of all demographic values for the\
        \ demographic. \nIt returns those values in a form suitable for rendering\
        \ as options in a HTML select input\n"
      operationId: indexAccountDemographicValues
      parameters:
      - name: accountId
        in: path
        description: An aggregate id (UUID v4) that identifies a given account
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      - name: demographicId
        in: path
        description: An aggregate id (UUID v4) that identifies a given demographic
        required: true
        schema:
          type: string
        example: dbb1c7c2-0238-4f4b-a318-829c1513327c
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/demographic_values'
  /surveys/{surveyId}/bulk_remove_grants:
    put:
      tags:
      - Bulk remove report grants
      summary: Bulk remove remove report grants
      description: |
        Endpoint to initiate the bulk remove grants on all reports associated to the specified survey.
        This endpoint triggers the BulkRemoveReportGrantsJob to run.
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      responses:
        "200":
          description: Successfully initiated the bulk remove grants process
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bulk_remove_grants'
        "404":
          description: Survey not found
  /surveys/{surveyId}/bulk_remove_grants/{aggregateId}:
    get:
      tags:
      - Bulk remove report grants
      summary: Get the progress of the bulk remove grants process
      description: |
        This endpoint shows the progress or status of the BulkRemoveReportGrantsJob that has been triggered from
        the `surveys/:survey_id/bulk_remove_grants` endpoint.
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: aggregateId
        in: path
        description: Aggregate ID for the bulk remove grants process
        required: true
        schema:
          type: string
          format: uuid
      responses:
        "200":
          description: Successfully fetched the progress
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/bulk_remove_grants_progress'
              examples:
                Running process:
                  $ref: '#/components/examples/runningExample'
                Completed process:
                  $ref: '#/components/examples/doneExample'
                Error in process:
                  $ref: '#/components/examples/errorExample'
        "404":
          description: Survey or process not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_404'
  /survey_capture_api/{surveyAggregateId}/response_for_code:
    get:
      tags:
      - Survey Capture Response for Code
      summary: Kiosk mode response for code
      description: Check user response on kiosk mode and return the response URL
      operationId: GetResponseForCode
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: kiosk_key
        in: query
        description: The kiosk key for the survey
        schema:
          type: string
        example: abcd123
      - name: code
        in: query
        description: The user unique identifier either an employee_id or the kiosk_code
        schema:
          type: string
        example: ******** or 7KHCAG39
      - name: locale
        in: query
        description: "The language to use to use when providing localisable data,\
          \ e.g. question label."
        required: true
        schema:
          type: string
          example: en
      requestBody:
        description: Body of the request must be json format.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/responseForCode'
        required: true
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseForCode'
        "403":
          description: Permission denied. Invalid Kiosk key.
        "404":
          description: Code/Response not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundBody'
        "422":
          description: Response submitted
        "500":
          description: Error response when an unhandled exception occurs
    post:
      tags:
      - Survey Capture Response for Code
      summary: Kiosk mode response for code
      description: Check user response on kiosk mode and return the response URL
      operationId: PostResponseForCode
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: kiosk_key
        in: query
        description: The kiosk key for the survey
        schema:
          type: string
        example: abcd123
      - name: code
        in: query
        description: The user unique identifier either an employee_id or the kiosk_code
        schema:
          type: string
        example: ******** or 7KHCAG39
      - name: locale
        in: query
        description: "The language to use to use when providing localisable data,\
          \ e.g. question label."
        required: true
        schema:
          type: string
          example: en
      requestBody:
        description: Body of the request must be json format.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/responseForCode'
        required: true
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/responseForCode'
        "403":
          description: Permission denied. Invalid Kiosk key.
        "404":
          description: Code/Response not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundBody'
        "422":
          description: Response submitted
        "500":
          description: Error response when an unhandled exception occurs
  /survey_capture_api/{surveyAggregateId}/kiosk_page_data:
    get:
      tags:
      - Survey Capture Kiosk Data
      summary: Kiosk page data
      description: Gets the data required to render the kiosk page
      operationId: getKioskPageData
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: kiosk_key
        in: query
        description: The kiosk key for the survey
        schema:
          type: string
        example: abcd123
      - name: locale
        in: query
        description: "The language to use to use when providing localisable data,\
          \ e.g. question label."
        required: true
        schema:
          type: string
          example: en
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/kioskPageData'
        "403":
          description: Permission denied. Invalid Kiosk key.
        "404":
          description: Survey not found
        "500":
          description: Error response when an unhandled exception occurs
  /surveys/{surveyAggregateId}/survey_designer/v1/survey_metadata:
    get:
      tags:
      - Survey Designer Survey Metadata
      summary: Survey Metadata
      description: Gets the bootstrap data required for the survey designer UI
      operationId: getSurveyMetadata
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/surveyMetadata'
        "403":
          description: Permission denied. Invalid survey id.
        "404":
          description: Survey not found
        "500":
          description: Error response when an unhandled exception occurs
  /surveys/{surveyAggregateId}/survey_designer/rename_section:
    post:
      tags:
      - Survey Designer Rename Section
      summary: Rename a section in survey designer
      description: Rename a given section for a given survey
      operationId: postRenameSection
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      requestBody:
        description: Body of the request must be json format.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/renameSection'
        required: true
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/renameSection'
        "403":
          description: Permission denied.
        "422":
          description: Unprocessable Content
        "500":
          description: Error response when an unhandled exception occurs
  /surveys/{surveyAggregateId}/survey_designer/survey_details:
    get:
      tags:
      - Survey Designer Survey Details
      summary: Get survey details
      description: Retrieves the details of a survey for the survey designer.
      operationId: getSurveyDetails
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      responses:
        "200":
          description: Survey details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/surveyDetails'
        "403":
          description: Permission denied. Invalid survey id.
        "404":
          description: Survey not found
        "500":
          description: Internal server error
  /survey_participants_import_api/{surveyId}/survey_data:
    get:
      tags:
      - Survey Participants API survey data
      summary: Survey Data
      description: Gets the survey name for a given survey aggregateID
      operationId: getSurveyData
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: locale
        in: query
        description: "The language to use to use when providing localisable data,\
          \ e.g. question label."
        required: true
        schema:
          type: string
          example: en
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/surveyData'
        "404":
          description: Survey not found
        "403":
          description: Auth issue. User not a superuser.
        "500":
          description: Error response when an unhandled exception occurs
  /surveys/{surveyMongoId}/participant_import/{participantImportId}:
    get:
      tags:
      - Survey Participants API validation status
      summary: Check import validation status
      description: Checks the validation status for the import
      operationId: checkImportValidationStatus
      parameters:
      - name: surveyMongoId
        in: path
        description: A MongoID that identifies a given survey
        required: true
        schema:
          type: string
        example: 663ad28514620849447123c4
      - name: participantImportId
        in: path
        description: A MongoID that identifies a given participant import
        required: true
        schema:
          type: string
        example: 663ad28514620849447123c4
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/importValidationStatus'
        "404":
          description: Survey not found
        "403":
          description: Auth issue. User not a superuser.
        "500":
          description: Error response when an unhandled exception occurs
    put:
      tags:
      - Survey Participants import start
      description: Starts the participant import
      operationId: StartParticipantImport
      parameters:
      - name: surveyMongoId
        in: path
        description: A MongoID that identifies a given survey
        required: true
        schema:
          type: string
        example: 663ad28514620849447123c4
      - name: participantImportId
        in: path
        description: A MongoID that identifies a given participant import
        required: true
        schema:
          type: string
        example: 663ad28514620849447123c4
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/importStarted'
        "409":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/importAlreadyInProgress'
        "404":
          description: Survey not found
        "403":
          description: Auth issue. User not a superuser.
        "500":
          description: Error response when an unhandled exception occurs
  /surveys/{surveyMongoId}/participant_import:
    post:
      tags:
      - Survey Participants API upload and validate file
      summary: Upload and validate participant file
      description: Uploads and validates the file
      operationId: uploadAndValidateFile
      parameters:
      - name: surveyMongoId
        in: path
        description: A MongoID that identifies a given survey
        required: true
        schema:
          type: string
        example: 663ad28514620849447123c4
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/surveyMongoId_participant_import_body'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/validateImportSuccess'
        "404":
          description: Survey not found
        "403":
          description: Auth issue. User not a superuser.
        "500":
          description: Error response when an unhandled exception occurs
        "409":
          description: This happens when import or validation is already in progress
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/validateImportInProgress'
        "422":
          description: This happens when there is an error (empty file or file is
            not a csv)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/validateImportError'
  /surveys/{surveyMongoId}/participant_import_v2:
    post:
      tags:
      - Survey Participants API upload and validate file
      summary: Upload and validate participant file
      description: Uploads and validates the file
      operationId: uploadAndValidateFile
      parameters:
      - name: surveyMongoId
        in: path
        description: A MongoID that identifies a given survey
        required: true
        schema:
          type: string
        example: 663ad28514620849447123c4
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/surveyMongoId_participant_import_v2_body'
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/validateImportSuccess'
        "404":
          description: Survey not found
        "403":
          description: Auth issue. User not a survey admin.
        "500":
          description: Error response when an unhandled exception occurs
        "422":
          description: This happens when there is an error (empty file or file is
            not a csv)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/validateImportErrorV2'
  /surveys/{surveyAggregateId}/survey_launch_settings:
    get:
      tags:
      - Survey Launch Settings
      summary: Survey launch settings
      description: Gets the launch settings for a survey
      operationId: getSurveyLaunchSettings
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/surveyLaunchSettings'
        "401":
          description: User is not logged in.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/userNotLoggedIn'
        "403":
          description: User does not have the permission to access this endpoint.
        "500":
          description: Error response when an unhandled exception occurs
  /surveys/{surveyAggregateId}/survey_scheduler/schedule:
    post:
      tags:
      - Survey Launch Scheduler
      summary: Survey launch scheduler
      description: "Schedules the survey launch, first reminder, final reminder and\
        \ close datetimes."
      operationId: surveyScheduler
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/launchSchedulerBody'
      responses:
        "201":
          description: Command created
          content:
            application/json:
              schema:
                type: string
                example: ok
        "400":
          description: Scheduling error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_400'
        "401":
          description: User is not logged in.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/userNotLoggedIn'
        "403":
          description: User does not have the permission to access this endpoint.
        "409":
          description: Event already actioned
        "422":
          description: Command parameter validation failed
        "500":
          description: Generic exception
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_500'
  /surveys/{surveyAggregateId}/survey_scheduler/cancel_launch:
    post:
      tags:
      - Survey Launch Scheduler
      summary: Survey launch scheduler
      description: "Schedules the survey launch, first reminder, final reminder and\
        \ close datetimes."
      operationId: surveyCancelSchedule
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/survey_scheduler_cancel_launch_body'
      responses:
        "201":
          description: Command created
          content:
            application/json:
              schema:
                type: string
                example: ok
        "400":
          description: Scheduling error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_400_1'
        "401":
          description: User is not logged in.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/userNotLoggedIn'
        "403":
          description: User does not have the permission to access this endpoint.
        "409":
          description: Event already actioned
        "422":
          description: Command parameter validation failed
        "500":
          description: Generic exception
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_500'
  /surveys/{surveyAggregateId}/survey_scheduler/close:
    post:
      tags:
      - Survey Launch closer
      summary: Survey closer
      description: Closes the survey
      operationId: surveyClose
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/survey_scheduler_close_body'
      responses:
        "201":
          description: Command created
          content:
            application/json:
              schema:
                type: string
                example: ok
        "400":
          description: Scheduling error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_400_1'
        "401":
          description: User is not logged in.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/userNotLoggedIn'
        "403":
          description: User does not have the permission to access this endpoint.
        "409":
          description: Event already actioned
        "422":
          description: Command parameter validation failed
        "500":
          description: Generic exception
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_500'
  /surveys/{surveyAggregateId}/survey_scheduler/reopen:
    post:
      tags:
      - Survey Launch survey reopener
      summary: Survey reopener
      description: Reopen the survey
      operationId: surveyReopen
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/survey_scheduler_reopen_body'
      responses:
        "201":
          description: Command created
          content:
            application/json:
              schema:
                type: string
                example: ok
        "400":
          description: Scheduling error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_400_1'
        "401":
          description: User is not logged in.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/userNotLoggedIn'
        "403":
          description: User does not have the permission to access this endpoint.
        "409":
          description: Event already actioned
        "422":
          description: Command parameter validation failed
        "500":
          description: Generic exception
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_500'
  /surveys/{surveyAggregateId}/survey_scheduler/resend_reminder:
    post:
      tags:
      - Survey Launch reminder resender
      summary: Resend reminder
      description: Resend the survey reminder
      operationId: resendReminder
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/survey_scheduler_resend_reminder_body'
      responses:
        "201":
          description: Command created
          content:
            application/json:
              schema:
                type: string
                example: ok
        "400":
          description: Scheduling error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_400_1'
        "401":
          description: User is not logged in.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/userNotLoggedIn'
        "403":
          description: User does not have the permission to access this endpoint.
        "409":
          description: Event already actioned
        "422":
          description: Command parameter validation failed
        "500":
          description: Generic exception
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_500'
  /surveys/{surveyId}/reports/{reportId}/trend/api/graphql:
    post:
      tags:
      - Trend
      summary: Execute GraphQL queries for comparison and survey trends
      description: "Endpoint to execute GraphQL queries to retrieve trend-related\
        \ data, including comparisons and survey trends"
      operationId: trendGraphQL
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: reportId
        in: path
        description: The active record instance/object's mongo id of the report for
          a given survey
        required: true
        schema:
          type: string
      - name: a
        in: query
        description: "This contains the anchor filters - un-removable demographic\
          \ filters imposed as part of a report. There can be single or multiple anchor\
          \ filters present.\n\nThe comma separated tuples of stq id - select option\
          \ id pairs, where the stq represents the demographic, and the the select\
          \ option represents the demographic value.\n\nOptionally, in certain administrator\
          \ scenarios, this anchor can be sent the special value \"ALL_RESULTS\".\n\
          \nAll supplied ids should be mongo style bson ids. "
        required: false
        schema:
          type: string
          example: "demographicId1-optionId1,demographicId2-optionId2"
      - name: comparison
        in: query
        description: |-
          Indicate a target to compare survey results against. This could be the overall survey, a benchmark, or another survey.

          This parameter controls what is returned as part of comparisonDeltaScore

          The value is a magical string, which indicates the type and source of comparison. Some examples:

          .overall - compare to the overall survey

          .auto_trend.<survey id> - compare to another survey

          <a summary bson id> - compare against a survey or benchmark.
        required: false
        schema:
          type: string
          example: .overall
      - name: filters
        in: query
        description: |-
          A comma-separated list of dash-seperated tuples, with each tuple being a stq id - select option id pair, where the stq represents the demographic, and the the select option represents the demographic value.

          In situations where there are multiple values selected for a particular demographic, multiple tuples with the same demographic id should be provided.

          All supplied ids should be mongo style bson ids.
        required: false
        schema:
          type: string
          example: "demographicId1-optionId1,demographicId1-optionId2,demographicId3-optionId7"
      - name: from
        in: query
        description: The start date to filter responses by. The Date is in the format
          "YYYY-M-D"
        required: false
        schema:
          type: string
          example: 2023-2-1
      - name: locale
        in: query
        description: "The language to use to use when providing localisable data,\
          \ e.g. question label."
        required: false
        schema:
          type: string
          example: en
      - name: preview
        in: query
        description: "Supply fake response data, used for testing reports before response\
          \ data is available."
        required: false
        schema:
          type: boolean
      - name: selected_leader
        in: query
        description: The select option ID of the selected leader
        required: false
        schema:
          type: string
          example: 655c2a5345e41d0051254857-64912d2b391fd9000db7fba7
      - name: to
        in: query
        description: End date to filter the responses by. The Date is in the format
          "YYYY-M-D"
        required: false
        schema:
          type: string
          example: 2023-2-24
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/api_graphql_body'
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/inline_response_200_1'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey or report.
        "404":
          description: "Not Found. The requested survey, report, or comparison does\
            \ not exist."
        "500":
          description: Internal server error.
  /survey_reporting_api/v1/surveys/{surveyId}/reports/{reportId}/report-data/factor-insight/{factorId}:
    get:
      tags:
      - Survey Reporting API Factor Insight
      summary: Get factor insight
      description: Endpoint to get the factor insight for a given factor in the survey
        report
      operationId: factorInsight
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: reportId
        in: path
        description: The active record instance/object's mongo id of the report for
          a given survey
        required: true
        schema:
          type: string
      - name: factorId
        in: path
        description: The active record instance/object's mongo id of the factor
        required: true
        schema:
          type: string
      - name: a
        in: query
        description: "This contains the anchor filters - un-removable demographic\
          \ filters imposed as part of a report. There can be single or multiple anchor\
          \ filters present.\n\nThe comma separated tuples of stq id - select option\
          \ id pairs, where the stq represents the demographic, and the the select\
          \ option represents the demographic value.\n\nOptionally, in certain administrator\
          \ scenarios, this anchor can be sent the special value \"ALL_RESULTS\".\n\
          \nAll supplied ids should be mongo style bson ids. "
        required: false
        schema:
          type: string
          example: "demographicId1-optionId1,demographicId2-optionId2"
      - name: comparison
        in: query
        description: |-
          Indicate a target to compare survey results against. This could be the overall survey, a benchmark, or another survey.

          This parameter controls what is returned as part of comparisonDeltaScore

          The value is a magical string, which indicates the type and source of comparison. Some examples:

          .overall - compare to the overall survey

          .auto_trend.<survey id> - compare to another survey

          <a summary bson id> - compare against a survey or benchmark.
        required: false
        schema:
          type: string
          example: .overall
      - name: filters
        in: query
        description: |-
          A comma-separated list of dash-seperated tuples, with each tuple being a stq id - select option id pair, where the stq represents the demographic, and the the select option represents the demographic value.

          In situations where there are multiple values selected for a particular demographic, multiple tuples with the same demographic id should be provided.

          All supplied ids should be mongo style bson ids.
        required: false
        schema:
          type: string
          example: "demographicId1-optionId1,demographicId1-optionId2,demographicId3-optionId7"
      - name: from
        in: query
        description: The start date to filter responses by. The Date is in the format
          "YYYY-M-D"
        required: false
        schema:
          type: string
          example: 2023-2-1
      - name: locale
        in: query
        description: "The language to use to use when providing localisable data,\
          \ e.g. question label."
        required: false
        schema:
          type: string
          example: en
      - name: selected_leader
        in: query
        description: The select option ID of the selected leader
        required: false
        schema:
          type: string
          example: 655c2a5345e41d0051254857-64912d2b391fd9000db7fba7
      - name: status
        in: query
        description: The status of the survey
        required: false
        schema:
          type: string
          example: closed
      - name: term
        in: query
        description: The search term used to filter the survey responses
        required: false
        schema:
          type: string
      - name: to
        in: query
        description: End date to filter the responses by. The Date is in the format
          "YYYY-M-D"
        required: false
        schema:
          type: string
          example: 2023-2-24
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_200_2'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey or report.
        "404":
          description: "Not Found. The requested survey, report, or factor does not\
            \ exist."
        "500":
          description: Internal server error.
  /survey_reporting_api/v1/surveys/{surveyId}/reports/{reportId}/report-data/question-insight/{questionId}:
    get:
      tags:
      - Survey Reporting API Question Insight
      summary: Get question insight
      description: Endpoint to get the question insight for a given question in the
        survey report
      operationId: questionInsight
      parameters:
      - name: surveyId
        in: path
        description: The active record instance/object's mongo id for the survey
        required: true
        schema:
          type: string
      - name: reportId
        in: path
        description: The active record instance/object's mongo id of the report for
          a given survey
        required: true
        schema:
          type: string
      - name: questionId
        in: path
        description: The active record instance/object's mongo id of the question
        required: true
        schema:
          type: string
      - name: a
        in: query
        description: "This contains the anchor filters - un-removable demographic\
          \ filters imposed as part of a report. There can be single or multiple anchor\
          \ filters present.\n\nThe comma separated tuples of stq id - select option\
          \ id pairs, where the stq represents the demographic, and the the select\
          \ option represents the demographic value.\n\nOptionally, in certain administrator\
          \ scenarios, this anchor can be sent the special value \"ALL_RESULTS\".\n\
          \nAll supplied ids should be mongo style bson ids. "
        required: false
        schema:
          type: string
          example: "demographicId1-optionId1,demographicId2-optionId2"
      - name: comparison
        in: query
        description: |-
          Indicate a target to compare survey results against. This could be the overall survey, a benchmark, or another survey.

          This parameter controls what is returned as part of comparisonDeltaScore

          The value is a magical string, which indicates the type and source of comparison. Some examples:

          .overall - compare to the overall survey

          .auto_trend.<survey id> - compare to another survey

          <a summary bson id> - compare against a survey or benchmark.
        required: false
        schema:
          type: string
          example: .overall
      - name: filters
        in: query
        description: |-
          A comma-separated list of dash-seperated tuples, with each tuple being a stq id - select option id pair, where the stq represents the demographic, and the the select option represents the demographic value.

          In situations where there are multiple values selected for a particular demographic, multiple tuples with the same demographic id should be provided.

          All supplied ids should be mongo style bson ids.
        required: false
        schema:
          type: string
          example: "demographicId1-optionId1,demographicId1-optionId2,demographicId3-optionId7"
      - name: from
        in: query
        description: The start date to filter responses by. The Date is in the format
          "YYYY-M-D"
        required: false
        schema:
          type: string
          example: 2023-2-1
      - name: locale
        in: query
        description: "The language to use to use when providing localisable data,\
          \ e.g. question label."
        required: false
        schema:
          type: string
          example: en
      - name: selected_leader
        in: query
        description: The select option ID of the selected leader
        required: false
        schema:
          type: string
          example: 655c2a5345e41d0051254857-64912d2b391fd9000db7fba7
      - name: status
        in: query
        description: The status of the survey
        required: false
        schema:
          type: string
          example: closed
      - name: term
        in: query
        description: The search term used to filter the survey responses
        required: false
        schema:
          type: string
      - name: to
        in: query
        description: End date to filter the responses by. The Date is in the format
          "YYYY-M-D"
        required: false
        schema:
          type: string
          example: 2023-2-24
      responses:
        "200":
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_200_3'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey or report.
        "404":
          description: "Not Found. The requested survey, report, or question does\
            \ not exist."
        "500":
          description: Internal server error.
  /multi_account_surveying_api/v1/org_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}:
    get:
      tags:
      - Multi Account Surveying
      summary: Get org survey data
      description: Retrieves metadata for parent organisation survey.
      operationId: getOrgSurveyData
      parameters:
      - name: accountAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given account
        required: true
        schema:
          type: string
          format: uuid
        example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: X-User-Id
        in: header
        description: The ID of the user making the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 1f492219-3915-42cf-9cef-ca5e930c97cb
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/orgSurveyData'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey or report.
        "404":
          description: "Not Found. The requested survey, report, or question does\
            \ not exist."
        "500":
          description: Internal server error.
  /multi_account_surveying_api/v1/surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}:
    get:
      tags:
      - Multi Account Surveying
      summary: Get survey structure
      description: Retrieves metadata describing a survey.
      operationId: getSurveyStructure
      parameters:
      - name: accountAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given account
        required: true
        schema:
          type: string
          format: uuid
        example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: X-User-Id
        in: header
        description: The ID of the user making the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 1f492219-3915-42cf-9cef-ca5e930c97cb
      responses:
        "200":
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/surveyStructure'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey or report.
        "404":
          description: "Not Found. The requested survey, report, or question does\
            \ not exist."
        "500":
          description: Internal server error.
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/upsert_survey:
    post:
      tags:
      - Multi Account Surveying
      summary: Upsert subsidiary survey
      description: Creates a new subsidiary survey for the specified account or updates
        an existing one.
      operationId: upsertSubsidiarySurvey
      parameters:
      - name: accountAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given account
        required: true
        schema:
          type: string
          format: uuid
        example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
      - name: X-User-Id
        in: header
        description: The ID of the user making the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 1f492219-3915-42cf-9cef-ca5e930c97cb
      - name: X-Correlation-Id
        in: header
        description: Correlation ID of the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 2f492219-3915-42cf-9cef-ca5e930c97cb
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/accountAggregateId_upsert_survey_body'
        required: true
      responses:
        "201":
          description: Created or updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_201'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey.
        "404":
          description: Not Found. The requested survey does not exist.
        "500":
          description: Internal server error.
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_section:
    post:
      tags:
      - Multi Account Surveying
      summary: Upsert section
      description: Creates a new section for the specified subsidiary survey or updates
        an existing one.
      operationId: upsertSection
      parameters:
      - name: accountAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given account
        required: true
        schema:
          type: string
          format: uuid
        example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: X-User-Id
        in: header
        description: The ID of the user making the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 1f492219-3915-42cf-9cef-ca5e930c97cb
      - name: X-Correlation-Id
        in: header
        description: Correlation ID of the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 2f492219-3915-42cf-9cef-ca5e930c97cb
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/surveyAggregateId_upsert_section_body'
        required: true
      responses:
        "201":
          description: Created or updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_201_1'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey.
        "404":
          description: Not Found. The requested survey does not exist.
        "500":
          description: Internal server error.
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/delete_section:
    post:
      tags:
      - Multi Account Surveying
      summary: Delete section
      description: Deletes a section from the specified subsidiary survey.
      operationId: deleteSection
      parameters:
      - name: accountAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given account
        required: true
        schema:
          type: string
          format: uuid
        example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: X-User-Id
        in: header
        description: The ID of the user making the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 1f492219-3915-42cf-9cef-ca5e930c97cb
      - name: X-Correlation-Id
        in: header
        description: Correlation ID of the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 2f492219-3915-42cf-9cef-ca5e930c97cb
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/surveyAggregateId_delete_section_body'
        required: true
      responses:
        "202":
          description: Successfully created delete section event.
        "204":
          description: Section has already been deleted.
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey.
        "404":
          description: Not Found. The requested section does not exist.
        "500":
          description: Internal server error.
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_factor:
    post:
      tags:
      - Multi Account Surveying
      summary: Upsert factor
      description: Creates a new factor for the specified subsidiary survey or updates
        an existing one.
      operationId: upsertFactor
      parameters:
      - name: accountAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given account
        required: true
        schema:
          type: string
          format: uuid
        example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: X-User-Id
        in: header
        description: The ID of the user making the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 1f492219-3915-42cf-9cef-ca5e930c97cb
      - name: X-Correlation-Id
        in: header
        description: Correlation ID of the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 2f492219-3915-42cf-9cef-ca5e930c97cb
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/surveyAggregateId_upsert_factor_body'
            example:
              factorSpecifications:
                factorId: 4b492219-3915-42cf-9cef-ca5e930c97cb
                name: Overall Satisfaction
                locale: en
                isIndexFactor: true
                isKeyFactor: true
        required: true
      responses:
        "201":
          description: Created or updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_201_2'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey.
        "404":
          description: Not Found. The requested survey does not exist.
        "500":
          description: Internal server error.
  ? /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/delete_survey_question
  : post:
      tags:
      - Multi Account Surveying
      summary: Delete survey question
      description: Deletes a question from the specified subsidiary survey.
      operationId: deleteSurveyQuestion
      parameters:
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/surveyAggregateId_delete_survey_question_body'
        required: true
      responses:
        "202":
          description: Successfully created delete question event.
        "204":
          description: Question has already been deleted.
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey.
        "404":
          description: Not Found. The requested question does not exist.
        "500":
          description: Internal server error.
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_question:
    post:
      tags:
      - Multi Account Surveying
      summary: Upsert question
      description: Creates a new question for the specified subsidiary survey or updates
        an existing one.
      operationId: upsertQuestion
      parameters:
      - name: accountAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given account
        required: true
        schema:
          type: string
          format: uuid
        example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: X-User-Id
        in: header
        description: The ID of the user making the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 1f492219-3915-42cf-9cef-ca5e930c97cb
      - name: X-Correlation-Id
        in: header
        description: Correlation ID of the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 2f492219-3915-42cf-9cef-ca5e930c97cb
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/surveyAggregateId_upsert_question_body'
        required: true
      responses:
        "201":
          description: Created or updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_201_3'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey.
        "404":
          description: Not Found. The requested survey does not exist.
        "500":
          description: Internal server error.
  ? /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_question_display_condition
  : post:
      tags:
      - Multi Account Surveying
      summary: Upsert question display condition
      description: Create or update a question display condition for the specified
        subsidiary survey.
      operationId: upsertQuestionDisplayCondition
      parameters:
      - name: accountAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given account
        required: true
        schema:
          type: string
          format: uuid
        example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
      - name: surveyAggregateId
        in: path
        description: An aggregateId (UUID) that identifies a given survey
        required: true
        schema:
          type: string
          format: uuid
        example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
      - name: X-User-Id
        in: header
        description: The ID of the user making the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 1f492219-3915-42cf-9cef-ca5e930c97cb
      - name: X-Correlation-Id
        in: header
        description: Correlation ID of the request.
        required: true
        schema:
          type: string
          format: uuid
          example: 2f492219-3915-42cf-9cef-ca5e930c97cb
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/surveyAggregateId_upsert_question_display_condition_body'
        required: true
      responses:
        "201":
          description: Created or updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/inline_response_201_4'
        "400":
          description: Bad Request. The client provided invalid input.
        "401":
          description: Unauthorized. The user is not authenticated or the authentication
            token is invalid.
        "403":
          description: Permission denied. User cannot access the survey.
        "404":
          description: Not Found. The requested survey does not exist.
        "500":
          description: Internal server error.
components:
  schemas:
    AppDataResponse:
      required:
      - account
      - active_integration
      - current_user
      type: object
      properties:
        account:
          $ref: '#/components/schemas/AppDataResponse_account'
        active_integration:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/SftpIntegration'
          - $ref: '#/components/schemas/HrisIntegration'
        current_user:
          $ref: '#/components/schemas/AppDataResponse_current_user'
    SetIntegrationRequest:
      required:
      - adaptor
      - notification_receiver_id
      type: object
      properties:
        adaptor:
          type: string
        notification_receiver_id:
          type: string
        credentials:
          $ref: '#/components/schemas/SetIntegrationRequest_credentials'
        publicKey:
          type: string
    performanceRatingCycles:
      type: array
      items:
        $ref: '#/components/schemas/performanceRatingCycles_inner'
    Question:
      type: object
      properties:
        label:
          type: string
          example: I have the opportunity to develop skills relevant to my career
            interests
        factorNames:
          uniqueItems: true
          type: array
          example:
          - Career Enablement
          items:
            type: string
        hasSufficientAnswers:
          type: boolean
          description: "It's possible for an overall survey to have enough responses\
            \ to meet minimum privacy rules, but \ndue a question being optional,\
            \ a particular question may have an insufficient number of answers to\
            \ be shown.\nIf this is the case, this boolean will return false, and\
            \ scores & comparison delta won't be returned for the impacted question."
          example: true
        correlation:
          $ref: '#/components/schemas/Correlation'
        scores:
          $ref: '#/components/schemas/Scores'
        comparisonDeltaScore:
          maximum: 100
          minimum: -100
          type: number
          description: "How much this score differs from the favorable score of the\
            \ currently selected comparison, if any."
          format: integer
          nullable: true
          example: 4
        commentCount:
          type: number
          description: number of comments associated with the question. A null result
            indicates comments are disabled.
          format: integer
          nullable: true
          example: 12
    index:
      type: object
      properties:
        jobs:
          type: array
          items:
            $ref: '#/components/schemas/show'
    not_found:
      type: object
      properties:
        error:
          type: string
          example: Survey not found
    create:
      required:
      - validateReportSharingPermissionsFileJobId
      type: object
      properties:
        validateReportSharingPermissionsFileJobId:
          type: string
          example: 6435f6680b79165440bf3890
    create_1:
      required:
      - backgroundJobId
      type: object
      properties:
        backgroundJobId:
          type: string
          example: 6435f6680b79165440bf3890
    bad_request:
      type: object
      properties:
        error:
          type: string
          example: Invalid background job Id
    update:
      required:
      - dismissed
      type: object
      properties:
        dismissed:
          type: boolean
          example: true
    show:
      required:
      - completedAt
      - id
      - isDismissed
      - progress
      - status
      - validationResults
      type: object
      properties:
        id:
          type: string
          example: 64631bfe51be470039fcf434
        isDismissed:
          type: boolean
          example: true
        status:
          type: string
          description: "returns one of the following states 'queued', 'running', 'done'\
            \ or 'error'"
          example: done
        progress:
          type: integer
          example: 100
        completedAt:
          type: string
          format: date-time
          example: 2023-04-14T04:33:26.831Z
        validationResults:
          $ref: '#/components/schemas/validationResults'
    new:
      required:
      - surveyName
      type: object
      properties:
        surveyName:
          type: string
          example: Engagement Survey 1999
    show_1:
      required:
      - progress
      - status
      type: object
      properties:
        status:
          type: string
          description: "returns one of the following states 'queued', 'running', 'done'\
            \ or 'error'"
          example: done
        progress:
          type: integer
          example: 100
        validationResults:
          $ref: '#/components/schemas/validationResults_1'
    ReportResponses:
      type: object
      properties:
        survey:
          $ref: '#/components/schemas/ReportResponses_survey'
        questions:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ReportResponses_questions'
        responses:
          type: array
          items:
            $ref: '#/components/schemas/ReportResponses_responses'
        next_cursor:
          type: string
          description: An id pointing to the next page of results
          nullable: true
          example: NTg3OWJlNWI2MjVmMTUwMDdlMDAxYmY3
    Report360Responses:
      type: object
      properties:
        survey:
          $ref: '#/components/schemas/ReportResponses_survey'
        questions:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ReportResponses_questions'
        responses:
          type: array
          items:
            $ref: '#/components/schemas/Report360Responses_responses'
        next_cursor:
          type: string
          description: An id pointing to the next page of results
          nullable: true
          example: NTg3OWJlNWI2MjVmMTUwMDdlMDAxYmY3
    account_admin:
      type: object
      properties:
        accountAdmin:
          $ref: '#/components/schemas/account_admin_accountAdmin'
    survey_creator:
      type: object
      properties:
        surveyCreator:
          $ref: '#/components/schemas/survey_creator_surveyCreator'
    update_survey_creator:
      type: object
      properties:
        surveyCreator:
          $ref: '#/components/schemas/update_survey_creator_surveyCreator'
    survey_data_analyst:
      type: object
      properties:
        surveyDataAnalyst:
          $ref: '#/components/schemas/survey_data_analyst_surveyDataAnalyst'
    survey_comment_replier:
      type: object
      properties:
        surveyCommentReplier:
          $ref: '#/components/schemas/survey_comment_replier_surveyCommentReplier'
    update_survey_comment_replier:
      type: object
      properties:
        surveyCommentReplier:
          $ref: '#/components/schemas/update_survey_comment_replier_surveyCommentReplier'
    account_admins:
      type: object
      properties:
        accountAdmins:
          type: array
          description: An array of account admins with employee information
          items:
            $ref: '#/components/schemas/account_admins_accountAdmins'
    survey_creators:
      type: object
      properties:
        surveyCreators:
          type: array
          description: An array of survey creators with employee information
          items:
            $ref: '#/components/schemas/survey_creators_surveyCreators'
    survey_data_analysts:
      type: object
      properties:
        surveyDataAnalysts:
          type: array
          description: An array of survey data analysts with employee information
          items:
            $ref: '#/components/schemas/account_admins_accountAdmins'
    survey_comment_repliers:
      type: object
      properties:
        surveyCommentRepliers:
          type: array
          description: An array of survey comment repliers with employee information
          items:
            $ref: '#/components/schemas/account_admins_accountAdmins'
    departments:
      type: object
      properties:
        departments:
          type: array
          description: An array of departments
          items:
            $ref: '#/components/schemas/departments_departments'
    permissions:
      required:
      - permissions_granted
      type: object
      properties:
        permissions_granted:
          type: array
          description: |
            An array with the statuses of the permissions that were in the query parameters,
            in the context of the account whose aggregate id was also in the query parameters.
          items:
            $ref: '#/components/schemas/permissions_permissions_granted'
    demographics:
      type: object
      properties:
        demographics:
          type: array
          description: An array of demographics
          items:
            $ref: '#/components/schemas/demographics_demographics'
    demographic_values:
      type: object
      properties:
        demographic_values:
          type: array
          description: An array of demographic values
          items:
            $ref: '#/components/schemas/demographic_values_demographic_values'
    bulk_remove_grants:
      type: object
      properties:
        bulk_remove_grants_process_id:
          type: string
          format: uuid
      example:
        bulk_remove_grants_process_id: 60eccfbc-463d-4a3c-ac98-fd3ce044f408
    bulk_remove_grants_progress:
      required:
      - status
      type: object
      properties:
        status:
          type: string
          enum:
          - running
          - done
          - error
        progress:
          maximum: 100
          minimum: 0
          type: integer
          format: int32
    responseForCode:
      type: object
      properties:
        responseURL:
          type: string
          example: http://localhost:3000/responses/response_id?secret=secret
    notFoundBody:
      type: object
      properties:
        errorType:
          type: string
          enum:
          - invalid_code
          - response_not_found
          - null
    kioskPageData:
      type: object
      properties:
        supportedLocales:
          type: array
          example:
          - en
          - de
          items:
            type: string
        surveyName:
          type: string
          example: Hooli's Engagement survey
        accountName:
          type: string
          example: Hooli
        logo:
          type: string
          example: http://www.google.com
        surveyStatus:
          type: string
          example: design
        kioskMessage:
          type: string
          example: "[{\"type\":\"paragraph\",\"content\":[{\"text\":\"Some text\"\
            }]}]"
        inputLabel:
          type: string
          example: Enter your code
    surveyMetadata:
      type: object
      properties:
        logo_url:
          type: string
          example: http://www.google.com
        supported_locales:
          type: array
          example:
          - en
          - fr
          - es
          items:
            type: string
    renameSection:
      type: object
      properties:
        aggregate_id:
          type: string
          description: An aggregateId (UUID) that identifies a given survey capture
            layout
          format: uuid
          example: fd9da066-d6e4-45bb-9aed-16500cd157bd
        locale:
          type: string
          example: en
        name:
          type: string
          example: Section name
        section_id:
          type: string
          format: uuid
          example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
    surveyDetails:
      required:
      - captureLayout
      - likert_color_schema
      - profile
      - survey
      type: object
      properties:
        survey:
          $ref: '#/components/schemas/surveyDetails_survey'
        likert_color_schema:
          type: string
          example: classical
          enum:
          - classical
          - blue
        captureLayout:
          $ref: '#/components/schemas/captureLayout'
        profile:
          $ref: '#/components/schemas/profile'
    surveyData:
      type: object
      properties:
        surveyName:
          type: string
          example: Hooli's Engagement survey
        surveyStatus:
          type: string
          example: design
          enum:
          - design
          - active
          - closed
    importValidationStatus:
      type: object
      properties:
        analyzingStep:
          $ref: '#/components/schemas/importValidationStatus_analyzingStep'
        applyingStep:
          $ref: '#/components/schemas/importValidationStatus_applyingStep'
        participantImportId:
          type: string
          example: 663ad57d14620849447123c7
    importStarted:
      type: object
      properties:
        filePath:
          type: string
          example: http://localhost:3000/surveys/57145ed8f4761aff35000e9c/participant_import/663afb1814620849447123dc
        errors:
          type: array
          example: []
          items:
            type: string
    importAlreadyInProgress:
      type: object
      properties:
        message:
          type: string
          example: Import in progress
        status:
          type: number
          example: 409
    validateImportSuccess:
      type: object
      properties:
        filePath:
          type: string
          example: http://localhost:3000/surveys/57145ed8f4761aff35000e9c/participant_import/663afb1814620849447123dc
        errors:
          type: array
          example: []
          items:
            type: string
    validateImportInProgress:
      type: object
      properties:
        message:
          type: string
          example: Import in progress
        status:
          type: number
          example: 409
    validateImportError:
      type: object
      properties:
        message:
          type: string
          example: empty.file
        status:
          type: number
          example: 422
    validateImportErrorV2:
      type: object
      properties:
        error_type:
          type: string
          example: invalid_import_file_headers
          enum:
          - invalid_import_file_headers
          - unsupported_survey_type
          - import_already_in_progress
          - import_file_is_too_big
          - malformed_import_file
    surveyLaunchSettings:
      required:
      - accountIanaTimeZone
      - actions
      - amplitude_data
      - commentRepliesSection
      - last_bulk_upload_props
      - links
      - security_token
      - survey_id
      - survey_launch_settings
      type: object
      properties:
        survey_launch_settings:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings'
        actions:
          $ref: '#/components/schemas/surveyLaunchSettings_actions'
        security_token:
          type: string
          example: j7yIXwGF_g-Kq-ntq6zscDecutVhmf15Oaz8J9Rs9-qQnBw3JeIOlkQshLmLBL20l6AVOG1WLWK9d1TryQHNOg
        links:
          $ref: '#/components/schemas/surveyLaunchSettings_links'
        survey_id:
          type: string
          example: 6671d301-5bdc-4b2e-ad7b-8fb3ea38e478
        accountIanaTimeZone:
          type: string
          example: Australia/Melbourne
        commentRepliesSection:
          $ref: '#/components/schemas/surveyLaunchSettings_commentRepliesSection'
        last_bulk_upload_props:
          $ref: '#/components/schemas/surveyLaunchSettings_last_bulk_upload_props'
        amplitude_data:
          $ref: '#/components/schemas/surveyLaunchSettings_amplitude_data'
    userNotLoggedIn:
      required:
      - redirect_url
      type: object
      properties:
        redirect_url:
          type: string
          example: /session/sign_in
    launchSchedulerBody:
      required:
      - aggregate_id
      - communicated_closure_offset
      - launch_plan_steps
      type: object
      properties:
        aggregate_id:
          type: string
          description: A unique identifier for the aggregate.
          format: uuid
        launch_plan_steps:
          type: array
          description: List of steps in the launch plan with corresponding datetime
            information.
          items:
            $ref: '#/components/schemas/launchSchedulerBody_launch_plan_steps'
        communicated_closure_offset:
          type: integer
          description: "The communicated closure offset value, in days."
          example: 1
    Variables:
      type: object
      properties:
        surveyId:
          type: string
          example: 655c2a5145e41d00512547e5
        comparisonId:
          type: string
          nullable: true
          example: .auto_trend.649bc4bc1e215f0028ed5573
        categoryId:
          type: string
          example: 655c2a5145e41d00512547f3
        category:
          type: string
          example: FACTOR
          enum:
          - FACTOR
          - QUESTION
        demographicFilters:
          $ref: '#/components/schemas/Variables_demographicFilters'
    TrendData:
      type: object
      properties:
        comparison:
          $ref: '#/components/schemas/TrendData_comparison'
        scores:
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/TrendData_scores'
      nullable: true
    Errors:
      type: array
      nullable: true
      items:
        $ref: '#/components/schemas/Errors_inner'
    FactorInsightReportData:
      type: object
      properties:
        factor:
          $ref: '#/components/schemas/FactorInsightReportData_factor'
        ratingQuestions:
          type: array
          description: The rating questions for the factor
          items:
            $ref: '#/components/schemas/RatingQuestion'
        recommendedQuestions:
          type: array
          description: "Recommended focus areas based on the question’s impact on\
            \ the survey index factor, favorable score and comparison to benchmarks.\
            \ This field is only available for the index factor."
          items:
            $ref: '#/components/schemas/RatingQuestion'
        reportType:
          type: string
          example: factorInsight
        showCommentCount:
          type: boolean
          example: true
    ParticipationSummary:
      type: object
      properties:
        closeDate:
          type: string
          format: date-time
          nullable: true
          example: 2024-10-26T23:00:00.641Z
        communicatedCloseAt:
          type: string
          format: date-time
          nullable: true
          example: 2024-10-25T23:00:00Z
        companies:
          type: number
          nullable: true
          example: null
        daysRemaining:
          type: integer
          nullable: true
          example: 4
        launchDate:
          type: string
          format: date
          nullable: true
          example: 2024-09-12
        path:
          type: string
          description: The path to the participation report
          nullable: true
          example: /surveys/6626f72decf46f002c11b742/reports/admin/participation_report
        responseCount:
          type: integer
          example: 754
        score:
          type: number
          nullable: true
          example: 85.10158013544017
        status:
          type: string
          example: closed
          enum:
          - design
          - scheduled
          - paused
          - active
          - closed
        totalCount:
          type: integer
          example: 886
    Comparison:
      type: object
      properties:
        comparison:
          type: string
          example: 5879bea8625f15007e00620d
        comparisonLabel:
          type: string
          example: New tech 2015
        comparisonOptions:
          type: array
          items:
            $ref: '#/components/schemas/Comparison_comparisonOptions'
    FilterPrivacyFailure:
      nullable: true
      oneOf:
      - type: object
        properties:
          type:
            type: string
            enum:
            - filter_below_minimum_reporting_group
          reasons:
            $ref: '#/components/schemas/FilterPrivacyFailure_reasons'
      - type: object
        properties:
          type:
            type: string
            enum:
            - filter_triangulation_check_failed
          reasons:
            $ref: '#/components/schemas/FilterPrivacyFailure_reasons_1'
    QuestionInsightReportData:
      type: object
      properties:
        reportType:
          type: string
          example: questionInsight
        showCommentCount:
          type: boolean
          example: true
        question:
          $ref: '#/components/schemas/Question_1'
    orgSurveyData:
      required:
      - aggregateId
      - configuration
      - description
      - flags
      - id
      - launchedAt
      - nameTranslations
      - region
      - status
      - surveyPeriodType
      - type
      type: object
      properties:
        id:
          type: string
          example: 67ff0b44437b38a664cd7a37
        configuration:
          type: object
          additionalProperties:
            type: string
          example:
            captureLikertColorSchema: classical
        flags:
          type: object
          additionalProperties:
            type: string
          example:
            improvedCommsConfiguration: enabled
        aggregateId:
          type: string
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        nameTranslations:
          type: object
          additionalProperties:
            type: string
          example:
            en: Quick Engagement Survey Apr 2025 v4
        description:
          type: object
          additionalProperties:
            type: string
          nullable: true
          example:
            en: Description for Quick Engagement Survey Apr 2025 v4
        launchedAt:
          type: string
          format: date-time
          nullable: true
          example: 2025-04-01T00:00:00Z
        status:
          type: string
          example: design
          enum:
          - design
          - active
          - closed
        type:
          $ref: '#/components/schemas/surveyType'
        surveyPeriodType:
          $ref: '#/components/schemas/surveyPeriodType'
        region:
          $ref: '#/components/schemas/region'
    surveyStructure:
      required:
      - accountId
      - accountMongoId
      - configuration
      - demographics
      - description
      - factors
      - flags
      - id
      - isOrgSurvey
      - launchedAt
      - mongoId
      - nameTranslations
      - questions
      - region
      - sections
      - status
      - surveyPeriodType
      - type
      type: object
      properties:
        id:
          type: string
          description: The Aggregate ID of this survey
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        mongoId:
          type: string
          description: "The Mongo ID of the survey, used for internal references."
          example: 67ff0b44437b38a664cd7a37
        accountId:
          type: string
          description: The aggregate ID of the account which owns this survey.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        accountMongoId:
          type: string
          description: The Mongo ID of the account which owns this survey.
          example: 67ff0b44437b38a664cd7a37
        configuration:
          type: object
          additionalProperties:
            type: string
          example:
            captureLikertColorSchema: classical
        flags:
          type: object
          additionalProperties:
            type: string
          example:
            improvedCommsConfiguration: enabled
        nameTranslations:
          type: object
          additionalProperties:
            type: string
          example:
            en: Quick Engagement Survey Apr 2025 v4
        description:
          type: object
          additionalProperties:
            type: string
          nullable: true
          example:
            en: Description for Quick Engagement Survey Apr 2025 v4
        launchedAt:
          type: string
          format: date-time
          nullable: true
          example: 2025-04-01T00:00:00Z
        status:
          type: string
          example: design
          enum:
          - design
          - active
          - closed
        type:
          $ref: '#/components/schemas/surveyType'
        surveyPeriodType:
          $ref: '#/components/schemas/surveyPeriodType'
        isOrgSurvey:
          type: boolean
          description: True if the survey is a central organisation survey
        region:
          $ref: '#/components/schemas/region'
        questions:
          type: array
          items:
            $ref: '#/components/schemas/Question'
        demographics:
          type: array
          items:
            $ref: '#/components/schemas/demographic'
        factors:
          type: array
          items:
            $ref: '#/components/schemas/factor_1'
        sections:
          type: array
          items:
            $ref: '#/components/schemas/sectionSpecifications'
    surveySpecifications:
      required:
      - aggregateId
      - configuration
      - flags
      - nameTranslations
      - orgSurveyAggregateId
      - orgSurveyRegion
      - surveyPeriodType
      - type
      type: object
      properties:
        aggregateId:
          type: string
          description: The aggregate ID of the survey.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        orgSurveyAggregateId:
          type: string
          description: The aggregate ID of the organization survey.
          format: uuid
          example: 5f492219-3915-42cf-9cef-ca5e930c97cb
        nameTranslations:
          type: object
          additionalProperties:
            type: string
          example:
            en: Quick Engagement Survey Apr 2025 v4
        type:
          $ref: '#/components/schemas/surveyType'
        surveyPeriodType:
          $ref: '#/components/schemas/surveyPeriodType'
        orgSurveyRegion:
          $ref: '#/components/schemas/region'
        configuration:
          type: object
          additionalProperties:
            type: string
          example:
            captureLikertColorSchema: classical
        flags:
          type: object
          additionalProperties:
            type: string
          example:
            improvedCommsConfiguration: enabled
    sectionSpecifications:
      required:
      - intendedPurpose
      - longDescription
      - nameTranslations
      - position
      - sectionId
      - shortDescription
      type: object
      properties:
        sectionId:
          type: string
          description: The aggregate ID of the section.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        position:
          type: string
          description: The position of the section in UI.
          format: integer
          example: "1"
        nameTranslations:
          type: array
          items:
            $ref: '#/components/schemas/sectionSpecifications_nameTranslations'
        longDescription:
          type: array
          items:
            $ref: '#/components/schemas/sectionSpecifications_longDescription'
        shortDescription:
          type: array
          items:
            $ref: '#/components/schemas/sectionSpecifications_shortDescription'
        intendedPurpose:
          type: string
          example: standard
          enum:
          - standard
          - demographic
    factorSpecifications:
      required:
      - factorId
      - isIndexFactor
      - isKeyFactor
      - nameTranslations
      type: object
      properties:
        factorId:
          type: string
          description: The aggregate ID of the factor.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        nameTranslations:
          type: array
          items:
            $ref: '#/components/schemas/factorSpecifications_nameTranslations'
        isIndexFactor:
          type: boolean
          description: Indicates if the factor is an index factor.
          example: true
        isKeyFactor:
          type: boolean
          description: Indicates if the factor is a key factor.
          example: true
    questionSpecifications:
      required:
      - descriptionTranslations
      - factorIds
      - intendedPurpose
      - isMandatory
      - questionId
      - questionType
      - ratingScale
      - sectionId
      - selectOptions
      - selectionLimit
      - stqOrder
      type: object
      properties:
        questionId:
          type: string
          description: The aggregate ID of the question.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        sectionId:
          type: string
          description: The aggregate ID of the section which this question(stq) belongs.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        factorIds:
          type: array
          description: The aggregate ID of the factors which this question(stq) belongs.
          items:
            type: string
            format: uuid
            example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        descriptionTranslations:
          type: array
          description: The text of the question in different locales.
          items:
            $ref: '#/components/schemas/questionSpecifications_descriptionTranslations'
        questionType:
          type: string
          description: The type of question.
          example: rating
          enum:
          - free-text
          - rating
          - select
        ratingScale:
          type: string
          description: The rating scale of the question.
          example: agreement
          enum:
          - agreement
          - rating
          - satisfaction
          - quality
          - frequency
          - frequency_alt
          - performance
          - importance
          - focus
        selectOptions:
          type: array
          description: "The options for the select question. If the question is not\
            \ a select question, this field should be null"
          nullable: true
          items:
            $ref: '#/components/schemas/selectOptionSpecifications'
        otherOption:
          $ref: '#/components/schemas/questionSpecifications_otherOption'
        intendedPurpose:
          type: string
          description: The intended purpose of the question.
          example: classification
          enum:
          - classification
          - demographic
          - feedback
          - reviewer_notes
          - interview
          - outcome
        stqOrder:
          type: integer
          description: The order of the stq that belongs to this question.
          nullable: true
          example: 1
        selectionLimit:
          type: integer
          description: "The selection limit of the select question. If the question\
            \ is not a select question, this field should be null"
          nullable: true
          example: 3
        isMandatory:
          type: boolean
          description: "The mandatory status of the question. The question can be\
            \ either optional or mandatory. If the question is not mandatory, this\
            \ field should be false."
          example: true
    questionDisplayConditionSpecifications:
      type: object
      properties:
        questionId:
          type: string
          description: The aggregate ID of the question.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        answerBranchingRule:
          $ref: '#/components/schemas/questionDisplayConditionSpecifications_answerBranchingRule'
    SftpIntegration:
      required:
      - adaptor
      - daily_sync
      - hostname
      - integration_type
      - last_data_sync
      - notify_user_email
      - notify_user_id
      - public_key
      - status
      - username
      type: object
      properties:
        integration_type:
          type: string
          enum:
          - sftp
        adaptor:
          type: string
          enum:
          - securefile
        hostname:
          type: string
        username:
          type: string
        alias:
          type: string
        public_key:
          type: string
        status:
          type: string
          enum:
          - active
          - enabled
          - disabled
          - incomplete
          - archived
        daily_sync:
          type: boolean
        notify_user_id:
          type: string
        notify_user_email:
          type: string
        last_data_sync:
          $ref: '#/components/schemas/SftpIntegration_last_data_sync'
    HrisIntegration:
      required:
      - adaptor
      - auth_user_id
      - credentials
      - daily_sync
      - demographics
      - filters
      - integration_type
      - last_data_sync
      - notify_user_email
      - status
      type: object
      properties:
        integration_type:
          type: string
          enum:
          - native
        adaptor:
          type: string
          enum:
          - bamboo
          - ceridian
          - elmo
          - gusto
          - hibob
          - namely
          - paylocity
          - personio
          - workday
        status:
          type: string
          enum:
          - auth_complete
          - auth_expired
          - enabled
          - disabled
        auth_user_id:
          type: string
        credentials:
          type: object
          additionalProperties: true
        daily_sync:
          type: boolean
        demographics:
          type: object
          additionalProperties: true
        filters:
          type: object
          additionalProperties: true
        notify_user_email:
          type: string
        notify_user_id:
          type: string
        auth_email:
          type: string
        last_data_sync:
          $ref: '#/components/schemas/SftpIntegration_last_data_sync'
        hris_report_link:
          type: string
        integration_id:
          type: string
        metadata:
          type: object
          additionalProperties: true
        companies:
          type: array
          items:
            $ref: '#/components/schemas/HrisIntegration_companies'
    Correlation:
      type: object
      properties:
        score:
          maximum: 1
          minimum: -1
          type: number
          format: float
          nullable: true
          example: 0.4
        type:
          type: string
          nullable: true
          example: tau-c
          enum:
          - tau-c
          - pearson
          - null
      description: |-
        Indication of how strongly a favorable response correlates with high / low index factor scores.
        We include the correlation type (currently tau-c or pearson) as this type effects how we interpret the associated score
        E.g. a 0.5 tau-c is a "very high" correlation, but 0.5 pearson is merely "high".
    Scores:
      type: object
      properties:
        favorable:
          maximum: 100
          minimum: 0
          type: number
          format: integer
          example: 65
        unfavorable:
          maximum: 100
          minimum: 0
          type: number
          format: integer
          example: 20
        decimalFavourableScore:
          maximum: 1
          minimum: 0
          type: number
          format: float
          example: 0.654
    validationResults:
      required:
      - errors
      - warnings
      type: object
      properties:
        errors:
          type: boolean
          example: false
        warnings:
          type: integer
          example: 10
    validationResults_1:
      required:
      - errors
      - warnings
      type: object
      properties:
        errors:
          type: boolean
          example: false
        warnings:
          type: integer
          example: 10
      nullable: true
    factor:
      required:
      - id
      - name
      - order
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: b6f5fb53-77f1-4810-aef5-de21d5ef829f
        name:
          type: array
          items:
            $ref: '#/components/schemas/factor_name'
        order:
          type: integer
          example: 1
    events:
      required:
      - cachedAt
      - closedAt
      - launchedAt
      type: object
      properties:
        launchedAt:
          type: integer
          example: 1627849200
        closedAt:
          type: integer
          example: 1627935600
        cachedAt:
          type: integer
          example: 1628022000
    questions:
      required:
      - answer_based_branching_condition
      - branching_conditions
      - code
      - description
      - factor_ids
      - id
      - intended_purpose
      - mandatory
      - order
      - other_option
      - scale
      - section_id
      - select_options
      - short_text
      - status
      - type
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
        section_id:
          type: string
          format: uuid
          nullable: true
          example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
        order:
          type: integer
          example: 1
        description:
          type: array
          items:
            $ref: '#/components/schemas/questions_description'
        short_text:
          type: array
          items:
            $ref: '#/components/schemas/questions_short_text'
        mandatory:
          type: boolean
          example: true
        factor_ids:
          type: array
          items:
            type: string
            format: uuid
            example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
        type:
          type: string
          example: RatingQuestion
          enum:
          - FreeTextQuestion
          - RatingQuestion
          - MultiSelectQuestion
        code:
          type: string
          example: testaccount.Z1rFsq
        scale:
          type: string
          example: rating
          enum:
          - agreement
          - rating
          - satisfaction
          - quality
          - frequency
          - frequency_alt
          - performance
          - importance
          - focus
        comments:
          type: boolean
          example: true
        other_option:
          type: boolean
          nullable: true
          example: false
        custom_other_option_label:
          type: array
          items:
            $ref: '#/components/schemas/questions_custom_other_option_label'
        select_options:
          type: array
          items:
            $ref: '#/components/schemas/questions_select_options'
        selection_limit:
          type: integer
          nullable: true
          example: 1
        branching_conditions:
          type: array
          items:
            $ref: '#/components/schemas/questions_branching_conditions'
        answer_based_branching_condition:
          $ref: '#/components/schemas/questions_answer_based_branching_condition'
        intended_purpose:
          type: string
          example: standard
          enum:
          - standard
          - demographic
        status:
          type: string
          example: active
          enum:
          - deleted
          - active
    sections:
      required:
      - code
      - description
      - id
      - intended_purpose
      - name
      - order
      - status
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: e4ac4648-6e58-4ddd-a433-3aaf0b803fbc
        name:
          type: array
          items:
            $ref: '#/components/schemas/sections_name'
        description:
          $ref: '#/components/schemas/sections_description'
        code:
          type: string
          example: S1
        intended_purpose:
          type: string
          example: standard
          enum:
          - standard
          - demographic
        status:
          type: string
          example: active
          enum:
          - deleted
          - active
        order:
          type: integer
          example: 1
    accountDemographics:
      required:
      - demographic_values
      - id
      - name
      - status
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: c7cc9f62-4ca7-4e3d-9a14-a318ee8022b7
        name:
          type: array
          items:
            $ref: '#/components/schemas/accountDemographics_name'
        status:
          type: string
          example: active
          enum:
          - deleted
          - active
        demographic_values:
          type: array
          items:
            $ref: '#/components/schemas/accountDemographics_demographic_values'
    captureLayout:
      required:
      - demographicsPositioning
      - id
      type: object
      properties:
        id:
          type: string
          format: uuid
          nullable: true
          example: 35bab727-eb84-401a-8087-d3b7a1840ffb
        demographicsPositioning:
          type: string
          example: top
          enum:
          - top
          - bottom
    profile:
      required:
      - accountName
      - accountSubdomain
      type: object
      properties:
        accountName:
          type: string
          example: Account Name
        accountSubdomain:
          type: string
          example: account-subdomain
    Score:
      type: object
      properties:
        decimal_score:
          type: number
          example: 0.60123
        favorable:
          maximum: 100
          minimum: 0
          type: integer
          example: 60
        unfavorable:
          maximum: 100
          minimum: 0
          type: integer
          example: 30
        significant:
          type: boolean
          example: true
    RatingQuestion:
      type: object
      properties:
        id:
          type: string
          description: Survey to question id
          example: 5879be59625f15007e001aa6
        questionId:
          type: string
          description: Question id
          example: 5879be59625f15007e001aa5
        aggregateId:
          type: string
          example: 9c441f9c-522a-48b6-b287-68033803f39b
        companies:
          type: number
          nullable: true
          example: null
        label:
          type: string
          example: I am proud to work for Hooli
        factorName:
          type: string
          example: Engagement
        score:
          $ref: '#/components/schemas/Score'
        overallScore:
          $ref: '#/components/schemas/RatingQuestion_overallScore'
        deltaScore:
          type: integer
          example: -12
        population:
          type: integer
          example: 64
        answeredPopulation:
          type: integer
          example: 64
        totalPopulation:
          type: integer
          example: 64
        commentCount:
          type: integer
          description: Number of comments associated with the question. A null result
            indicates comments are disabled.
          nullable: true
          example: 6
        correlation:
          $ref: '#/components/schemas/RatingQuestion_correlation'
        path:
          $ref: '#/components/schemas/RatingQuestion_path'
        hasIndexFactor:
          type: boolean
          example: true
        hasBranchingConditions:
          type: boolean
          example: false
    Question_1:
      type: object
      properties:
        id:
          type: string
          description: Survey to question id
          example: 5879be59625f15007e001aa6
        questionId:
          type: string
          description: Question id
          example: 5879be59625f15007e001aa5
        aggregateId:
          type: string
          example: 9c441f9c-522a-48b6-b287-68033803f39b
        companies:
          type: number
          nullable: true
          example: null
        label:
          type: string
          example: I am proud to work for Hooli
        factorName:
          type: string
          example: Engagement
        score:
          $ref: '#/components/schemas/Score'
        deltaScore:
          type: integer
          example: -12
        population:
          type: integer
          example: 64
        answeredPopulation:
          type: integer
          example: 64
        totalPopulation:
          type: integer
          example: 64
        commentCount:
          type: integer
          description: Number of comments associated with the question. A null result
            indicates comments are disabled.
          nullable: true
          example: 6
        correlation:
          $ref: '#/components/schemas/RatingQuestion_correlation'
        paths:
          $ref: '#/components/schemas/RatingQuestion_path'
        hasIndexFactor:
          type: boolean
          example: true
        hasDemographicReport:
          type: boolean
          example: false
        isRecommended:
          type: boolean
          example: true
        scoresByDemographic:
          type: array
          items:
            $ref: '#/components/schemas/Question_1_scoresByDemographic'
    surveyType:
      type: string
      example: engagement
      enum:
      - engagement
      - exit
      - master
      - onboard
      - three_sixty
    surveyPeriodType:
      type: string
      example: snapshot
      enum:
      - snapshot
      - adhoc
      - continuous
    region:
      type: string
      example: us
      enum:
      - us
      - eu
      - au
    demographic:
      required:
      - demographicId
      - demographicQuestionMongoId
      - demographicSTQMongoId
      - demographicValues
      - descriptionTranslations
      - nameTranslations
      type: object
      properties:
        demographicId:
          type: string
          description: The aggregate ID of the demographic.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        demographicSTQMongoId:
          type: string
          description: The mongo ID of the survey-to-question that links this demographic
          example: 67ff0b44437b38a664cd7a37
        demographicQuestionMongoId:
          type: string
          description: The mongo ID of the question that models this demographic
          example: 67ff0b44437b38a664cd7a37
        descriptionTranslations:
          type: array
          description: The text of the demographic description in different locales.
          items:
            $ref: '#/components/schemas/demographic_descriptionTranslations'
        nameTranslations:
          type: array
          description: The text of the demographic name in different locales.
          items:
            $ref: '#/components/schemas/demographic_descriptionTranslations'
        demographicValues:
          type: array
          description: The values that can be assigned for this demographic.
          items:
            $ref: '#/components/schemas/demographicValue'
    factor_1:
      required:
      - factorId
      - factorMongoId
      - isIndexFactor
      - isKeyFactor
      - nameTranslations
      type: object
      properties:
        factorId:
          type: string
          description: The aggregate ID of the factor.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        factorMongoId:
          type: string
          description: The Mongo ID of the factor.
          example: 67ff0b44437b38a664cd7a37
        nameTranslations:
          type: array
          items:
            $ref: '#/components/schemas/factorSpecifications_nameTranslations'
        isIndexFactor:
          type: boolean
          description: Indicates if the factor is an index factor.
          example: true
        isKeyFactor:
          type: boolean
          description: Indicates if the factor is a key factor.
          example: true
    selectOptionSpecifications:
      required:
      - selectOptionId
      - sortTerm
      - valueTranslations
      type: object
      properties:
        selectOptionId:
          type: string
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        valueTranslations:
          type: array
          example:
          - text: Option 1
            locale: en
          - text: Opción 1
            locale: es
          items:
            $ref: '#/components/schemas/selectOptionSpecifications_valueTranslations'
        sortTerm:
          type: object
          nullable: true
          example:
            en: "00001"
    demographicValue:
      required:
      - demographicValueId
      - demographicValueMongoId
      - labelTranslations
      - valueTranslations
      type: object
      properties:
        demographicValueId:
          type: string
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        demographicValueMongoId:
          type: string
          description: The mongo ID of this select option
          example: 67ff0b44437b38a664cd7a37
        labelTranslations:
          type: array
          description: The demographic value label localised
          example:
          - text: Option 1
            locale: en
          - text: Opción 1
            locale: es
          items:
            $ref: '#/components/schemas/selectOptionSpecifications_valueTranslations'
        valueTranslations:
          type: array
          description: The demographic value localised
          example:
          - text: Option 1
            locale: en
          - text: Opción 1
            locale: es
          items:
            $ref: '#/components/schemas/selectOptionSpecifications_valueTranslations'
    inline_response_200:
      oneOf:
      - $ref: '#/components/schemas/ReportResponses'
      - $ref: '#/components/schemas/Report360Responses'
    inline_response_404:
      type: object
      properties:
        error:
          type: string
      example:
        error: Not found
    surveyMongoId_participant_import_body:
      type: object
      properties:
        upload:
          type: string
          format: binary
        append:
          type: string
          example: "1"
    surveyMongoId_participant_import_v2_body:
      type: object
      properties:
        upload:
          type: string
          format: binary
        append:
          type: string
          example: "1"
    inline_response_400:
      type: object
      properties:
        error:
          type: string
          example: an error occurred in scheduling
        code:
          type: string
          nullable: true
          example: SL001
    inline_response_500:
      type: object
      properties:
        error:
          type: string
          example: generic_command_exception
    survey_scheduler_cancel_launch_body:
      type: object
      properties:
        aggregate_id:
          type: string
          description: A unique identifier for the aggregate.
          format: uuid
    inline_response_400_1:
      type: object
      properties:
        error:
          type: string
          example: an error occurred in scheduling
    survey_scheduler_close_body:
      type: object
      properties:
        aggregate_id:
          type: string
          description: A unique identifier for the aggregate.
          format: uuid
    survey_scheduler_reopen_body:
      type: object
      properties:
        aggregate_id:
          type: string
          description: A unique identifier for the aggregate.
          format: uuid
    survey_scheduler_resend_reminder_body:
      type: object
      properties:
        aggregate_id:
          type: string
          description: A unique identifier for the aggregate.
          format: uuid
        reminder_type:
          type: string
          enum:
          - first_reminder
          - final_reminder
    api_graphql_body:
      required:
      - operationName
      - query
      - variables
      type: object
      properties:
        operationName:
          type: string
          example: SurveyTrendScores
        query:
          type: string
          example: |
            query Comparison($surveyId: ID!, $comparisonId: ID!) {
              comparison(surveyId: $surveyId, comparisonId: $comparisonId) {
                name
                scores {
                  factorId
                  score
                }
              }
            }
        variables:
          $ref: '#/components/schemas/Variables'
    inline_response_200_1:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/TrendData'
        errors:
          $ref: '#/components/schemas/Errors'
    inline_response_200_2:
      type: object
      properties:
        reportData:
          $ref: '#/components/schemas/FactorInsightReportData'
        participationSummary:
          $ref: '#/components/schemas/ParticipationSummary'
        comparison:
          $ref: '#/components/schemas/Comparison'
        filterPrivacyFailure:
          $ref: '#/components/schemas/FilterPrivacyFailure'
        hierarchyPrivacyThresholdExceeded:
          type: boolean
          example: false
        insignificantFilters:
          type: boolean
          example: false
    inline_response_200_3:
      type: object
      properties:
        reportData:
          $ref: '#/components/schemas/QuestionInsightReportData'
        participationSummary:
          $ref: '#/components/schemas/ParticipationSummary'
        comparison:
          $ref: '#/components/schemas/Comparison'
        filterPrivacyFailure:
          $ref: '#/components/schemas/FilterPrivacyFailure'
        hierarchyPrivacyThresholdExceeded:
          type: boolean
          example: false
        insignificantFilters:
          type: boolean
          example: false
    accountAggregateId_upsert_survey_body:
      required:
      - surveySpecifications
      type: object
      properties:
        surveySpecifications:
          $ref: '#/components/schemas/surveySpecifications'
    inline_response_201:
      required:
      - aggregateId
      - id
      type: object
      properties:
        id:
          type: string
          description: The Mongo ID of the survey.
          example: 67ff0b44437b38a664cd7a37
        aggregateId:
          type: string
          description: The Aggregate ID of the survey.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
    surveyAggregateId_upsert_section_body:
      required:
      - sectionSpecifications
      type: object
      properties:
        sectionSpecifications:
          $ref: '#/components/schemas/sectionSpecifications'
    inline_response_201_1:
      required:
      - aggregateId
      - id
      type: object
      properties:
        id:
          type: string
          description: The Mongo ID of the section.
          example: 67ff0b44437b38a664cd7a37
        aggregateId:
          type: string
          description: The Aggregate ID of the section.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
    surveyAggregateId_delete_section_body:
      required:
      - sectionId
      type: object
      properties:
        sectionId:
          type: string
          description: The ID of the section to be deleted.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
    surveyAggregateId_upsert_factor_body:
      required:
      - factorSpecifications
      type: object
      properties:
        factorSpecifications:
          $ref: '#/components/schemas/factorSpecifications'
    inline_response_201_2:
      required:
      - aggregateId
      - id
      type: object
      properties:
        id:
          type: string
          description: The Mongo ID of the factor.
          example: 67ff0b44437b38a664cd7a37
        aggregateId:
          type: string
          description: The Aggregate ID of the factor.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
    surveyAggregateId_delete_survey_question_body:
      required:
      - correlationId
      - questionId
      type: object
      properties:
        questionId:
          type: string
          description: The ID of the question to be deleted.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        correlationId:
          type: string
          description: The correlation ID for the Temporal workflow creating/updating
            the survey.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
    surveyAggregateId_upsert_question_body:
      required:
      - questionSpecifications
      type: object
      properties:
        questionSpecifications:
          $ref: '#/components/schemas/questionSpecifications'
    inline_response_201_3:
      required:
      - aggregateId
      - id
      - stqId
      type: object
      properties:
        id:
          type: string
          description: The Mongo ID of the question.
          example: 67ff0b44437b38a664cd7a37
        aggregateId:
          type: string
          description: The Aggregate ID of the question.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        stqId:
          type: string
          description: The Mongo ID of the STQ.
          example: 67ff0b44437b38a664cd7a37
    surveyAggregateId_upsert_question_display_condition_body:
      required:
      - questionDisplayConditionSpecifications
      type: object
      properties:
        questionDisplayConditionSpecifications:
          $ref: '#/components/schemas/questionDisplayConditionSpecifications'
    inline_response_201_4:
      required:
      - aggregateId
      - id
      type: object
      properties:
        id:
          type: string
          description: The Mongo ID of the question.
          example: 67ff0b44437b38a664cd7a37
        aggregateId:
          type: string
          description: The Aggregate ID of the question.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
    AppDataResponse_account_admins:
      required:
      - email
      - id
      type: object
      properties:
        id:
          type: string
        email:
          type: string
    AppDataResponse_account_credentials_sftp:
      required:
      - hostname
      - username
      type: object
      properties:
        username:
          type: string
        hostname:
          type: string
    AppDataResponse_account_credentials_legacy_sap:
      required:
      - password
      - username
      type: object
      properties:
        username:
          type: string
        password:
          type: string
    AppDataResponse_account_credentials:
      required:
      - legacy_sap
      - sftp
      type: object
      properties:
        sftp:
          $ref: '#/components/schemas/AppDataResponse_account_credentials_sftp'
        legacy_sap:
          $ref: '#/components/schemas/AppDataResponse_account_credentials_legacy_sap'
    AppDataResponse_account_time_zone:
      required:
      - abbreviated
      - name
      type: object
      properties:
        name:
          type: string
        abbreviated:
          type: string
    AppDataResponse_account:
      required:
      - admins
      - ceridian_enabled
      - credentials
      - elmo_enabled
      - employee_import_link
      - hibob_enabled
      - id
      - legacy_sap_enabled
      - legacy_sap_icd_download_path
      - paylocity_enabled
      - time_zone
      type: object
      properties:
        admins:
          type: array
          items:
            $ref: '#/components/schemas/AppDataResponse_account_admins'
        credentials:
          $ref: '#/components/schemas/AppDataResponse_account_credentials'
        elmo_enabled:
          type: boolean
        employee_import_link:
          type: string
        hibob_enabled:
          type: boolean
        ceridian_enabled:
          type: boolean
        id:
          type: string
        legacy_sap_enabled:
          type: boolean
        legacy_sap_icd_download_path:
          type: string
        paylocity_enabled:
          type: boolean
        time_zone:
          $ref: '#/components/schemas/AppDataResponse_account_time_zone'
    AppDataResponse_current_user:
      required:
      - is_superuser
      type: object
      properties:
        is_superuser:
          type: boolean
    SetIntegrationRequest_credentials:
      type: object
      properties:
        alias:
          type: string
        hostname:
          type: string
        username:
          type: string
    performanceRatingCycles_inner:
      type: object
      properties:
        performanceCycleAggregateId:
          type: string
          format: uuid
          example: 07d4877f-**************-a40f6a974ef5
        performanceCycleLabel:
          type: string
          example: March 2023
        linked:
          type: boolean
          example: true
        status:
          type: string
          example: open
          enum:
          - open
          - closed
        startDate:
          type: string
          format: date-time
          example: 2023-01-31T13:00:00Z
        endDate:
          type: string
          format: date-time
          nullable: true
          example: 2023-01-31T13:00:00Z
    ReportResponses_survey:
      type: object
      properties:
        name:
          type: string
          description: Survey name
          example: Hooli's Engagement Survey
        status:
          type: string
          description: Survey status
          example: closed
        response_count:
          type: integer
          description: The number of responses on the survey
          example: 211
        invited_count:
          type: integer
          description: Survey
          example: 241
        type:
          type: string
          description: The type of survey
          example: engagement
        created_at:
          type: string
          description: The date the survey was created
          format: date-time
          example: 2017-01-14T05:59:53.753Z
        launched_at:
          type: string
          description: The date the survey was launched
          format: date-time
          example: 2017-01-14T05:59:53.753Z
        closed_at:
          type: string
          description: The date the survey was closed
          format: date-time
          example: 2017-01-14T05:59:53.753Z
    ReportResponses_questions:
      type: object
      properties:
        code:
          type: string
          example: hooli.150
        type:
          type: string
          example: MultiSelectQuestion
        theme:
          type: string
          example: People & Teams
        factors:
          type: array
          example:
          - Teamwork & Ownership
          items:
            type: string
        label:
          type: string
          example: 'If I weren''t in my current team I could see myself contributing
            to:'
        select_options:
          type: object
          additionalProperties:
            type: string
          example:
            "5879be9d625f15007e0055cb": Marketing
    ReportResponses_responses:
      type: object
      properties:
        email:
          type: string
          example: <EMAIL>
        employee_id:
          type: integer
          example: 52952
        submitted_at:
          type: string
          format: date-time
          example: 2017-01-14T06:01:01.956Z
        answers:
          type: object
          additionalProperties: true
    Report360Responses_responses:
      type: object
      properties:
        email:
          type: string
          example: <EMAIL>
        employee_id:
          type: integer
          example: 52952
        submitted_at:
          type: string
          format: date-time
          example: 2017-01-14T06:01:01.956Z
        answers:
          type: object
          additionalProperties: true
        relationship_to_reviewee:
          type: string
          nullable: true
          example: Manager
        reviewee_email:
          type: string
          format: email
          nullable: true
          example: <EMAIL>
    account_admin_accountAdmin:
      required:
      - hasRole
      type: object
      properties:
        hasRole:
          type: boolean
          description: Whether this employee has the account admin role assigned
          example: true
    survey_creator_surveyCreator_demographicValues:
      required:
      - id
      - label
      type: object
      properties:
        id:
          type: string
          description: A uuid that identifies the demographic value
          example: dbb1c7c2-0238-4f4b-a318-829c1513327c
        label:
          type: string
          description: The name of the demographic value
          example: APAC
    survey_creator_surveyCreator_scopes:
      required:
      - demographicId
      - demographicName
      - demographicValues
      type: object
      properties:
        demographicId:
          type: string
          description: A uuid that identifies the demographic
          example: dbb1c7c2-0238-4f4b-a318-829c1513327c
        demographicName:
          type: string
          description: The name of the demographic
          example: Region
        demographicValues:
          type: array
          description: A group of values within this demographic
          items:
            $ref: '#/components/schemas/survey_creator_surveyCreator_demographicValues'
    survey_creator_surveyCreator:
      required:
      - hasRole
      - scopes
      type: object
      properties:
        hasRole:
          type: boolean
          description: Whether this employee has the survey creator role assigned
          example: true
        scopes:
          type: array
          description: |
            Scopes that limit the applicability of this role. These correspond to demographic values.
            E.g., A survey creator only for the APAC Region.
          items:
            $ref: '#/components/schemas/survey_creator_surveyCreator_scopes'
    update_survey_creator_surveyCreator_scopes:
      required:
      - demographicId
      - demographicValueIds
      type: object
      properties:
        demographicId:
          type: string
          description: A uuid that identifies the demographic
          example: dbb1c7c2-0238-4f4b-a318-829c1513327c
        demographicValueIds:
          type: array
          description: A group of values within this demographic
          items:
            type: string
            description: A uuid that identifies the demographic value
            example: dbb1c7c2-0238-4f4b-a318-829c1513327c
    update_survey_creator_surveyCreator:
      required:
      - hasRole
      type: object
      properties:
        hasRole:
          type: boolean
          description: Whether this employee has the survey creator role assigned
          example: true
        scopes:
          type: array
          description: |
            Scopes that limit the applicability of this role. These correspond to demographic values.
            E.g., A survey creator only for the APAC Region.
          items:
            $ref: '#/components/schemas/update_survey_creator_surveyCreator_scopes'
    survey_data_analyst_surveyDataAnalyst:
      required:
      - hasRole
      type: object
      properties:
        hasRole:
          type: boolean
          description: Whether this employee has the survey data analyst role assigned
          example: true
    survey_comment_replier_surveyCommentReplier:
      required:
      - hasRole
      - isFeatureFlagEnabled
      type: object
      properties:
        hasRole:
          type: boolean
          description: Whether this employee has the survey comment replier role assigned
          example: true
        isFeatureFlagEnabled:
          type: boolean
          description: The survey comment replier role is hidden behind a feature
            flag. This boolean value represents the state of that flag.
          example: true
    update_survey_comment_replier_surveyCommentReplier:
      required:
      - hasRole
      type: object
      properties:
        hasRole:
          type: boolean
          description: Whether this employee has the survey comment replier role assigned
          example: true
    account_admins_accountAdmins:
      required:
      - employeeAggregateId
      - name
      type: object
      properties:
        employeeAggregateId:
          type: string
          description: A uuid that identifies this employee who has the role assigned
          example: dbb1c7c2-0238-4f4b-a318-829c1513327c
        employeeId:
          type: string
          description: An id that identifies the employee in a HRIS
          example: "12345"
        avatarUrl:
          type: string
          description: A url for an avatar image of the employee
          example: https://www.example.com/image
        name:
          type: string
          description: The name of the employee
          example: Clarisse Bain
        email:
          type: string
          description: The email of the employee
          example: <EMAIL>
    survey_creators_scopes:
      required:
      - demographic
      - values
      type: object
      properties:
        demographic:
          type: string
          description: The name of the demographic
          example: Region
        values:
          type: array
          description: A group of values within this demographic
          items:
            type: string
            description: The name of the demographic value
            example: APAC
    survey_creators_surveyCreators:
      required:
      - employeeAggregateId
      - name
      - scopes
      type: object
      properties:
        employeeAggregateId:
          type: string
          description: A uuid that identifies this employee who has the role assigned
          example: dbb1c7c2-0238-4f4b-a318-829c1513327c
        employeeId:
          type: string
          description: An id that identifies the employee in a HRIS
          example: "12345"
        avatarUrl:
          type: string
          description: A url for an avatar image of the employee
          example: https://www.example.com/image
        name:
          type: string
          description: The name of the employee
          example: Clarisse Bain
        email:
          type: string
          description: The email of the employee
          example: <EMAIL>
        scopes:
          type: array
          description: |
            Scopes that limit the applicability of this role. These correspond to demographic values.
            E.g., A survey creator only for the APAC Region.
          items:
            $ref: '#/components/schemas/survey_creators_scopes'
    departments_departments:
      required:
      - name
      - value
      type: object
      properties:
        value:
          type: string
          description: A uuid that identifies the department demographic value id
          example: dbb1c7c2-0238-4f4b-a318-829c1513327c
        name:
          type: string
          description: The department name
          example: Finance
    permissions_permissions_granted:
      required:
      - granted
      - permission
      type: object
      properties:
        permission:
          type: string
          description: The name of the permission
          example: administration.account.roles.administer
        granted:
          type: boolean
          description: Whether the user in the JWT is granted this permission on the
            account
          example: false
    demographics_demographics:
      required:
      - id
      - name
      type: object
      properties:
        id:
          type: string
          description: A uuid that identifies the demographic
          example: dbb1c7c2-0238-4f4b-a318-829c1513327c
        name:
          type: string
          description: The demographic name
          example: Region
    demographic_values_demographic_values:
      required:
      - name
      - value
      type: object
      properties:
        name:
          type: string
          description: A uuid that corresponds to the demographic value id
          example: dbb1c7c2-0238-4f4b-a318-829c1513327c
        value:
          type: string
          description: The demographic value name
          example: APAC
    surveyDetails_survey_name:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Survey Name
    surveyDetails_survey:
      required:
      - account_demographics
      - events
      - factors
      - id
      - name
      - questions
      - sections
      - status
      type: object
      properties:
        factors:
          type: array
          items:
            $ref: '#/components/schemas/factor'
        status:
          type: string
          example: active
          enum:
          - active
          - design
          - closed
          - scheduled
          - archived
        events:
          $ref: '#/components/schemas/events'
        id:
          type: string
          format: uuid
          example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
        name:
          type: array
          items:
            $ref: '#/components/schemas/surveyDetails_survey_name'
        questions:
          type: array
          items:
            $ref: '#/components/schemas/questions'
        sections:
          type: array
          items:
            $ref: '#/components/schemas/sections'
        account_demographics:
          $ref: '#/components/schemas/accountDemographics'
    importValidationStatus_analyzingStep:
      type: object
      properties:
        progress:
          type: number
          example: 50
        result:
          type: string
        status:
          type: string
          example: new
          enum:
          - new
          - hold
          - active
          - pending
          - complete
          - error
          - skipped
        invalidParticipants:
          type: array
          example:
          - - <EMAIL>
            - "1234"
            - email_employee_id_mismatch
          - - ""
            - "4567"
            - no_active_user_found
          - - <EMAIL>
            - no_active_user_found
          items:
            type: array
            items:
              type: string
        validParticipants:
          type: array
          example:
          - "123"
          items:
            type: string
    importValidationStatus_applyingStep:
      type: object
      properties:
        progress:
          type: number
          example: 50
        result:
          type: string
        status:
          type: string
          example: new
          enum:
          - new
          - hold
          - active
          - pending
          - complete
          - error
          - skipped
        invalidParticipants:
          type: array
          example:
          - - email
            - <EMAIL>
          - - email
            - <EMAIL>
          - - employee_id
            - "12345"
          items:
            type: array
            items:
              type: string
    surveyLaunchSettings_survey_launch_settings_launch_plan_steps_datetime:
      required:
      - day
      - hour
      - minute
      - month
      - second
      - timezone
      - year
      type: object
      properties:
        year:
          type: integer
          example: 2024
        month:
          type: integer
          example: 11
        day:
          type: integer
          example: 26
        hour:
          type: integer
          example: 15
        minute:
          type: integer
          example: 15
        second:
          type: integer
          example: 0
        timezone:
          type: string
          example: Australia/Melbourne
    surveyLaunchSettings_survey_launch_settings_launch_plan_steps_metadata:
      required:
      - executor_name
      - participant_count
      type: object
      properties:
        participant_count:
          type: integer
          example: 0
        executor_name:
          type: string
          example: Admin Super1
    surveyLaunchSettings_survey_launch_settings_launch_plan_steps_launches:
      required:
      - datetime
      - metadata
      - status
      - type
      type: object
      properties:
        type:
          type: string
          enum:
          - launch
        status:
          type: string
          enum:
          - scheduled
          - running
          - completed
          - failed
          - skipped
          - canceled
        datetime:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_datetime'
        metadata:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_metadata'
    surveyLaunchSettings_survey_launch_settings_launch_plan_steps_first_reminders:
      required:
      - datetime
      - metadata
      - status
      - type
      type: object
      properties:
        type:
          type: string
          enum:
          - first_reminder
        status:
          type: string
          enum:
          - scheduled
          - running
          - completed
          - failed
          - skipped
          - canceled
        datetime:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_datetime'
        metadata:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_metadata'
    surveyLaunchSettings_survey_launch_settings_launch_plan_steps_final_reminders:
      required:
      - datetime
      - metadata
      - status
      - type
      type: object
      properties:
        type:
          type: string
          enum:
          - final_reminder
        status:
          type: string
          enum:
          - scheduled
          - running
          - completed
          - failed
          - skipped
          - canceled
        datetime:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_datetime'
        metadata:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_metadata'
    surveyLaunchSettings_survey_launch_settings_launch_plan_steps_closures:
      required:
      - datetime
      - metadata
      - status
      - type
      type: object
      properties:
        type:
          type: string
          enum:
          - close
          - reopen
        status:
          type: string
          enum:
          - scheduled
          - running
          - completed
          - failed
          - skipped
          - canceled
        datetime:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_datetime'
        metadata:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_metadata'
    surveyLaunchSettings_survey_launch_settings_launch_plan_steps:
      required:
      - closures
      - communicated_closure_offset
      - final_reminders
      - first_reminders
      - launches
      type: object
      properties:
        launches:
          type: array
          items:
            $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_launches'
        first_reminders:
          type: array
          items:
            $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_first_reminders'
        final_reminders:
          type: array
          items:
            $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_final_reminders'
        closures:
          type: array
          items:
            $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps_closures'
        communicated_closure_offset:
          type: integer
          example: 0
    surveyLaunchSettings_survey_launch_settings:
      required:
      - account_id
      - allow_raw_data_extract
      - comments_reporting_group_minimum
      - demographic_count
      - invited_count
      - launch_plan_steps
      - participant_count
      - pending_count
      - protection_level
      - question_count
      - questions_without_factors_count
      - reporting_group_minimum
      - status
      - survey_can_send_sms
      type: object
      properties:
        survey_can_send_sms:
          type: boolean
          description: Indicates if SMS is enabled for the survey
          example: true
        name:
          type: string
          example: Survey name
        account_id:
          type: string
          example: 5be2582d86723507e1a0707e3
        participant_count:
          type: integer
          example: 0
        pending_count:
          type: integer
          example: 0
        invited_count:
          type: integer
          example: 0
        status:
          type: string
          example: closed
          enum:
          - closed
          - active
          - archived
          - design
        question_count:
          type: integer
          example: 20
        demographic_count:
          type: integer
          example: 2
        questions_without_factors_count:
          type: integer
          example: 0
        reporting_group_minimum:
          type: integer
          example: 5
        comments_reporting_group_minimum:
          type: integer
          example: 5
        protection_level:
          type: string
          example: individuals
          enum:
          - individuals
          - "off"
          - minimum_reporting_group
        allow_raw_data_extract:
          type: boolean
          example: false
        launch_plan_steps:
          $ref: '#/components/schemas/surveyLaunchSettings_survey_launch_settings_launch_plan_steps'
    surveyLaunchSettings_actions:
      required:
      - cancel_launch
      - close
      - reopen
      - resend_reminder
      - schedule
      type: object
      properties:
        schedule:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/survey_scheduler/schedule
        cancel_launch:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/survey_scheduler/cancel_launch
        close:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/survey_scheduler/close
        reopen:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/survey_scheduler/reopen
        resend_reminder:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/survey_scheduler/resend_reminder
    surveyLaunchSettings_links:
      required:
      - capture_preview
      - comment_reply_admins
      - comments_settings
      - communications
      - demographics
      - factors
      - import_questions_select_file
      - participant
      - reporting_rules
      - reports
      type: object
      properties:
        participant:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/participant
        capture_preview:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/design/demonstration
        demographics:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/design/edit?question-type=segment
        communications:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/communication_designs
        reporting_rules:
          type: string
          format: uri
          example: /app/survey-configuration/d572b2d1-43ae-4ab0-8b2b-127606e4fd17/survey/6671d301-5bdc-4b2e-ad7b-8fb3ea38e478/confidentiality
        factors:
          type: string
          format: uri
          example: /app/survey-configuration/d572b2d1-43ae-4ab0-8b2b-127606e4fd17/survey/6671d301-5bdc-4b2e-ad7b-8fb3ea38e478/reporting-factors#configure-factors
        reports:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/reports/admin/insight_report?locale=en
        comments_settings:
          type: string
          format: uri
          example: /surveys/6745482b14620824031ee087/report_settings/edit
        comment_reply_admins:
          type: string
          format: uri
          example: /app/roles/accounts/d572b2d1-43ae-4ab0-8b2b-127606e4fd17
        import_questions_select_file:
          type: string
          format: uri
          example: /app/survey-configuration/d572b2d1-43ae-4ab0-8b2b-127606e4fd17/survey/6671d301-5bdc-4b2e-ad7b-8fb3ea38e478/import-questions/select-file
    surveyLaunchSettings_commentRepliesSection:
      type: object
      properties:
        repliesEnabled:
          type: boolean
          example: false
        replierCount:
          type: integer
          example: 5
        canAdministerAccount:
          type: boolean
          example: false
      nullable: true
    surveyLaunchSettings_last_bulk_upload_props:
      required:
      - account_users_url
      - last_bulk_import_within_three_months
      type: object
      properties:
        last_bulk_import_within_three_months:
          type: string
          nullable: true
        account_users_url:
          type: string
          format: uri
          example: /account/670854641462080bba82824b/users
    surveyLaunchSettings_amplitude_data:
      required:
      - Closed at date
      - Comments reporting group minimum
      - Created at date
      - Index factor
      - Internal comparisons loaded
      - Internal comparisons shown
      - Kiosk mode
      - Launched at date
      - Likert colour
      - Locales enabled
      - Number of invitations
      - Number of locales enabled
      - Percent of users invited
      - Raw data extract status
      - Reporting group minimum
      - Status
      - Subtype
      - Survey feature flags enabled
      - Survey launch method
      - Survey template aggregate ID selected
      - Survey template name selected
      - Survey template type selected
      - Type
      - eNPS enabled
      type: object
      properties:
        Status:
          type: string
          example: closed
          enum:
          - closed
          - active
          - archived
          - design
        Type:
          type: string
          example: engagement
        Subtype:
          type: string
          example: snapshot
        Survey template type selected:
          type: string
          example: quick_engagement_survey
        Survey template name selected:
          type: string
          nullable: true
        Survey template aggregate ID selected:
          type: string
          nullable: true
        Raw data extract status:
          type: string
          example: disabled
        Number of invitations:
          type: integer
          nullable: true
          example: 0
        Percent of users invited:
          type: number
          format: float
          nullable: true
          example: 0.0
        Survey launch method:
          type: string
          example: snapshot_launch
        Reporting group minimum:
          type: integer
          example: 5
        Comments reporting group minimum:
          type: integer
          example: 5
        Survey feature flags enabled:
          type: array
          example:
          - action_dashboard_report
          - action_driver
          - action_due_dates
          items:
            type: string
        Created at date:
          type: string
          format: date-time
          example: 2024-11-26T04:01:47Z
        Launched at date:
          type: string
          format: date-time
          example: 2024-11-26T04:15:02Z
        Closed at date:
          type: string
          format: date-time
          example: 2024-11-26T04:20:48Z
        Internal comparisons loaded:
          type: integer
          example: 0
        Internal comparisons shown:
          type: integer
          example: 0
        Locales enabled:
          type: array
          example:
          - en
          items:
            type: string
        Number of locales enabled:
          type: integer
          example: 1
        Index factor:
          type: string
          example: Engagement
        eNPS enabled:
          type: string
          example: No eNPS question selected
        Kiosk mode:
          type: string
          example: inactive
        Likert colour:
          type: string
          example: classical
    launchSchedulerBody_datetime:
      type: object
      properties:
        year:
          type: integer
          description: The year of the step datetime.
        month:
          type: integer
          description: The month of the step datetime.
        day:
          type: integer
          description: The day of the step datetime.
        hour:
          type: integer
          description: The hour of the step datetime.
        minute:
          type: integer
          description: The minute of the step datetime.
        second:
          type: integer
          description: The second of the step datetime.
        timezone:
          type: string
          description: "The timezone for the step datetime (e.g., \"Australia/Melbourne\"\
            )."
      description: The datetime when the step should occur.
      nullable: true
    launchSchedulerBody_launch_plan_steps:
      required:
      - datetime
      - step
      type: object
      properties:
        step:
          type: string
          description: "The name of the step (e.g., \"launch\", \"close\", \"reminder\"\
            , \"\" etc.)"
        datetime:
          $ref: '#/components/schemas/launchSchedulerBody_datetime'
    Variables_demographicFilters:
      type: object
      properties:
        direct:
          type: string
          description: A comma-separated list of dash-separated tuples which represents
            the selected demographic and its value
          example: "demographicId1-optionId1,demographicId1-optionId2"
        hierarchy:
          type: string
          description: Anchor filter of the selected report
          example: 6321266f04bd970066aa885c-5e09f5e073357d001b58a738
    TrendData_comparison_scores:
      type: object
      properties:
        factorId:
          type: string
          example: 5ea28f9d62c77c0029583837
        score:
          type: number
          example: 70
        __typename:
          type: string
          example: ComparisonScore
    TrendData_comparison:
      type: object
      properties:
        name:
          type: string
          example: New Tech (500-1000)
        scores:
          type: array
          items:
            $ref: '#/components/schemas/TrendData_comparison_scores'
        __typename:
          type: string
          example: Comparison
      nullable: true
    TrendData_scoreSummary:
      type: object
      properties:
        favorable:
          type: number
          example: 70
        neutral:
          type: number
          example: 20
        unfavorable:
          type: number
          example: 10
        __typename:
          type: string
          example: ScoreSummary
      nullable: true
    TrendData_scores:
      type: object
      properties:
        id:
          type: string
          description: |
            A composite identifier combining the survey ID and the target ID, separated by an underscore (`_`).
            - `survey_id`: The unique identifier for the survey.
            - `target_id`: The unique identifier for the target (e.g., factor or question).
          example: 5ea28f9d62c77c0029583827_5ea28f9d62c77c0029583837
        name:
          type: string
          example: Employee Engagement Survey 2024
        closedOn:
          type: string
          format: date-time
          example: 2020-05-02T07:00:00Z
        scoreSummary:
          $ref: '#/components/schemas/TrendData_scoreSummary'
        __typename:
          type: string
          example: Score
    Errors_inner:
      type: object
      properties:
        message:
          type: string
          example: automatic_comparisons_not_supported
        locations:
          type: array
          nullable: true
          items:
            type: object
            properties:
              line:
                type: integer
                example: 2
              column:
                type: integer
                example: 3
        path:
          type: array
          nullable: true
          items:
            type: string
            example: comparison
        extensions:
          type: object
          properties:
            value:
              type: string
              nullable: true
              example: null
            problems:
              type: array
              items:
                type: object
                properties:
                  explanation:
                    type: string
                    example: Expected value to not be null
          nullable: true
    FactorInsightReportData_factor_privacy_impact:
      type: object
      properties:
        impact:
          type: string
          example: LOW
        protected_groups:
          type: array
          example: []
          items:
            type: string
    FactorInsightReportData_factor_score:
      type: object
      properties:
        decimal_score:
          type: number
          nullable: true
          example: 0.4
        favorable:
          maximum: 100
          minimum: 0
          type: integer
          example: 40
        unfavorable:
          maximum: 100
          minimum: 0
          type: integer
          example: 20
    FactorInsightReportData_factor_scores:
      type: object
      properties:
        id:
          type: string
          example: 51f5de90db4d0872fb000014
        name:
          type: string
          example: 6-10 years
        companies:
          type: number
          nullable: true
          example: null
        population:
          type: integer
          example: 18
        score:
          $ref: '#/components/schemas/FactorInsightReportData_factor_score'
    FactorInsightReportData_factor_scoresByDemographic:
      type: object
      properties:
        id:
          type: string
          example: 66e2413efe8f2a003ed68807
        name:
          type: string
          example: Tenure
        privacy_impact:
          $ref: '#/components/schemas/FactorInsightReportData_factor_privacy_impact'
        scores:
          type: array
          items:
            $ref: '#/components/schemas/FactorInsightReportData_factor_scores'
    FactorInsightReportData_factor:
      type: object
      properties:
        id:
          type: string
          example: 5879be59625f15007e001a8f
        name:
          type: string
          example: Engagement
        population:
          type: integer
          example: 46
        deltaScore:
          type: integer
          nullable: true
          example: -5
        companies:
          type: number
          nullable: true
          example: null
        hasDemographicReport:
          type: boolean
          example: true
        score:
          $ref: '#/components/schemas/Score'
        scoresByDemographic:
          type: array
          items:
            $ref: '#/components/schemas/FactorInsightReportData_factor_scoresByDemographic'
    Comparison_comparisonTypes:
      type: object
      properties:
        id:
          type: string
          example: benchmark
          enum:
          - benchmark
          - survey
          - automatic
          - current_survey
        label:
          type: string
          example: Benchmark
    Comparison_previousSurveys:
      type: object
      properties:
        name:
          type: string
    Comparison_accountId:
      type: object
      properties:
        $oid:
          type: string
          example: 57145ecef4761aff35000008
    Comparison_comparisonOptions:
      type: object
      properties:
        id:
          type: string
          example: 5879bea8625f15007e00620d
        label:
          type: string
          example: New tech 2015
        type:
          type: string
          example: benchmark
          enum:
          - benchmark
          - survey
          - automatic
          - current_survey
        additionalInformation:
          type: string
        comparisonTypes:
          type: array
          items:
            $ref: '#/components/schemas/Comparison_comparisonTypes'
        hideComparison:
          type: boolean
          example: true
        previousSurveys:
          type: array
          example: []
          items:
            $ref: '#/components/schemas/Comparison_previousSurveys'
        userName:
          type: string
          example: Super User
        userEmail:
          type: string
          example: <EMAIL>
        accountId:
          $ref: '#/components/schemas/Comparison_accountId'
        accountAggregateId:
          type: string
          example: 0d0e1fe2-d7ac-4c67-b498-102e275e2395
        accountName:
          type: string
          example: Hooli
        canManageComparisons:
          type: boolean
          example: true
        canSelfServiceComparisons:
          type: boolean
          example: true
        automaticComparisonsV2Enabled:
          type: boolean
          example: true
        surveyId:
          type: string
          example: 57145ed8f4761aff35000e9c
        surveyName:
          type: string
          example: Hooli's Engagement Survey
        isLifecycleSurvey:
          type: boolean
          example: false
        isSharedReport:
          type: boolean
          example: false
    FilterPrivacyFailure_reasons:
      type: object
      properties:
        minimum_reporting_group:
          type: integer
          example: 5
    FilterPrivacyFailure_reasons_1_protected_group:
      type: object
      properties:
        population:
          type: integer
        demographics:
          type: object
          additionalProperties:
            type: string
            nullable: true
    FilterPrivacyFailure_reasons_1_violations:
      type: object
      properties:
        disallowed_filter:
          type: object
          additionalProperties:
            type: string
            nullable: true
        protected_group:
          $ref: '#/components/schemas/FilterPrivacyFailure_reasons_1_protected_group'
    FilterPrivacyFailure_reasons_1:
      type: object
      properties:
        minimum_triangulatable_group:
          type: integer
        violations:
          type: array
          items:
            $ref: '#/components/schemas/FilterPrivacyFailure_reasons_1_violations'
    sectionSpecifications_nameTranslations:
      required:
      - locale
      - text
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      example: "[{text=Section 1, locale=en}, {text=Sección 1, locale=es}]"
    sectionSpecifications_longDescription:
      required:
      - locale
      - text
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      example: "[{text=This section is intended to measure overall satisfaction.,\
        \ locale=en}, {text=Esta sección tiene como objetivo medir la satisfacció\
        n general., locale=es}]"
    sectionSpecifications_shortDescription:
      required:
      - locale
      - text
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      example: "[{text=Overall, locale=en}, {text=General, locale=es}]"
    factorSpecifications_nameTranslations:
      required:
      - locale
      - text
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      description: Translations of the factor name in different languages.
      example: "[{text=Factor 1, locale=en}, {text=Factor 1, locale=es}]"
    questionSpecifications_descriptionTranslations:
      required:
      - locale
      - text
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      example: "[{text=Question 1, locale=en}, {text=Pregunta 1, locale=es}]"
    questionSpecifications_otherOption:
      type: object
      properties:
        valueTranslations:
          type: array
          description: The text of the question in different locales.
          items:
            $ref: '#/components/schemas/questionSpecifications_descriptionTranslations'
      description: The other option for the select question.
    questionDisplayConditionSpecifications_answerBranchingRule:
      type: object
      properties:
        id:
          type: string
          description: The aggregate ID of the display condition that this rule based
            on.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        parentQuestionId:
          type: string
          description: The aggregate ID of the parent question that this rule based
            on.
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
        selectOptionIds:
          type: array
          description: The aggregate IDs of the select options (answers) that this
            rule based on.
          items:
            type: string
            format: uuid
            example: 4b492219-3915-42cf-9cef-ca5e930c97cb
      description: "Answer-based rules, also known as answer-based branching, provide\
        \ a personalized survey experience by allowing admin to show or hide questions\
        \ to participants depending on their answer to a previous question. https://support.cultureamp.com/en/articles/7048354-use-answer-branching-rules-in-a-survey"
    SftpIntegration_last_data_sync:
      type: object
      properties:
        status:
          type: string
          enum:
          - success
          - success_with_ignored_data
          - blocked
          - failed
          - in_progress
        date:
          type: string
        summary_link:
          type: string
    HrisIntegration_companies:
      type: object
      properties:
        company_id:
          type: string
    factor_name:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Factor Name
    questions_description:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Question Description
    questions_short_text:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Short Text
    questions_custom_other_option_label:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Other Option Label
    questions_value:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Option Value
    questions_select_options:
      required:
      - id
      - status
      - value
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        status:
          type: string
          example: active
          enum:
          - deleted
          - active
        value:
          type: array
          items:
            $ref: '#/components/schemas/questions_value'
    questions_branching_conditions:
      required:
      - condition_id
      - demographic_id
      - matcher_type
      type: object
      properties:
        condition_id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        demographic_id:
          type: string
          nullable: true
          example: demographic1
        select_option_ids:
          type: array
          example:
          - option1
          items:
            type: string
        matcher_type:
          type: string
          example: standard
          enum:
          - standard
          - inverted
    questions_answer_based_branching_condition:
      required:
      - id
      - question_id
      - select_option_ids
      type: object
      properties:
        id:
          type: string
          format: uuid
          nullable: true
          example: 123e4567-e89b-12d3-a456-************
        question_id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        parent_id:
          type: string
          format: uuid
          nullable: true
          example: 123e4567-e89b-12d3-a456-************
        select_option_ids:
          type: array
          example:
          - option1
          items:
            type: string
      nullable: true
    sections_name:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Section Name
    sections_description_long:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Long Description
    sections_description_short:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Short Description
    sections_description:
      required:
      - long
      - short
      type: object
      properties:
        long:
          type: array
          items:
            $ref: '#/components/schemas/sections_description_long'
        short:
          type: array
          items:
            $ref: '#/components/schemas/sections_description_short'
    accountDemographics_name:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Account Name
    accountDemographics_value:
      required:
      - locale
      - value
      type: object
      properties:
        locale:
          type: string
          example: en
        value:
          type: string
          example: Demographic Value
    accountDemographics_demographic_values:
      required:
      - demographic_value_id
      - status
      - value
      type: object
      properties:
        demographic_value_id:
          type: string
          format: uuid
          example: 9d4061b7-6d54-4947-a294-7aefa3afeb71
        value:
          type: array
          items:
            $ref: '#/components/schemas/accountDemographics_value'
        status:
          type: string
          example: active
          enum:
          - deleted
          - active
    RatingQuestion_overallScore:
      type: object
      properties:
        favorable:
          maximum: 100
          minimum: 0
          type: integer
          example: 75
        unfavorable:
          maximum: 100
          minimum: 0
          type: integer
          example: 12
        decimal_score:
          type: number
          example: 0.****************
    RatingQuestion_correlation:
      type: object
      properties:
        score:
          type: number
          nullable: true
          example: 0.**********
        type:
          type: string
          nullable: true
          example: tau-c
          enum:
          - tau-c
          - pearson
          - null
        population:
          type: integer
          nullable: true
          example: 37
    RatingQuestion_path:
      type: object
      properties:
        question_path:
          type: string
          description: Path to the question insight report for the question
          example: /surveys/57145ed8f4761aff35000e9c/reports/admin/question_report/5879be59625f15007e001a9c
        comments_path:
          type: string
          description: Path to the comment report for the question
          example: /surveys/57145ed8f4761aff35000e9c/reports/admin/comments?questionId=5879be59625f15007e001a9b
    Question_1_scores:
      type: object
      properties:
        id:
          type: string
          example: 64912d32391fd9000db7fcc4
        name:
          type: string
          example: 5 - Sets a New Standard
        companies:
          type: number
          nullable: true
          example: null
        population:
          type: integer
          example: 126
        score:
          $ref: '#/components/schemas/Score'
    Question_1_scoresByDemographic:
      type: object
      properties:
        id:
          type: string
          example: 649bc4bd1e215f0028ed55aa
        name:
          type: string
          example: Performance Rating
        privacy_impact:
          $ref: '#/components/schemas/FactorInsightReportData_factor_privacy_impact'
        scores:
          type: array
          items:
            $ref: '#/components/schemas/Question_1_scores'
    demographic_descriptionTranslations:
      required:
      - locale
      - text
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      example: "[{text=Demographic 1, locale=en}, {text=Pregunta 1, locale=es}]"
    selectOptionSpecifications_valueTranslations:
      required:
      - locale
      - text
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
  parameters:
    surveyAggregateId:
      name: surveyAggregateId
      in: path
      description: An aggregateId (UUID) that identifies a given survey
      required: true
      schema:
        type: string
        format: uuid
      example: 06e7d444-f999-4f46-952d-7a7cb55c40a0
    performanceCycleAggregateId:
      name: performanceCycleAggregateId
      in: path
      description: An aggregate id (UUID) that identifies a performance rating cycle
      required: true
      schema:
        type: string
        format: uuid
      example: 9d89f6c0-3dae-4c9f-82f2-8069d7b7d9b1
    stqId:
      name: stqId
      in: path
      description: "The active record instance/object's mongo id of the question to\
        \ retrieve. \nThis is the STQ (survey to question) id, rather than the question\
        \ id."
      required: true
      schema:
        type: string
    reportId:
      name: reportId
      in: path
      description: The active record instance/object's mongo id of the report for
        a given survey
      required: true
      schema:
        type: string
    surveyId:
      name: surveyId
      in: path
      description: The active record instance/object's mongo id for the survey
      required: true
      schema:
        type: string
    locale:
      name: locale
      in: query
      description: "The language to use to use when providing localisable data, e.g.\
        \ question label."
      required: true
      schema:
        type: string
        example: en
    anchor:
      name: anchor
      in: query
      description: "This is the anchor filter - an un-removable demographic filter\
        \ imposed as part of a report. \n\nA single tuple stq id - select option id\
        \ pair, where the stq represents the demographic, and the the select option\
        \ represents the demographic value.\n\nOptionally, in certain administrator\
        \ scenarios, this anchor can be sent the special value \"ALL_RESULTS\".\n\n\
        All supplied ids should be mongo style bson ids. "
      required: false
      schema:
        type: string
        example: demographicId1-optionId1
    filters:
      name: filters
      in: query
      description: |-
        A comma-separated list of dash-seperated tuples, with each tuple being a stq id - select option id pair, where the stq represents the demographic, and the the select option represents the demographic value.

        In situations where there are multiple values selected for a particular demographic, multiple tuples with the same demographic id should be provided.

        All supplied ids should be mongo style bson ids.
      required: false
      schema:
        type: string
        example: "demographicId1-optionId1,demographicId1-optionId2,demographicId3-optionId7"
    comparison:
      name: comparison
      in: query
      description: |-
        Indicate a target to compare survey results against. This could be the overall survey, a benchmark, or another survey.

        This parameter controls what is returned as part of comparisonDeltaScore

        The value is a magical string, which indicates the type and source of comparison. Some examples:

        .overall - compare to the overall survey

        .auto_trend.<survey id> - compare to another survey

        <a summary bson id> - compare against a survey or benchmark.
      required: false
      schema:
        type: string
        example: .overall
    preview:
      name: preview
      in: query
      description: "Supply fake response data, used for testing reports before response\
        \ data is available."
      required: false
      schema:
        type: boolean
    dismissed:
      name: dismissed
      in: query
      description: Filters returned job collection to only those that have not been
        dismissed by the user.
      required: false
      schema:
        type: boolean
        example: false
    backgroundJobId:
      name: backgroundJobId
      in: path
      description: The id for the background job that is executing the report sharing
        permissions import
      required: true
      schema:
        type: string
    limit:
      name: limit
      in: query
      description: A maximum number of results to fetch
      required: false
      schema:
        type: integer
        example: 250
    cursor:
      name: cursor
      in: query
      description: A string representing an id that points to the next page of results.
        This should have been sent in the response to a previous request.
      required: false
      schema:
        type: string
        example: NTg3OWJlNWI2MjVmMTUwMDdlMDAxYmY3
    start_date:
      name: start_date
      in: query
      description: The start date to filter responses by
      required: false
      schema:
        type: string
        format: date-time
        example: 2010-01-01T06:00:00.999Z
    end_date:
      name: end_date
      in: query
      description: End date to filter the responses by
      required: false
      schema:
        type: string
        format: date-time
        example: 2010-01-01T06:00:00.999Z
    employee_id:
      name: employeeId
      in: path
      description: An aggregate id (UUID v4) that identifies a given employee
      required: true
      schema:
        type: string
      example: dbb1c7c2-0238-4f4b-a318-829c1513327c
    account_id:
      name: accountId
      in: path
      description: An aggregate id (UUID v4) that identifies a given account
      required: true
      schema:
        type: string
      example: dbb1c7c2-0238-4f4b-a318-829c1513327c
    account_aggregate_id:
      name: account_id
      in: query
      description: An aggregate id (UUID v4) that identifies a given account
      required: true
      schema:
        type: string
      example: dbb1c7c2-0238-4f4b-a318-829c1513327c
    permissions:
      name: "permissions[]"
      in: query
      description: An array of permissions to check
      required: false
      schema:
        type: array
        items:
          type: string
      example: administration.account.roles.administer
    demographic_id:
      name: demographicId
      in: path
      description: An aggregate id (UUID v4) that identifies a given demographic
      required: true
      schema:
        type: string
      example: dbb1c7c2-0238-4f4b-a318-829c1513327c
    aggregateId:
      name: aggregateId
      in: path
      description: Aggregate ID for the bulk remove grants process
      required: true
      schema:
        type: string
        format: uuid
    kioskKey:
      name: kiosk_key
      in: query
      description: The kiosk key for the survey
      schema:
        type: string
      example: abcd123
    code:
      name: code
      in: query
      description: The user unique identifier either an employee_id or the kiosk_code
      schema:
        type: string
      example: ******** or 7KHCAG39
    surveyMongoId:
      name: surveyMongoId
      in: path
      description: A MongoID that identifies a given survey
      required: true
      schema:
        type: string
      example: 663ad28514620849447123c4
    participantImportId:
      name: participantImportId
      in: path
      description: A MongoID that identifies a given participant import
      required: true
      schema:
        type: string
      example: 663ad28514620849447123c4
    anchor_filters:
      name: a
      in: query
      description: "This contains the anchor filters - un-removable demographic filters\
        \ imposed as part of a report. There can be single or multiple anchor filters\
        \ present.\n\nThe comma separated tuples of stq id - select option id pairs,\
        \ where the stq represents the demographic, and the the select option represents\
        \ the demographic value.\n\nOptionally, in certain administrator scenarios,\
        \ this anchor can be sent the special value \"ALL_RESULTS\".\n\nAll supplied\
        \ ids should be mongo style bson ids. "
      required: false
      schema:
        type: string
        example: "demographicId1-optionId1,demographicId2-optionId2"
    from:
      name: from
      in: query
      description: The start date to filter responses by. The Date is in the format
        "YYYY-M-D"
      required: false
      schema:
        type: string
        example: 2023-2-1
    selected_locale:
      name: locale
      in: query
      description: "The language to use to use when providing localisable data, e.g.\
        \ question label."
      required: false
      schema:
        type: string
        example: en
    selected_leader:
      name: selected_leader
      in: query
      description: The select option ID of the selected leader
      required: false
      schema:
        type: string
        example: 655c2a5345e41d0051254857-64912d2b391fd9000db7fba7
    to:
      name: to
      in: query
      description: End date to filter the responses by. The Date is in the format
        "YYYY-M-D"
      required: false
      schema:
        type: string
        example: 2023-2-24
    factorId:
      name: factorId
      in: path
      description: The active record instance/object's mongo id of the factor
      required: true
      schema:
        type: string
    status:
      name: status
      in: query
      description: The status of the survey
      required: false
      schema:
        type: string
        example: closed
    term:
      name: term
      in: query
      description: The search term used to filter the survey responses
      required: false
      schema:
        type: string
    questionId:
      name: questionId
      in: path
      description: The active record instance/object's mongo id of the question
      required: true
      schema:
        type: string
    accountAggregateId:
      name: accountAggregateId
      in: path
      description: An aggregateId (UUID) that identifies a given account
      required: true
      schema:
        type: string
        format: uuid
      example: 2c998a19-7acd-44be-8cb3-b15d03f64aa1
    userId:
      name: X-User-Id
      in: header
      description: The ID of the user making the request.
      required: true
      schema:
        type: string
        format: uuid
        example: 1f492219-3915-42cf-9cef-ca5e930c97cb
    correlationId:
      name: X-Correlation-Id
      in: header
      description: Correlation ID of the request.
      required: true
      schema:
        type: string
        format: uuid
        example: 2f492219-3915-42cf-9cef-ca5e930c97cb
  examples:
    runningExample:
      value:
        status: running
        progress: 50
    doneExample:
      value:
        status: done
        progress: 100
    errorExample:
      value:
        status: error
