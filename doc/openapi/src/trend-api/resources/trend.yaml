post:
  summary: Execute GraphQL queries for comparison and survey trends
  operationId: trendGraphQL
  description: Endpoint to execute GraphQL queries to retrieve trend-related data, including comparisons and survey trends
  tags:
    - Trend
  parameters:
    - $ref: "../parameters/path/surveyId.yaml"
    - $ref: "../parameters/path/reportId.yaml"
    - $ref: "../parameters/query/anchor_filters.yaml"
    - $ref: "../parameters/query/comparison.yaml"
    - $ref: "../parameters/query/filters.yaml"
    - $ref: "../parameters/query/from.yaml"
    - $ref: "../parameters/query/selected_locale.yaml"
    - $ref: "../parameters/query/preview.yaml"
    - $ref: "../parameters/query/selected_leader.yaml"
    - $ref: "../parameters/query/to.yaml"
  requestBody:
    content:
      application/json:
        schema:
          type: array
          items:
            type: object
            properties:
              operationName:
                type: string
                example: SurveyTrendScores
              query:
                type: string
                example: |
                  query Comparison($surveyId: ID!, $comparisonId: ID!) {
                    comparison(surveyId: $surveyId, comparisonId: $comparisonId) {
                      name
                      scores {
                        factorId
                        score
                      }
                    }
                  }
              variables:
                $ref: "../schemas/Variables.yaml"
            required:
              - operationName
              - query
              - variables
  responses:
    '200':
      description: Expected response to a valid request
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                data:
                  $ref: "../schemas/TrendData.yaml"
                errors:
                  $ref: "../schemas/Errors.yaml"
    "400": 
      description: Bad Request. The client provided invalid input.
    "401":
      description: Unauthorized. The user is not authenticated or the authentication token is invalid.
    "403":
      description: Permission denied. User cannot access the survey or report.
    "404":
      description: Not Found. The requested survey, report, or comparison does not exist.
    "500":
      description: Internal server error.
    


