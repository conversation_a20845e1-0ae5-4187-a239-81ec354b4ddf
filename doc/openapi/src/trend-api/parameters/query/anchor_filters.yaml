name: a
in: query
required: false
description: |-
  This contains the anchor filters - un-removable demographic filters imposed as part of a report. There can be single or multiple anchor filters present.
  
  The comma separated tuples of stq id - select option id pairs, where the stq represents the demographic, and the the select option represents the demographic value.

  Optionally, in certain administrator scenarios, this anchor can be sent the special value "ALL_RESULTS".
  
  All supplied ids should be mongo style bson ids. 
schema:
  type: string
  example: demographicId1-optionId1,demographicId2-optionId2
