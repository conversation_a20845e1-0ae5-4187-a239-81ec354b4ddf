get:
  summary: Get survey structure
  operationId: getSurveyStructure
  description: Retrieves metadata describing a survey.
  tags:
    - Multi Account Surveying
  parameters:
    - $ref: "../parameters/path/accountAggregateId.yaml" # Account ID
    - $ref: "../parameters/path/surveyAggregateId.yaml" # Survey ID
    - $ref: "../parameters/header/userId.yaml" # User ID
  responses:
    '200':
      description: Successful response
      content:
        application/json:
          schema:
            $ref: '../schemas/surveyStructure.yaml'
    "400":
      description: Bad Request. The client provided invalid input.
    "401":
      description: Unauthorized. The user is not authenticated or the authentication token is invalid.
    "403":
      description: Permission denied. User cannot access the survey or report.
    "404":
      description: Not Found. The requested survey, report, or question does not exist.
    "500":
      description: Internal server error.