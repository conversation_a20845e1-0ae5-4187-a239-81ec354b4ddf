post:
  summary: Upsert question display condition
  operationId: upsertQuestionDisplayCondition
  description: Create or update a question display condition for the specified subsidiary survey.
  tags:
    - Multi Account Surveying
  parameters:
    - $ref: "../parameters/path/accountAggregateId.yaml" # Subsidiary account ID
    - $ref: "../parameters/path/surveyAggregateId.yaml" # Subsidiary survey ID
    - $ref: "../parameters/header/userId.yaml" # User ID
    - $ref: "../parameters/header/correlationId.yaml" # Correlation ID
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            questionDisplayConditionSpecifications:
              $ref: "../schemas/questionDisplayConditionSpecifications.yaml"
          required:
            - questionDisplayConditionSpecifications
  responses:
    "201":
      description: Created or updated successfully.
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: string
                description: The Mongo ID of the question.
                example: 67ff0b44437b38a664cd7a37
              aggregateId:
                type: string
                description: The Aggregate ID of the question.
                example: 4b492219-3915-42cf-9cef-ca5e930c97cb
                format: uuid

            required:
              - id
              - aggregateId
    "400":
      description: Bad Request. The client provided invalid input.
    "401":
      description: Unauthorized. The user is not authenticated or the authentication token is invalid.
    "403":
      description: Permission denied. User cannot access the survey.
    "404":
      description: Not Found. The requested survey does not exist.
    "500":
      description: Internal server error.
