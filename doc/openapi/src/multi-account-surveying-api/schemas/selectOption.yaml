type: object
properties:
  selectOptionId:
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  selectOptionMongoId:
    type: string
    example: 67ff0b44437b38a664cd7a37
  valueTranslations:
    type: array
    description: The "value" of the select option"
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required:
        - text
        - locale
    example:
      - text: Option 1
        locale: en
      - text: Opción 1
        locale: es
  labelTranslations:
    type: array
    description: The "label" of the select option
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required:
        - text
        - locale
    example:
      - text: Option 1
        locale: en
      - text: Opción 1
        locale: es
  sortTerm:
    type: object
    example:
      en: "00001"
    nullable: true
required:
  - selectOptionId
  - selectOptionMongoId
  - valueTranslations
  - labelTranslations
  - sortTerm
