type: object
properties:
  id:
    type: string
    description: The Aggregate ID of this survey
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  mongoId:
    type: string
    description: The Mongo ID of the survey, used for internal references.
    example: 67ff0b44437b38a664cd7a37
  accountId:
    type: string
    description: The aggregate ID of the account which owns this survey.
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  accountMongoId:
    type: string
    description: The Mongo ID of the account which owns this survey.
    example: 67ff0b44437b38a664cd7a37
  configuration:
    type: object
    additionalProperties:
      type: string
    example:
      captureLikertColorSchema: classical
  flags:
    type: object
    additionalProperties:
      type: string
    example:
      improvedCommsConfiguration: enabled
  nameTranslations:
    type: object
    additionalProperties:
      type: string
    example:
      en: Quick Engagement Survey Apr 2025 v4
  description:
    type: object
    nullable: true
    additionalProperties:
      type: string
    example:
      en: Description for Quick Engagement Survey Apr 2025 v4
  launchedAt:
    type: string
    nullable: true
    format: date-time
    example: '2025-04-01T00:00:00Z'
  status:
    type: string
    enum:
      - design
      - active
      - closed
    example: design
  type:
    $ref: "../schemas/surveyType.yaml"
  surveyPeriodType:
    $ref: "../schemas/surveyPeriodType.yaml"
  isOrgSurvey:
    description: True if the survey is a central organisation survey
    type: boolean
  region:
    $ref: "../schemas/region.yaml"
  questions:
    type: array
    items:
      $ref: "../schemas/question.yaml"
  demographics:
    type: array
    items:
      $ref: "../schemas/demographic.yaml"
  factors:
    type: array
    items:
      $ref: "../schemas/factor.yaml"
  sections:
    type: array
    items:
      $ref: "../schemas/sectionSpecifications.yaml"

required:
  - id
  - mongoId
  - accountId
  - accountMongoId
  - configuration
  - flags
  - nameTranslations
  - description
  - launchedAt
  - status
  - type
  - surveyPeriodType
  - isOrgSurvey
  - region
  - questions
  - demographics
  - factors
  - sections
