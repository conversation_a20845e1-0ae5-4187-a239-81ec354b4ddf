type: object
properties:
  questionId:
    description: The aggregate ID of the question.
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  sectionId:
    description: The aggregate ID of the section which this question(stq) belongs.
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  factorIds:
    description: The aggregate ID of the factors which this question(stq) belongs.
    type: array
    items:
      type: string
      format: uuid
      example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  descriptionTranslations:
    description: The text of the question in different locales.
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      example:
        - text: Question 1
          locale: en
        - text: Pregunta 1
          locale: es
  questionType:
    description: The type of question.
    type: string
    enum:
      - free-text
      - rating
      - select
    example: rating
  ratingScale:
    description: The rating scale of the question.
    type: string
    enum:
      - agreement
      - rating
      - satisfaction
      - quality
      - frequency
      - frequency_alt
      - performance
      - importance
      - focus
    example: agreement
  selectOptions:
    description: The options for the select question. If the question is not a select question, this field should be null
    type: array
    items:
      $ref: "../schemas/selectOptionSpecifications.yaml"
    nullable: true
  otherOption:
    description: The other option for the select question.
    type: object
    properties:
      valueTranslations:
        description: The text of the question in different locales.
        type: array
        items:
          type: object
          properties:
            text:
              type: string
            locale:
              type: string
          required: [ text, locale ]
          example:
            - text: Question 1
              locale: en
            - text: Pregunta 1
              locale: es
  intendedPurpose:
    description: The intended purpose of the question.
    type: string
    enum:
      - classification
      - demographic
      - feedback
      - reviewer_notes
      - interview
      - outcome
    example: classification
  stqOrder:
    description: The order of the stq that belongs to this question.
    type: integer
    example: 1
    nullable: true
  selectionLimit:
    description: The selection limit of the select question. If the question is not a select question, this field should be null
    type: integer
    example: 3
    nullable: true
  isMandatory:
    description: The mandatory status of the question. The question can be either optional or mandatory. If the question is not mandatory, this field should be false.
    type: boolean
    example: true
required:
  - questionId
  - sectionId
  - factorIds
  - descriptionTranslations
  - questionType
  - ratingScale
  - selectOptions
  - intendedPurpose
  - stqOrder
  - selectionLimit
  - isMandatory

