type: object
properties:
  factorId:
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
    description: The aggregate ID of the factor.
  factorMongoId:
    type: string
    description: The Mongo ID of the factor.
    example: 67ff0b44437b38a664cd7a37
  nameTranslations:
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      description: Translations of the factor name in different languages.
      example:
        - text: Factor 1
          locale: en
        - text: Factor 1
          locale: es
  isIndexFactor:
    type: boolean
    example: true
    description: Indicates if the factor is an index factor.
  isKeyFactor:
    type: boolean
    example: true
    description: Indicates if the factor is a key factor.
required:
  - factorId
  - factorMongoId
  - nameTranslations
  - isIndexFactor
  - isKeyFactor
