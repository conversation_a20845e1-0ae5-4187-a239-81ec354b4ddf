type: object
properties:
  questionId:
    description: The aggregate ID of the question.
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  answerBranchingRule:
    description: Answer-based rules, also known as answer-based branching, provide a personalized survey experience by allowing admin to show or hide questions to participants depending on their answer to a previous question. https://support.cultureamp.com/en/articles/7048354-use-answer-branching-rules-in-a-survey
    type: object
    properties:
      id:
        description: The aggregate ID of the display condition that this rule based on.
        type: string
        format: uuid
        example: 4b492219-3915-42cf-9cef-ca5e930c97cb
      parentQuestionId:
        description: The aggregate ID of the parent question that this rule based on.
        type: string
        format: uuid
        example: 4b492219-3915-42cf-9cef-ca5e930c97cb
      selectOptionIds:
        description: The aggregate IDs of the select options (answers) that this rule based on.
        type: array
        items:
          type: string
          format: uuid
          example: 4b492219-3915-42cf-9cef-ca5e930c97cb
