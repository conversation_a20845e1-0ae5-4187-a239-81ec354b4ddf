type: object
properties:
  sectionId:
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
    description: The aggregate ID of the section.
  sectionMongoId:
    type: string
    description: The Mongo ID of the section.
    example: 67ff0b44437b38a664cd7a37
  position:
    type: string
    format: integer
    example: 1
    description: The position of the section in UI.
  nameTranslations:
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      example:
        - text: Section 1
          locale: en
        - text: Sección 1
          locale: es
  longDescription:
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      example:
        - text: This section is intended to measure overall satisfaction.
          locale: en
        - text: Esta sección tiene como objetivo medir la satisfacción general.
          locale: es
  shortDescription:
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required: [ text, locale ]
      example:
        - text: Overall
          locale: en
        - text: General
          locale: es
  intendedPurpose:
    type: string
    enum:
      - standard
      - demographic
    example: standard
required:
  - sectionId
  - sectionMongoId
  - position
  - nameTranslations
  - longDescription
  - shortDescription
  - intendedPurpose
