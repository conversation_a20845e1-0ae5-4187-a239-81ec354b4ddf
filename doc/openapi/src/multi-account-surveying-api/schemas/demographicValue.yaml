type: object
properties:
  demographicValueId:
    type: string
    format: uuid
    example: 4b492219-3915-42cf-9cef-ca5e930c97cb
  demographicValueMongoId:
    description: The mongo ID of this select option
    type: string
    example: 67ff0b44437b38a664cd7a37
  labelTranslations:
    description: The demographic value label localised
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required:
        - text
        - locale
    example:
      - text: Option 1
        locale: en
      - text: Opción 1
        locale: es
  valueTranslations:
    description: The demographic value localised
    type: array
    items:
      type: object
      properties:
        text:
          type: string
        locale:
          type: string
      required:
        - text
        - locale
    example:
      - text: Option 1
        locale: en
      - text: Opción 1
        locale: es
required:
  - demographicValueId
  - demographicValueMongoId
  - labelTranslations
  - valueTranslations
