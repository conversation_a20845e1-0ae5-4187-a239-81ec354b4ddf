openapi: '3.0.0'
info:
  version: 0.0.1
  title: Murmur APIs
  description: Specification/documentation for the endpoints available in murmur, starting with the new survey insight report and process for importing report sharing permissions using a v2 controller.
servers:
  - url: http://localhost:3030
    description: Web Gateway running as proxy
  - url: http://localhost:3000
    description: Directly to Murmur
  - url: /
    description: Directly to murmur for frontend compatibility with frontend-api lib
  - url: sgw://murmur
    description: Access to murmur via the service gateway protocol
paths:
  /api/murmur/employee_integration/app_data:
    $ref: ./api/murmur/employee_integration/resources/app_data.yaml
  /api/murmur/employee_integration/set_integration:
    $ref: ./api/murmur/employee_integration/resources/set_integration.yaml
  /api/murmur/employee_integration/disable_integration:
    $ref: ./api/murmur/employee_integration/resources/disable_integration.yaml
  /dimensions_api/v1/survey/{surveyAggregateId}/performance_rating_cycles:
    $ref: ./dimensions_api/resources/performance_rating_cycles.yaml
  /dimensions_api/v1/survey/{surveyAggregateId}/link_performance_rating_cycle/{performanceCycleAggregateId}:
    $ref: ./dimensions_api/resources/link_performance_rating_cycle.yaml
  /dimensions_api/v1/survey/{surveyAggregateId}/unlink_performance_rating_cycle/{performanceCycleAggregateId}:
    $ref: ./dimensions_api/resources/unlink_performance_rating_cycle.yaml
  /survey-insights-api/v1/survey/{surveyId}/report/{reportId}/question/{stqId}:
    $ref: ./survey-insights-api/resources/question.yaml
  /surveys/{surveyId}/import_report_sharing_permissions_file:
    $ref: ./import_report_sharing_permissions_file/resources/import_report_sharing_permissions_file.yaml
  /surveys/{surveyId}/import_report_sharing_permissions_file/{backgroundJobId}:
    $ref: ./import_report_sharing_permissions_file/resources/show.yaml
  /surveys/{surveyId}/validate_report_sharing_permissions_file:
    $ref: ./validate_report_sharing_permissions_file/resources/create.yaml
  /surveys/{surveyId}/validate_report_sharing_permissions_file/new:
    $ref: ./validate_report_sharing_permissions_file/resources/new.yaml
  /surveys/{surveyId}/validate_report_sharing_permissions_file/{backgroundJobId}:
    $ref: ./validate_report_sharing_permissions_file/resources/show.yaml
  /surveys/{surveyId}/validate_report_sharing_permissions_file/{backgroundJobId}/errors_download:
    $ref: ./validate_report_sharing_permissions_file/resources/errors_download.yaml
  /reporting_api/survey/{surveyId}/responses:
    $ref: ./reporting-api/resources/responses.yaml
  /roles_api/employees/{employeeId}/account_admin:
    $ref: ./roles_api/resources/account_admin.yaml
  /roles_api/employees/{employeeId}/survey_creator:
    $ref: ./roles_api/resources/survey_creator.yaml
  /roles_api/employees/{employeeId}/survey_data_analyst:
    $ref: ./roles_api/resources/survey_data_analyst.yaml
  /roles_api/employees/{employeeId}/survey_comment_replier:
    $ref: ./roles_api/resources/survey_comment_replier.yaml
  /roles_api/accounts/{accountId}/account_admins:
    $ref: ./roles_api/resources/account_admins.yaml
  /roles_api/accounts/{accountId}/survey_creators:
    $ref: ./roles_api/resources/survey_creators.yaml
  /roles_api/accounts/{accountId}/survey_data_analysts:
    $ref: ./roles_api/resources/survey_data_analysts.yaml
  /roles_api/accounts/{accountId}/survey_comment_repliers:
    $ref: ./roles_api/resources/survey_comment_repliers.yaml
  /roles_api/accounts/{accountId}/departments:
    $ref: ./roles_api/resources/departments.yaml
  /roles_api/permissions:
    $ref: ./roles_api/resources/permissions.yaml
  /roles_api/accounts/{accountId}/demographics:
    $ref: ./roles_api/resources/demographics.yaml
  /roles_api/accounts/{accountId}/demographics/{demographicId}/values:
    $ref: ./roles_api/resources/demographic_values.yaml
  /surveys/{surveyId}/bulk_remove_grants:
    $ref: ./bulk_remove_report_grants/resources/bulk_remove_grants.yaml
  /surveys/{surveyId}/bulk_remove_grants/{aggregateId}:
    $ref: ./bulk_remove_report_grants/resources/bulk_remove_grants_progress.yaml
  /survey_capture_api/{surveyAggregateId}/response_for_code:
    $ref: ./survey-capture-api/resources/response_for_code.yaml
  /survey_capture_api/{surveyAggregateId}/kiosk_page_data:
    $ref: ./survey-capture-api/resources/kiosk_page_data.yaml
  /surveys/{surveyAggregateId}/survey_designer/v1/survey_metadata:
    $ref: ./survey-designer-api/resources/survey_metadata.yaml
  /surveys/{surveyAggregateId}/survey_designer/rename_section:
    $ref: ./survey-designer-api/resources/rename_section.yaml
  /surveys/{surveyAggregateId}/survey_designer/survey_details:
    $ref: ./survey-designer-api/resources/survey_details.yaml
  /survey_participants_import_api/{surveyId}/survey_data:
    $ref: ./survey-participants-import-api/resources/survey_data.yaml
  /surveys/{surveyMongoId}/participant_import/{participantImportId}:
    $ref: ./survey-participants-import-api/resources/check_validation_status.yaml
  /surveys/{surveyMongoId}/participant_import:
    $ref: ./survey-participants-import-api/resources/upload_and_validate_file.yaml
  /surveys/{surveyMongoId}/participant_import_v2:
    $ref: ./survey-participants-import-api/resources/upload_and_validate_file_v2.yaml
  /surveys/{surveyAggregateId}/survey_launch_settings:
    $ref: ./snapshot-launch-api/resources/survey_launch_settings.yaml
  /surveys/{surveyAggregateId}/survey_scheduler/schedule:
    $ref: ./snapshot-launch-api/resources/survey_scheduler.yaml
  /surveys/{surveyAggregateId}/survey_scheduler/cancel_launch:
    $ref: ./snapshot-launch-api/resources/cancel_launch.yaml
  /surveys/{surveyAggregateId}/survey_scheduler/close:
    $ref: ./snapshot-launch-api/resources/close_survey.yaml
  /surveys/{surveyAggregateId}/survey_scheduler/reopen:
    $ref: ./snapshot-launch-api/resources/reopen_survey.yaml
  /surveys/{surveyAggregateId}/survey_scheduler/resend_reminder:
    $ref: ./snapshot-launch-api/resources/resend_reminder.yaml
  /surveys/{surveyId}/reports/{reportId}/trend/api/graphql:
    $ref: ./trend-api/resources/trend.yaml
  /survey_reporting_api/v1/surveys/{surveyId}/reports/{reportId}/report-data/factor-insight/{factorId}:
    $ref: ./survey-reporting-api/resources/factor-insight.yaml
  /survey_reporting_api/v1/surveys/{surveyId}/reports/{reportId}/report-data/question-insight/{questionId}:
    $ref: ./survey-reporting-api/resources/question-insight.yaml
  /multi_account_surveying_api/v1/org_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}:
    $ref: ./multi-account-surveying-api/resources/get_org_survey_data.yaml
  /multi_account_surveying_api/v1/surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}:
    $ref: ./multi-account-surveying-api/resources/get_survey_structure.yaml
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/upsert_survey:
    $ref: ./multi-account-surveying-api/resources/upsert_subsidiary_survey.yaml
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_section:
    $ref: ./multi-account-surveying-api/resources/upsert_section.yaml
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/delete_section:
    $ref: ./multi-account-surveying-api/resources/delete_section.yaml
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_factor:
    $ref: ./multi-account-surveying-api/resources/upsert_factor.yaml
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/delete_survey_question:
    $ref: ./multi-account-surveying-api/resources/delete_survey_question.yaml
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_question:
    $ref: ./multi-account-surveying-api/resources/upsert_question.yaml
  /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_question_display_condition:
    $ref: ./multi-account-surveying-api/resources/upsert_question_display_condition.yaml
