# path
reportId:
  $ref: './path/reportId.yaml'
surveyId:
  $ref: './path/surveyId.yaml'
questionId:
  $ref: './path/questionId.yaml'

# query
a:
  $ref: './query/anchor_filters.yaml'
comparison:
  $ref: './query/comparison.yaml'
filters:
  $ref: './query/filters.yaml'
from:
  $ref: './query/from.yaml'
locale:
  $ref: './query/selected_locale.yaml'
selected_leader:
  $ref: './query/selected_leader.yaml'
status:
  $ref: './query/status.yaml'
term:
  $ref: './query/term.yaml'
to:
  $ref: './query/to.yaml'


