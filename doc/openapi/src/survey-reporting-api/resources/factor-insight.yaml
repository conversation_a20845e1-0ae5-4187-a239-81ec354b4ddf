get:
  summary: Get factor insight
  operationId: factorInsight
  description: Endpoint to get the factor insight for a given factor in the survey report
  tags:
    - Survey Reporting API Factor Insight
  parameters:
    - $ref: "../parameters/path/surveyId.yaml"
    - $ref: "../parameters/path/reportId.yaml"
    - $ref: "../parameters/path/factorId.yaml"
    - $ref: "../parameters/query/anchor_filters.yaml"
    - $ref: "../parameters/query/comparison.yaml"
    - $ref: "../parameters/query/filters.yaml"
    - $ref: "../parameters/query/from.yaml"
    - $ref: "../parameters/query/selected_locale.yaml"
    - $ref: "../parameters/query/selected_leader.yaml"
    - $ref: "../parameters/query/status.yaml"
    - $ref: "../parameters/query/term.yaml"
    - $ref: "../parameters/query/to.yaml"
  responses:
    '200':
      description: Expected response to a valid request
      content:
        application/json:
          schema:
            type: object
            properties:
              reportData:
                $ref: "../schemas/FactorInsightReportData.yaml"
              participationSummary:
                $ref: "../schemas/ParticipationSummary.yaml"
              comparison:
                $ref: "../schemas/Comparison.yaml"
              filterPrivacyFailure:
                $ref: "../schemas/FilterPrivacyFailure.yaml"
              hierarchyPrivacyThresholdExceeded:
                type: boolean
                example: false
              insignificantFilters: 
                type: boolean
                example: false   
    "400": 
      description: Bad Request. The client provided invalid input.
    "401":
      description: Unauthorized. The user is not authenticated or the authentication token is invalid.
    "403":
      description: Permission denied. User cannot access the survey or report.
    "404":
      description: Not Found. The requested survey, report, or factor does not exist.
    "500":
      description: Internal server error.