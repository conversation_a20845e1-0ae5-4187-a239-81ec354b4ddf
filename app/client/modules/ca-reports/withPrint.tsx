import React, { type ComponentType } from 'react';
import getDisplayName from 'react-display-name';
import { usePrint } from 'ca-hooks/src/usePrint/usePrint';

export type PrintProps = {
  print: () => void;
};

export const withPrint = <M,>(Component: ComponentType<M & PrintProps>) => {
  const PrintComponent: ComponentType<M> = (props) => {
    const { print } = usePrint();
    return <Component {...props} print={print} />;
  };
  PrintComponent.displayName = getDisplayName(Component);
  return PrintComponent;
};
