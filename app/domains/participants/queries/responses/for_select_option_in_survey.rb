module Participants
  module Queries
    module Responses
      class ForSelectOptionInSurvey
        module Types
          include Dry.Types()
        end

        class ResponseStruct < Dry::Struct
          attribute :aggregate_id, Types::String
          attribute :user_aggregate_id, Types::String.optional
          attribute :user_mongo_id, Types::String.optional
        end

        def initialize(
          feature_flag_query: ::FeatureFlags::Queries::ValueForAccount.new,
          account_query: ::Participants::Queries::Accounts::ForSurvey.new,
          user_query: ::Participants::Queries::Users::ByIds.new,
          comparator_handler: MongoToPostgresScientist::ComparatorHandler.new
        )
          @feature_flag_query = feature_flag_query
          @account_query = account_query
          @user_query = user_query
          @comparator_handler = comparator_handler
        end

        def call(survey_aggregate_id:, question_aggregate_id:, select_option_id:, exclude_user_aggregate_ids:)
          experiment = MongoToPostgresScientist::MtpExperiment.new(operation: self.class.name)
          account_aggregate_id = account_query.call(survey_aggregate_id: survey_aggregate_id).aggregate_id

          if finish_experiment?(account_aggregate_id)
            pg_query(survey_aggregate_id, question_aggregate_id, select_option_id, exclude_user_aggregate_ids, account_aggregate_id)
          elsif start_experiment?(account_aggregate_id)
            experiment.use { mongo_query(survey_aggregate_id, question_aggregate_id, select_option_id, exclude_user_aggregate_ids, account_aggregate_id) }
            experiment.try { pg_query(survey_aggregate_id, question_aggregate_id, select_option_id, exclude_user_aggregate_ids, account_aggregate_id) }
            experiment.compare do |control_value, candidate_value|
              comparator_handler.call(
                control_value: control_value,
                candidate_value: candidate_value,
                experiment: experiment
              )
            end

            experiment.run
          else
            mongo_query(survey_aggregate_id, question_aggregate_id, select_option_id, exclude_user_aggregate_ids, account_aggregate_id)
          end
        rescue => error
          experiment.publish_error(error)
          mongo_query(survey_aggregate_id, question_aggregate_id, select_option_id, exclude_user_aggregate_ids, account_aggregate_id)
        end

        private

        attr_reader :feature_flag_query, :account_query, :user_query, :comparator_handler

        def mongo_query(survey_aggregate_id, question_aggregate_id, select_option_id, exclude_user_aggregate_ids, account_aggregate_id)
          survey = Survey.find_by(aggregate_id: survey_aggregate_id)
          return [] if survey.nil?

          query = survey.responses.real.ne(status: :deleted)

          if select_option_id.present?
            query = query.where("answers.select_option_ids" => select_option_id)
          else
            survey_demographics_without_self_report = Question.find_by(aggregate_id: question_aggregate_id)

            sos = survey_demographics_without_self_report.select_options
            sos = sos.respond_to?(:active) ? sos.active : sos
            select_option_ids = sos.map(&:id)

            query = query.nin("answers.select_option_ids" => select_option_ids)
          end

          exclude_user_mongo_ids = user_query.call(user_aggregate_ids: exclude_user_aggregate_ids).map(&:mongo_id)
          query = query.nin(user_id: exclude_user_mongo_ids) if exclude_user_mongo_ids.present?

          responses = query.only(:aggregate_id, :user_id).to_a

          user_mongo_ids = responses.map(&:user_id).uniq
          user_id_mappings = User.where(:id.in => user_mongo_ids).only(:id, :aggregate_id).map { |u| [u.id.to_s, u.aggregate_id] }.to_h

          responses.map { |response|
            user_aggregate_id = user_id_mappings[response.user_id.to_s]

            response_struct = {
              aggregate_id: response.aggregate_id,
              user_aggregate_id: user_aggregate_id,
              user_mongo_id: response.user_id.to_s
            }
            ResponseStruct.new(response_struct)
          }.compact
        end

        def pg_query(survey_aggregate_id, question_aggregate_id, select_option_id, exclude_user_aggregate_ids, account_aggregate_id)
          survey = SurveyDesign::PgSurvey.find_by(aggregate_id: survey_aggregate_id)
          return [] if survey.nil?

          responses = survey.responses.real

          select_option_aggregate_id = ::Domains::MurmurGeneral.database(:general_db)[:select_option_id_mappings]
            .where(question_aggregate_id: question_aggregate_id, mongo_id: select_option_id.to_s)
            .select(:aggregate_id)
            .first&.[](:aggregate_id)

          answers = if select_option_id.present?
            SurveyDesign::PgAnswer.where(
              account_aggregate_id: account_aggregate_id,
              question_aggregate_id: question_aggregate_id,
              response_aggregate_id: responses.pluck(:aggregate_id)
            ).where(
              "demographic_value_aggregate_ids @> ARRAY[?]::uuid[]", select_option_aggregate_id
            )
          else
            survey_demographics_without_self_report = SurveyDesign::PgQuestion.find_by(aggregate_id: question_aggregate_id)

            sos = survey_demographics_without_self_report.select_options
            sos = sos.select { |so| so.status == "active" }
            select_option_aggregate_ids = sos.map(&:aggregate_id)

            SurveyDesign::PgAnswer.where(
              account_aggregate_id: account_aggregate_id,
              question_aggregate_id: question_aggregate_id,
              response_aggregate_id: responses.pluck(:aggregate_id)
            ).where.not(
              "demographic_value_aggregate_ids && ARRAY[?]::uuid[]", select_option_aggregate_ids
            )
          end

          matching_responses = answers.map { |answer|
            responses.find { |r|
              r.aggregate_id == answer.response_aggregate_id
            }
          }

          user_aggregate_ids = matching_responses.map(&:user_aggregate_id)
          users = user_query.call(user_aggregate_ids: user_aggregate_ids)

          matching_responses.map { |response|
            next if exclude_user_aggregate_ids.include?(response.user_aggregate_id)

            response_struct = {
              aggregate_id: response.aggregate_id,
              user_aggregate_id: response.user_aggregate_id,
              user_mongo_id: users.find { |u| u.aggregate_id == response.user_aggregate_id }.mongo_id
            }
            ResponseStruct.new(response_struct)
          }.compact
        end

        def start_experiment?(account_aggregate_id)
          feature_flag_query.call(
            account_aggregate_id: account_aggregate_id,
            flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_START,
            fallback_value: false
          )
        end

        def finish_experiment?(account_aggregate_id)
          feature_flag_query.call(
            account_aggregate_id: account_aggregate_id,
            flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_FINISH,
            fallback_value: false
          )
        end
      end
    end
  end
end
