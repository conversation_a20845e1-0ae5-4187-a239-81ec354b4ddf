module Participants
  module Queries
    module Users
      class ByIds
        module Types
          include Dry.Types()
        end

        class UserStruct < Dry::Struct
          attribute :aggregate_id, Types::String
          attribute :mongo_id, Types::String
        end

        def call(user_aggregate_ids:)
          ::User.in(aggregate_id: user_aggregate_ids).map do |mongo_user|
            UserStruct.new(
              aggregate_id: mongo_user.aggregate_id,
              mongo_id: mongo_user.id.to_s
            )
          end
        end
      end
    end
  end
end
