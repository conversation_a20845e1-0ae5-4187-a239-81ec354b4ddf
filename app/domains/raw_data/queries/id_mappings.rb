module RawData
  module Queries
    class IdMappings
      def from_aggregate_ids(collection:, aggregate_ids:)
        validate_collection(collection)

        mapping_repos[collection]
          .select(:aggregate_id, :mongo_id)
          .where(aggregate_id: aggregate_ids)
          .each_with_object({}) { |mapping, hash| hash[mapping[:aggregate_id]] = mapping[:mongo_id] }
      end

      def from_mongo_ids(collection:, mongo_ids:)
        validate_collection(collection)

        mapping_repos[collection]
          .select(:aggregate_id, :mongo_id)
          .where(mongo_id: mongo_ids)
          .each_with_object({}) { |mapping, hash| hash[mapping[:mongo_id]] = mapping[:aggregate_id] }
      end

      private

      def validate_collection(collection)
        raise ArgumentError, "Select options mapping currently not supported due to question scoping" if collection == :select_option
        raise ArgumentError, "Invalid collection: #{collection}" unless mapping_repos.key?(collection)
      end

      def mapping_repos
        {
          survey: ::Domains::MurmurGeneral.database(:general_db)[:survey_id_mappings],
          response: ::Domains::MurmurGeneral.database(:general_db)[:response_id_mappings],
          factor: ::Domains::MurmurGeneral.database(:general_db)[:factor_id_mappings],
          question: ::Domains::MurmurGeneral.database(:general_db)[:question_id_mappings],
          section: ::Domains::MurmurGeneral.database(:general_db)[:section_id_mappings]
        }
      end
    end
  end
end
