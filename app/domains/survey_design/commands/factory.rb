module SurveyDesign
  module Commands
    class Factory
      class HandlerNotFoundError < StandardError; end

      # @param survey_id [String] A survey UUID string (aggregate_id).
      # @param command_class [Class] The class of command to be handled, valid examples are Domains::SurveyDesign::Command and SurveyDesigner::LegacyDomains::Command.
      def handler_for(survey_id:, command_class:)
        event_framework_handler_class_for(command_class: command_class).new
      end

      private

      def event_framework_handler_class_for(command_class:)
        EVENT_FRAMEWORK_HANDLER_MAP.fetch(command_class) do
          raise HandlerNotFoundError, "No handler registered for #{command_class}"
        end
      end

      EVENT_FRAMEWORK_HANDLER_MAP = {
        # Survey
        Domains::SurveyDesign::Survey::CreateBlankCommand => Domains::SurveyDesign::Survey::CreateBlankCommandHandler,
        Domains::SurveyDesign::Survey::RenameCommand => Domains::SurveyDesign::Survey::RenameCommandHandler,
        Domains::SurveyDesign::Survey::DeleteCommand => Domains::SurveyDesign::Survey::DeleteCommandHandler,
        Domains::SurveyDesign::Survey::RestoreCommand => Domains::SurveyDesign::Survey::RestoreCommandHandler,

        # TODO: Domains::SurveyDesign::Survey::MigrateCommandHandler
        #   This handles multiple commands
        #   We will likely ignore this one and deprecate it completely when we port all handlers to mongo/postgres

        # SurveyCaptureLayout (creating, renaming, deleting, restoring, positioning sections)
        Domains::SurveyDesign::SurveyCaptureLayout::AddSectionCommand => Domains::SurveyDesign::SurveyCaptureLayout::AddSectionCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::RenameSectionCommand => Domains::SurveyDesign::SurveyCaptureLayout::RenameSectionCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionCommand => Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::RestoreSectionCommand => Domains::SurveyDesign::SurveyCaptureLayout::RestoreSectionCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::MoveSectionCommand => Domains::SurveyDesign::SurveyCaptureLayout::MoveSectionCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::PositionDemographicSectionsCommand => Domains::SurveyDesign::SurveyCaptureLayout::PositionDemographicSectionsCommandHandler,

        # SurveyCaptureLayout (section descriptions)
        Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionLongDescriptionCommand => Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionLongDescriptionCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionShortDescriptionCommand => Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionShortDescriptionCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionDescriptionsCommand => Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionDescriptionsCommandHandler,

        # SurveyCaptureLayout (moving/hiding questions)
        Domains::SurveyDesign::SurveyCaptureLayout::HideQuestionFromCaptureCommand => Domains::SurveyDesign::SurveyCaptureLayout::HideQuestionFromCaptureCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::PositionQuestionCommand => Domains::SurveyDesign::SurveyCaptureLayout::PositionQuestionCommandHandler,
        Domains::SurveyDesign::SurveyCaptureLayout::PositionQuestionAtSectionBottomCommand => Domains::SurveyDesign::SurveyCaptureLayout::PositionQuestionAtSectionBottomCommandHandler,

        # SurveyQuestion (creating, renaming, deleting, restoring questions)
        Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand => Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::DeleteCommand => Domains::SurveyDesign::SurveyQuestion::DeleteCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::RestoreCommand => Domains::SurveyDesign::SurveyQuestion::RestoreCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::ChangeTextCommand => Domains::SurveyDesign::SurveyQuestion::ChangeTextCommandHandler, # text in capture
        Domains::SurveyDesign::SurveyQuestion::ChangeShortTextCommand => Domains::SurveyDesign::SurveyQuestion::ChangeShortTextCommandHandler, # text as a report filter (self-select questions)

        # SurveyQuestion (changing question type and rating scale)
        Domains::SurveyDesign::SurveyQuestion::ChangeQuestionTypeCommand => Domains::SurveyDesign::SurveyQuestion::ChangeQuestionTypeCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::ChangeRatingScaleCommand => Domains::SurveyDesign::SurveyQuestion::ChangeRatingScaleCommandHandler,

        # SurveyQuestion (changing mandatory/optional status)
        Domains::SurveyDesign::SurveyQuestion::MakeMandatoryCommand => Domains::SurveyDesign::SurveyQuestion::MakeMandatoryCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::MakeOptionalCommand => Domains::SurveyDesign::SurveyQuestion::MakeOptionalCommandHandler,

        # SurveyQuestion (changing select options)
        Domains::SurveyDesign::SurveyQuestion::ChangeSelectionLimitCommand => Domains::SurveyDesign::SurveyQuestion::ChangeSelectionLimitCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::AddSelectOptionCommand => Domains::SurveyDesign::SurveyQuestion::AddSelectOptionCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::DeleteSelectOptionCommand => Domains::SurveyDesign::SurveyQuestion::DeleteSelectOptionCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::AddSelectOptionsCommand => Domains::SurveyDesign::SurveyQuestion::AddSelectOptionsCommandHandler,
        # Domains::SurveyDesign::SurveyQuestion::DeleteSelectOptionsCommand => Domains::SurveyDesign::SurveyQuestion::DeleteSelectOptionsCommandHandler, # missing handler, used only by the migrate command handler
        Domains::SurveyDesign::SurveyQuestion::RenameSelectOptionCommand => Domains::SurveyDesign::SurveyQuestion::RenameSelectOptionCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::MoveSelectOptionCommand => Domains::SurveyDesign::SurveyQuestion::MoveSelectOptionCommandHandler,

        # TODO: Domains::SurveyDesign::SurveyQuestion::UpdateCommandHandler
        #   This handles multiple commands
        #   Used by Questions::Commands::UpdateQuestionDetails
        #     there is branching logic based on event sourcing or mongo
        #     looking at the survey event sourced attribute
        #   Can affect:
        #     AddWorkflowQuestion (sales experience API),
        #     create and update on survey question controller
        #
        # TODO: Domains::SurveyDesign::SurveyQuestion::UpdateSelectOptionsCommandHandler
        #   This handles multiple commands
        #   Used by Questions::Commands::UpdateSelectOptions
        #     there is branching logic based on event sourcing or mongo
        #     looking at the survey event sourced attribute
        #   Can affect:
        #     Demographics controller (legacy demographics)
        #     AddWorkflowQuestion (sales experience API),
        #     update on survey question controller

        # SurveyQuestion (Enabling/disabling "Other" as a select option)
        Domains::SurveyDesign::SurveyQuestion::EnableOtherSelectOptionCommand => Domains::SurveyDesign::SurveyQuestion::EnableOtherSelectOptionCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::DisableOtherSelectOptionCommand => Domains::SurveyDesign::SurveyQuestion::DisableOtherSelectOptionCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::ChangeOtherSelectOptionLabel => Domains::SurveyDesign::SurveyQuestion::ChangeOtherSelectOptionLabelCommandHandler,

        # SurveyQuestion (enabling select comments)
        Domains::SurveyDesign::SurveyQuestion::EnableSelectCommentsCommand => Domains::SurveyDesign::SurveyQuestion::EnableSelectCommentsCommandHandler,
        Domains::SurveyDesign::SurveyQuestion::DisableSelectCommentsCommand => Domains::SurveyDesign::SurveyQuestion::DisableSelectCommentsCommandHandler,

        # Answer-based branching commands
        Domains::SurveyDesign::DisplayCondition::AddAnswerConditionToQuestionCommand => Domains::SurveyDesign::DisplayCondition::AddAnswerConditionToQuestionCommandHandler,
        Domains::SurveyDesign::DisplayCondition::RemoveAnswerConditionFromQuestionCommand => Domains::SurveyDesign::DisplayCondition::RemoveAnswerConditionFromQuestionCommandHandler,
        Domains::SurveyDesign::DisplayCondition::ChooseParentQuestionCommand => Domains::SurveyDesign::DisplayCondition::ChooseParentQuestionCommandHandler,
        Domains::SurveyDesign::DisplayCondition::AddSelectOptionFromParentQuestionCommand => Domains::SurveyDesign::DisplayCondition::AddSelectOptionFromParentQuestionCommandHandler,
        Domains::SurveyDesign::DisplayCondition::RemoveSelectOptionFromParentQuestionCommand => Domains::SurveyDesign::DisplayCondition::RemoveSelectOptionFromParentQuestionCommandHandler,

        # Demographic-based branching commands
        # In reality these are not really event framework handlers, but they obey the same interface
        # and are used in the Elm survey designer
        SurveyDesigner::LegacyDomains::SurveyQuestion::AddDemographicCondition::Command =>
          SurveyDesigner::LegacyDomains::SurveyQuestion::AddDemographicCondition::CommandHandler,
        SurveyDesigner::LegacyDomains::SurveyQuestion::AddDemographicConditionValue::Command =>
          SurveyDesigner::LegacyDomains::SurveyQuestion::AddDemographicConditionValue::CommandHandler,
        SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveDemographicCondition::Command =>
          SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveDemographicCondition::CommandHandler,
        SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveDemographicConditionValue::Command =>
          SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveDemographicConditionValue::CommandHandler,
        SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveAllDemographicConditions::Command =>
          SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveAllDemographicConditions::CommandHandler,
        SurveyDesigner::LegacyDomains::SurveyQuestion::SelectConditionDemographicQuestion::Command =>
          SurveyDesigner::LegacyDomains::SurveyQuestion::SelectConditionDemographicQuestion::CommandHandler,
        SurveyDesigner::LegacyDomains::SurveyQuestion::SetConditionMatcherType::Command =>
          SurveyDesigner::LegacyDomains::SurveyQuestion::SetConditionMatcherType::CommandHandler
      }.freeze
    end
  end
end
