module SurveyDesign
  module MultiAccountSurveying
    module Queries
      class NotSnapshotEngagementSurveyError < StandardError; end
      class DataExtractionError < StandardError; end
      class InactiveSurveyError < StandardError; end
      class GetSurveyData
        include Dry::Monads[:result]

        def initialize(
          survey_repo: Survey.extras(read: {mode: :secondary}),
          account_repo: Account.extras(read: {mode: :secondary}),
          stq_repo: ->(survey) { survey.survey_to_questions },
          extract_survey_data: ExtractSurveyData.new,
          extract_question_data: ExtractQuestionData.new,
          extract_section_data: ExtractSectionData.new,
          extract_factor_data: ExtractFactorData.new,
          extract_demographic_data: ExtractDemographicData.new,
          logger: Splunk::Logger.new(Rails.logger)
        )
          @survey_repo = survey_repo
          @account_repo = account_repo
          @stq_repo = stq_repo
          @extract_survey_data = extract_survey_data
          @extract_question_data = extract_question_data
          @extract_section_data = extract_section_data
          @extract_factor_data = extract_factor_data
          @extract_demographic_data = extract_demographic_data
          @logger = logger
        end

        def call(survey_aggregate_id:)
          survey = survey_repo.find_by(aggregate_id: survey_aggregate_id)
          account = account_repo.find(survey.account_id)

          # Only snapshot engagement surveys are in scope for MAS/MAR
          raise NotSnapshotEngagementSurveyError, "Survey with id '#{survey_aggregate_id}' is not a snapshot engagement survey" unless survey.is_snapshot_engagement?

          # Raise error if survey is archived or closed
          # TODO confirm we don't want to return closed surveys
          raise InactiveSurveyError, "Survey with id '#{survey_aggregate_id}' is not active" if survey.inactive?

          survey_data = extract_data(extract_survey_data, {survey_aggregate_id: survey_aggregate_id}, survey_aggregate_id, "survey")

          # Ignore any STQs that are not currently active, divide into normal STQs and demographic STQs
          all_stqs = stq_repo.call(survey)
          stqs, demographic_stqs = filter_stqs(all_stqs)

          question_data = extract_data(extract_question_data, {stqs: stqs}, survey_aggregate_id, "question")
          section_data = extract_data(extract_section_data, {theme_ids: survey.themes.active.pluck(:id)}, survey_aggregate_id, "section")
          factor_data = extract_data(extract_factor_data, {factor_ids: survey.factors.active.pluck(:id)}, survey_aggregate_id, "factor")
          demographic_data = extract_data(extract_demographic_data, {demographic_stqs: demographic_stqs}, survey_aggregate_id, "demographic")

          Success({
            accountId: account.aggregate_id,
            accountMongoId: account.id.to_s,
            accountSubdomain: account.subdomain,
            region: Murmur::REGION,
            **survey_data,
            questions: question_data,
            demographics: demographic_data,
            factors: factor_data,
            sections: section_data
          })
        rescue Mongoid::Errors::DocumentNotFound => e
          message = if e.message.include?("not found for class Survey")
            "Could not find survey with id '#{survey_aggregate_id}'"
          elsif e.message.include?("not found for class Account")
            "Could not find account for survey with id '#{survey_aggregate_id}'"
          else
            "Document not found"
          end

          handle_failure(
            status: :not_found,
            message: message,
            survey_aggregate_id: survey_aggregate_id
          )
        rescue NotSnapshotEngagementSurveyError, InactiveSurveyError => e
          handle_failure(
            status: :bad_request,
            message: e.message,
            survey_aggregate_id: survey_aggregate_id
          )
        rescue DataExtractionError => e
          handle_failure(
            status: :internal_server_error,
            message: e.message,
            survey_aggregate_id: survey_aggregate_id
          )
        rescue => e
          handle_failure(
            status: :internal_server_error,
            message: e.message,
            survey_aggregate_id: survey_aggregate_id
          )
        end

        private

        attr_reader :survey_repo, :account_repo, :stq_repo, :extract_survey_data, :extract_question_data, :extract_section_data, :extract_factor_data, :extract_demographic_data, :logger

        def find_survey(survey_aggregate_id:)
          survey_repo.find_by(aggregate_id: survey_aggregate_id)
        end

        def find_account(account_id:)
          account_repo.find(account_id)
        end

        def extract_data(extractor, args, survey_aggregate_id, data_type)
          result = extractor.call(**args)
          raise DataExtractionError, "Failed to extract #{data_type} data for survey with id '#{survey_aggregate_id}'" unless result.success?
          result.value!
        end

        def filter_stqs(stqs)
          filtered_stqs = []
          demographic_stqs = []

          # Ignore inactive (i.e. soft-deleted) STQs
          stqs.active.each do |stq|
            if stq.type == :culture
              filtered_stqs << stq

            # TODO check how this performs - large accounts could have a very large number of demographic STQs
            elsif stq.type == :segment
              if stq.self_report? # self-select demographics are treated as questions
                filtered_stqs << stq
              else
                demographic_stqs << stq
              end
            end
          end

          [filtered_stqs, demographic_stqs]
        end

        ##
        # Handles the failure case, log to Splunk and return a Failure result.
        #
        # @param status [Symbol] The status of the failure.
        # @param message [String] The error message.
        # @param survey_aggregate_id [String] The aggregate ID of the survey that caused the failure.
        # @return [Dry::Monads::Result] A Failure result with the status and message.
        def handle_failure(status:, message:, survey_aggregate_id:)
          logger.log(
            app: "murmur",
            module: self.class.name,
            status: status,
            message: "Failed to get survey data: #{message}",
            survey_aggregate_id: survey_aggregate_id
          )

          Failure({status: status, message: message})
        end
      end
    end
  end
end
