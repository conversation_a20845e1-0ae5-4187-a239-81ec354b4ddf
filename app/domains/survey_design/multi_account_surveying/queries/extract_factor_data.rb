module SurveyDesign
  module MultiAccountSurveying
    module Queries
      class ExtractFactorData
        include Dry::Monads[:result]

        def initialize(
          factor_repo: Factor.extras(read: {mode: :secondary}),
          logger: Splunk::Logger.new(Rails.logger)
        )
          @factor_repo = factor_repo
          @logger = logger
        end

        def call(factor_ids:)
          # TODO next PR
          Success([])
        end

        private

        attr_reader :factor_repo, :logger
      end
    end
  end
end
