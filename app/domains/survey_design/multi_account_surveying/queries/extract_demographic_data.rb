module SurveyDesign
  module MultiAccountSurveying
    module Queries
      class ExtractDemographicData
        include Dry::Monads[:result]

        def initialize(
          question_repo: Question.extras(read: {mode: :secondary}),
          logger: Splunk::Logger.new(Rails.logger)
        )
          @question_repo = question_repo
          @logger = logger
        end

        def call(demographic_stqs:)
          # TODO next PR
          Success([])
        end

        private

        attr_reader :question_repo, :logger
      end
    end
  end
end
