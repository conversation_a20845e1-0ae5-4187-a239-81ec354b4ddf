# TODO delete this when getOrgSurveyData endpoint is removed
# https://cultureamp.atlassian.net/browse/ERV-2298

module SurveyDesign
  module MultiAccountSurveying
    module Queries
      class NotOrganisationSurveyError < StandardError; end
      class NotSnapshotEngagementSurveyError < StandardError; end
      class GetOrgSurveyData
        include Dry::Monads[:result]

        def initialize(
          survey_repo: Survey.extras(read: {mode: :secondary}),
          logger: Splunk::Logger.new(Rails.logger)
        )
          @survey_repo = survey_repo
          @logger = logger
        end

        def call(survey_aggregate_id:)
          survey = survey_repo.find_by(aggregate_id: survey_aggregate_id)

          raise NotOrganisationSurveyError, "Survey with id '#{survey_aggregate_id}' is not an organisation survey" unless survey.is_org_survey?
          raise NotSnapshotEngagementSurveyError, "Survey with id '#{survey_aggregate_id}' is not a snapshot engagement survey" unless survey.is_snapshot_engagement?

          Success({
            id: survey.id.to_s,
            aggregateId: survey.aggregate_id,
            configuration: {
              Configs::CAPTURE_LIKERT_COLOR_SCHEMA.camelize(:lower) => survey.configuration[Configs::CAPTURE_LIKERT_COLOR_SCHEMA],
              Configs::SUPPORTED_LOCALES.camelize(:lower) => survey.configuration[Configs::SUPPORTED_LOCALES]
            },
            flags: {
              Flags::IMPROVED_COMMS_CONFIGURATION.camelize(:lower) => survey.flags[Flags::IMPROVED_COMMS_CONFIGURATION]
            },
            # Use _translations suffix to get all localized texts, not just the default locale text
            name: survey.name_translations,
            description: survey.description_translations,
            launchedAt: survey.launched_at&.iso8601,
            status: survey.status.to_s,
            type: survey.type.to_s,
            surveyPeriodType: survey.survey_period_type.to_s,
            region: Murmur::REGION
          })
        rescue Mongoid::Errors::DocumentNotFound
          handle_failure(
            status: :not_found,
            message: "Could not find survey with id '#{survey_aggregate_id}'",
            survey_aggregate_id: survey_aggregate_id
          )
        rescue NotOrganisationSurveyError, NotSnapshotEngagementSurveyError => e
          handle_failure(
            status: :bad_request,
            message: e.message,
            survey_aggregate_id: survey_aggregate_id
          )
        rescue => e
          handle_failure(
            status: :internal_server_error,
            message: e.message,
            survey_aggregate_id: survey_aggregate_id
          )
        end

        private

        attr_reader :survey_repo, :logger

        ##
        # Handles the failure case, log to Splunk and return a Failure result.
        #
        # @param status [Symbol] The status of the failure.
        # @param message [String] The error message.
        # @param survey_aggregate_id [String] The aggregate ID of the survey that caused the failure.
        # @return [Dry::Monads::Result] A Failure result with the status and message.
        def handle_failure(status:, message:, survey_aggregate_id:)
          logger.log(
            app: "murmur",
            module: "SurveyDesign::MultiAccountSurveying::Queries::GetOrgSurveyData",
            status: status,
            message: "Failed to get org survey data: #{message}",
            survey_aggregate_id: survey_aggregate_id
          )

          Failure({status: status, message: message})
        end
      end
    end
  end
end
