module SurveyDesign
  module MultiAccountSurveying
    module Queries
      class ExtractSectionData
        include Dry::Monads[:result]

        def initialize(
          theme_repo: Theme.extras(read: {mode: :secondary}),
          logger: Splunk::Logger.new(Rails.logger)
        )
          @theme_repo = theme_repo
          @logger = logger
        end

        def call(theme_ids:)
          # TODO next PR
          Success([])
        end

        private

        attr_reader :theme_repo
      end
    end
  end
end
