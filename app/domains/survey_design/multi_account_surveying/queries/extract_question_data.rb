module SurveyDesign
  module MultiAccountSurveying
    module Queries
      class ExtractQuestionData
        include Dry::Monads[:result]

        def initialize(
          question_repo: Question.extras(read: {mode: :secondary}),
          theme_repo: Theme.extras(read: {mode: :secondary}),
          factor_repo: Factor.extras(read: {mode: :secondary}),
          logger: Splunk::Logger.new(Rails.logger)
        )
          @question_repo = question_repo
          @theme_repo = theme_repo
          @factor_repo = factor_repo
          @logger = logger
        end

        def call(stqs:)
          # TODO next PR
          Success([])
        end

        private

        attr_reader :question_repo, :theme_repo, :factor_repo
      end
    end
  end
end
