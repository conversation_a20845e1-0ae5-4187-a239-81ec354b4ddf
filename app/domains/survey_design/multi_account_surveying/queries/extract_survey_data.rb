module SurveyDesign
  module MultiAccountSurveying
    module Queries
      class ExtractSurveyData
        class InactiveSurveyError < StandardError; end

        include Dry::Monads[:result]

        def initialize(
          survey_repo: Survey.extras(read: {mode: :secondary}),
          logger: Splunk::Logger.new(Rails.logger)
        )
          @survey_repo = survey_repo
          @logger = logger
        end

        def call(survey_aggregate_id:)
          survey = survey_repo.find_by(aggregate_id: survey_aggregate_id)

          # Raise error if survey is archived or closed
          raise InactiveSurveyError, "Survey with id '#{survey_aggregate_id}' is not active" if survey.inactive?

          Success({
            id: survey.aggregate_id,
            mongoId: survey.id.to_s,
            configuration: {
              Configs::CAPTURE_LIKERT_COLOR_SCHEMA.camelize(:lower) => survey.configuration[Configs::CAPTURE_LIKERT_COLOR_SCHEMA],
              Configs::SUPPORTED_LOCALES.camelize(:lower) => survey.configuration[Configs::SUPPORTED_LOCALES]
            },
            flags: {
              Flags::IMPROVED_COMMS_CONFIGURATION.camelize(:lower) => survey.flags[Flags::IMPROVED_COMMS_CONFIGURATION]
            },
            # Use _translations suffix to get all localized texts, not just the default locale text
            name: survey.name_translations,
            description: survey.description_translations,
            launchedAt: survey.launched_at&.iso8601,
            status: survey.status.to_s,
            type: survey.type.to_s,
            surveyPeriodType: survey.survey_period_type.to_s,
            isOrgSurvey: survey.is_org_survey
          })
        rescue Mongoid::Errors::DocumentNotFound
          handle_failure(
            status: :not_found,
            message: "Could not find survey with id '#{survey_aggregate_id}'",
            survey_aggregate_id: survey_aggregate_id
          )
        rescue InactiveSurveyError => e
          handle_failure(
            status: :bad_request,
            message: e.message,
            survey_aggregate_id: survey_aggregate_id
          )
        rescue => e
          handle_failure(
            status: :internal_server_error,
            message: e.message,
            survey_aggregate_id: survey_aggregate_id
          )
        end

        private

        attr_reader :survey_repo, :logger

        ##
        # Handles the failure case, log to Splunk and return a Failure result.
        #
        # @param status [Symbol] The status of the failure.
        # @param message [String] The error message.
        # @param survey_aggregate_id [String] The aggregate ID of the survey that caused the failure.
        # @return [Dry::Monads::Result] A Failure result with the status and message.
        def handle_failure(status:, message:, survey_aggregate_id:)
          logger.log(
            app: "murmur",
            module: self.class.name,
            status: status,
            message: "Failed to extract survey data: #{message}",
            survey_aggregate_id: survey_aggregate_id
          )

          Failure({status: status, message: message})
        end
      end
    end
  end
end
