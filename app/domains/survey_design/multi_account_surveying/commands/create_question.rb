module SurveyDesign
  module MultiAccountSurveying
    module Commands
      class QuestionCreationError < StandardError; end
      class SurveyToQuestionCreationError < StandardError; end
      class FactorsAttachmentError < StandardError; end
      class SelectOptionsCreationError < StandardError; end

      class CreateQuestion
        include Dry::Monads[:result]

        ##
        # @param survey_repo [Survey] The repository for surveys.
        # @param question_repo [Question] The repository for questions.
        # @param stq_repo [->(survey) { survey.survey_to_questions }] Lambda to get the survey_to_questions repository.
        # @param add_question_command [Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand] The command to add a question and stq, assign section to stq and positions stq.
        # @param command_handler_factory [SurveyDesign::Commands::Factory] The survey design command handler factory.
        # @param attach_factor_command [Questions::Commands::AddToFactors] The command to attach factors to the stq.
        # @param create_select_options_command [SurveyDesign::MultiAccountSurveying::Commands::CreateSelectOptions] The command to create select options for the question.
        # @param make_question_mandatory_command [Domains::SurveyDesign::SurveyQuestion::MakeMandatoryCommand] The command to make a question mandatory.
        # @param enable_other_select_option_command [Domains::SurveyDesign::SurveyQuestion::EnableOtherSelectOptionCommand] The command to enable the other select option for a question.
        # @param change_other_select_option_label_command [Domains::SurveyDesign::SurveyQuestion::ChangeOtherSelectOptionLabel] The command to change the label of the other select option.
        # @param logger [Splunk::Logger] The logger for logging messages.
        def initialize(
          survey_repo: Survey,
          question_repo: Question.extras(read: {mode: :secondary}),
          stq_repo: ->(aggregate_id) { Survey.where(aggregate_id: aggregate_id).first.survey_to_questions },
          section_repo: Theme,
          add_question_command: Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand,
          command_handler_factory: SurveyDesign::Commands::Factory.new,
          attach_factor_command: Questions::Commands::AddToFactors.new,
          create_select_options_command: SurveyDesign::MultiAccountSurveying::Commands::CreateSelectOptions.new,
          make_question_mandatory_command: Domains::SurveyDesign::SurveyQuestion::MakeMandatoryCommand,
          enable_other_select_option_command: Domains::SurveyDesign::SurveyQuestion::EnableOtherSelectOptionCommand,
          change_other_select_option_label_command: Domains::SurveyDesign::SurveyQuestion::ChangeOtherSelectOptionLabel,
          logger: Splunk::Logger.new(Rails.logger)
        )
          @survey_repo = survey_repo
          @question_repo = question_repo
          @stq_repo = stq_repo
          @section_repo = section_repo
          @add_question_command = add_question_command
          @command_handler_factory = command_handler_factory
          @attach_factor_command = attach_factor_command
          @create_select_options_command = create_select_options_command
          @make_question_mandatory_command = make_question_mandatory_command
          @enable_other_select_option_command = enable_other_select_option_command
          @change_other_select_option_label_command = change_other_select_option_label_command
          @logger = logger
        end

        # @param survey_aggregate_id [String] The aggregate ID of the survey.
        # @param question_specifications [Hash] The specifications for the question.
        # @param select_option_specifications [Array] The specifications for the select options, if applicable.
        # @return [Dry::Monads::Result] The result of the operation. Success or Failure.
        def call(survey_aggregate_id:, question_specifications:, select_option_specifications: [], metadata:)
          specs = question_specifications.deep_symbolize_keys

          survey = survey_repo.where(aggregate_id: survey_aggregate_id).first
          raise QuestionCreationError, "Survey not found" unless survey

          section = section_repo.where(section_id: specs[:section_id], survey_id: survey.id.to_s).first
          raise QuestionCreationError, "Section not found with id '#{specs[:section_id]}'" unless section

          # Generate positioned_after_question_id to determine stq order for the question.
          positioned_after_question_id = find_previous_question_id(position: specs[:stq_order], survey_aggregate_id: survey_aggregate_id, section_id: section.id)

          result = add_question_command.validate(
            survey_id: survey_aggregate_id,
            aggregate_id: specs[:question_id],
            text: specs[:description_translations],
            code: specs[:code],
            positioned_after_question_id: positioned_after_question_id, # When this field is nil, the command always sets stq order to 0.
            section_id: specs[:section_id],
            intended_purpose: specs[:intended_purpose], # This seems always "feedback" for engagement surveys
            question_type: specs[:question_type],
            rating_scale: specs[:rating_scale],
            selection_limit: specs[:selection_limit].to_i,
            cloned_from_curated_question_reference_code: nil,
            cloned_from_survey_question_id: nil,
            added_at: Time.now.utc
          )

          raise QuestionCreationError, result.errors.to_h if result.failure?

          command = add_question_command.new(result.to_h)
          # This command handler will create the question and stq, and attach the section to the stq.
          # (Attaching factors to the stq and creating select options are done in separate commands)
          add_question_command_handler = command_handler_factory.handler_for(survey_id: survey_aggregate_id, command_class: add_question_command)
          handler_result = add_question_command_handler.call(command: command, metadata: metadata)
          raise QuestionCreationError, "Failed adding question" unless handler_result == "success"

          question = question_repo.where(aggregate_id: specs[:question_id]).first
          stq = stq_repo.call(survey_aggregate_id)&.find_for_question(question.id)

          raise SurveyToQuestionCreationError, "Stq creation for the question '#{question.id}' is failed" unless stq

          question_aggregate_id = question.aggregate_id
          # By default, the question is optional
          # Only make question mandatory if the specs[:is_mandatory] is "true"
          mark_as_mandatory(question_aggregate_id, survey_aggregate_id, metadata) if specs[:is_mandatory] == "true"

          # Only SelectQuestion supports other option.
          # Other Option is disable by default
          other_option = specs[:other_option]
          add_other_option(other_option, question_aggregate_id, survey_aggregate_id, metadata) if question.select_question? && other_option.present?

          select_options = create_select_options(question, select_option_specifications, metadata)

          # Attach factors to the stq based on factor ids parameter
          attach_factor_result = attach_factor_command.call(survey: survey.reload, factor_aggregate_ids: specs[:factor_ids], question_aggregate_id: question.aggregate_id)
          raise FactorsAttachmentError, attach_factor_result[1] unless attach_factor_result[0] == :ok

          data = {
            id: question.id.to_s,
            aggregate_id: question.aggregate_id,
            stq_id: stq.id.to_s
          }
          data[:select_options] = select_options unless select_options.empty?

          Success({status: :created, data: data})
        rescue QuestionCreationError, SurveyToQuestionCreationError, FactorsAttachmentError, SelectOptionsCreationError => e
          handle_failure(status: :bad_request, message: e.message, survey_aggregate_id: survey_aggregate_id)
        rescue => e
          handle_failure(status: :internal_server_error, message: e.message, survey_aggregate_id: survey_aggregate_id)
        end

        private

        attr_reader :survey_repo, :question_repo, :stq_repo, :section_repo, :add_question_command,
          :attach_factor_command, :create_select_options_command,
          :make_question_mandatory_command,
          :enable_other_select_option_command,
          :change_other_select_option_label_command,
          :command_handler_factory,
          :logger

        ##
        # @param position [Integer, nil] Requested position of the question(stq).
        # @param survey_aggregate_id [UUID] The aggregate ID of the survey.
        # @param section_id [UUID] The ID of the section to which the question belongs.
        # @return [UUID, nil] The aggregate ID of the previous positioned question or nil if position param is nil(or less than and equal to 0), or the question not found.
        def find_previous_question_id(position:, survey_aggregate_id:, section_id:)
          return if position.to_i <= 0

          previous_position = position.to_i - 1
          stq_repo.call(survey_aggregate_id).where(theme_id: section_id, order: previous_position).first&.question&.aggregate_id
        end

        ##
        # Creates select options for a question based on the provided specifications.
        # @param question [Question] The question object for which select options are being created.
        # @param select_option_specifications [Array<Hash>] Specifications for the select options to be created.
        # @param metadata [EventFramework::Event::Metadata] Metadata for the operation.
        # @return [Array<Hash>] An array of created select options, each represented as a hash with keys :id and :aggregate_id.
        def create_select_options(question, select_option_specifications, metadata)
          return [] unless question.select_question? && select_option_specifications.present?

          result = create_select_options_command.call(
            question_aggregate_id: question.aggregate_id,
            select_options_specifications: select_option_specifications,
            metadata: metadata
          )

          case result
          in Success
            result.value![:data]
          in Failure({message:})
            raise SelectOptionsCreationError, message
          else
            raise SelectOptionsCreationError, "Creation of select options failed for question #{question.aggregate_id}"
          end
        end

        def handle_failure(status:, message:, survey_aggregate_id:)
          logger.log(
            app: "murmur",
            message: message,
            module: self.class.name,
            status: status,
            survey_aggregate_id: survey_aggregate_id
          )

          Failure({status: status, message: message})
        end

        def mark_as_mandatory(question_aggregate_id, survey_aggregate_id, metadata)
          make_question_mandatory_command_result = make_question_mandatory_command.validate(
            aggregate_id: question_aggregate_id,
            made_mandatory_at: Time.now.utc
          )
          raise QuestionCreationError, make_question_mandatory_command_result.errors.to_h if make_question_mandatory_command_result.failure?

          handler = command_handler_factory.handler_for(
            survey_id: survey_aggregate_id,
            command_class: make_question_mandatory_command
          )
          result = handler.call(
            command: make_question_mandatory_command.new(make_question_mandatory_command_result.to_h),
            metadata: metadata
          )

          raise QuestionCreationError, "Failed making question mandatory" unless result == "success"
        end

        def add_other_option(option, question_aggregate_id, survey_aggregate_id, metadata)
          enable_other_select_option_command_result = enable_other_select_option_command.validate(
            aggregate_id: question_aggregate_id,
            enabled_at: Time.now.utc
          )

          raise QuestionCreationError, enable_other_select_option_command_result.errors.to_h if enable_other_select_option_command_result.failure?

          handler = command_handler_factory.handler_for(
            survey_id: survey_aggregate_id,
            command_class: enable_other_select_option_command
          )
          result = handler.call(
            command: enable_other_select_option_command.new(enable_other_select_option_command_result.to_h),
            metadata: metadata
          )

          raise QuestionCreationError, "Failed enabling other select option" unless result == "success"

          value_translations = option[:value_translations]
          value_translations.each do |translation|
            text = translation[:text]
            locale = translation[:locale]

            change_other_select_option_label_command_result = change_other_select_option_label_command.validate(
              aggregate_id: question_aggregate_id,
              text: text,
              locale: locale,
              changed_at: Time.now.utc
            )

            raise QuestionCreationError, change_other_select_option_label_command_result.errors.to_h if change_other_select_option_label_command_result.failure?

            handler = command_handler_factory.handler_for(
              survey_id: survey_aggregate_id,
              command_class: change_other_select_option_label_command
            )
            result = handler.call(
              command: change_other_select_option_label_command.new(change_other_select_option_label_command_result.to_h),
              metadata: metadata
            )

            raise QuestionCreationError, "Failed changing other select option label" unless result == "success"
          end
        end
      end
    end
  end
end
