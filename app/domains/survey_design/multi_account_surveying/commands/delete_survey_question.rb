module SurveyDesign
  module MultiAccountSurveying
    module Commands
      class InvalidParameterError < StandardError; end
      class DeletionFailedError < StandardError; end
      class ENPSProtectionError < StandardError; end
      class DeleteSurveyQuestion
        include Dry::Monads[:result]

        # @param survey_repo [Survey] The repository for surveys.
        # @param question_repo [Question] The repository for questions.
        # @param delete_question_command [Domains::SurveyDesign::SurveyQuestion::DeleteCommand] The command to add a section.
        # @param enps_protection_checker [ENPSProtection] The checker for eNPS11 protections.
        # @param logger [Splunk::Logger] The logger for logging messages.
        # @param command_handler_factory [SurveyDesign::Commands::Factory] The survey design command handler factory.
        def initialize(
          survey_repo: Survey.extras(read: {mode: :secondary}),
          question_repo: Question.extras(read: {mode: :secondary}),
          delete_question_command: Domains::SurveyDesign::SurveyQuestion::DeleteCommand,
          enps_protection_checker: ENPSProtection,
          logger: Splunk::Logger.new(Rails.logger),
          command_handler_factory: SurveyDesign::Commands::Factory.new
        )
          @survey_repo = survey_repo
          @question_repo = question_repo
          @delete_question_command = delete_question_command
          @enps_protection_checker = enps_protection_checker
          @logger = logger
          @command_handler_factory = command_handler_factory
        end

        # @param survey_aggregate_id [String] The aggregate ID of the survey.
        # @param question_id [String] The UUID of the question to be deleted.
        # @param metadata [EventFramework::Event::Metadata] The metadata for the event.
        # @return [Dry::Monads::Result] The result of the operation.
        # @return [Failure] If the operation fails, it returns a failure with the status and message.
        def call(survey_aggregate_id:, question_id:, metadata:)
          # Block modification of ENPS11 question
          # TODO check treatment of ENPS11 questions if parent survey enables, then disables ENPS11
          # https://cultureamp.atlassian.net/browse/ERV-2286
          raise ENPSProtectionError, "Blocked attempt to modify ENPS 11 question" if enps_protection_checker.enps_11_question?(question_id)

          survey = survey_repo.where(aggregate_id: survey_aggregate_id).first
          raise InvalidParameterError, "Survey not found" unless survey
          raise InvalidParameterError, "Survey is not event sourced" unless survey.event_sourced?

          # If question or STQ do not exist or STQ has already been deleted, return 204
          return Success({status: :no_content}) unless has_survey_question(survey.stqs, question_id)

          result = delete_question_command.validate(
            aggregate_id: question_id,
            deleted_at: Time.now.utc
          )

          raise InvalidParameterError, result.errors.to_h if result.failure?

          command = delete_question_command.new(result.to_h)
          delete_question_command_handler = command_handler_factory.handler_for(survey_id: survey_aggregate_id, command_class: delete_question_command)
          handler_result = delete_question_command_handler.call(command: command, metadata: metadata)

          # handle_result can be a failure or success
          # It does not return failure message
          raise DeletionFailedError, "Failed deleting question" unless handler_result == "success"

          Success({status: :accepted})
        rescue Mongoid::Errors::Validations => e
          handle_failure(status: :bad_request, message: e.message, survey_aggregate_id: survey_aggregate_id)
        rescue InvalidParameterError, DeletionFailedError, ENPSProtectionError => e
          handle_failure(status: :bad_request, message: e.message, survey_aggregate_id: survey_aggregate_id)
        rescue => e
          handle_failure(status: :internal_server_error, message: e.message, survey_aggregate_id: survey_aggregate_id)
        end

        private

        attr_reader :survey_repo, :question_repo, :delete_question_command, :enps_protection_checker, :logger, :command_handler_factory

        def has_survey_question(stqs, question_id)
          question = question_repo.where(aggregate_id: question_id).first
          return false unless question

          stq = stqs.where(question_id: question.id).first
          return false unless stq
          return false if stq.status == :deleted

          true
        end

        def handle_failure(status:, message:, survey_aggregate_id:)
          logger.log(
            app: "murmur",
            message: message,
            module: self.class.name,
            status: status,
            survey_aggregate_id: survey_aggregate_id
          )

          Failure({status: status, message: message})
        end
      end
    end
  end
end
