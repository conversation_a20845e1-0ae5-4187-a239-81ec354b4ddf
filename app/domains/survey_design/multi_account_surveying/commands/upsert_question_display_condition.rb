module SurveyDesign
  module MultiAccountSurveying
    module Commands
      class InvalidParametersError < StandardError; end
      class QuestionNotFoundError < StandardError; end
      class UpdateNotImplementedError < StandardError; end
      class DisplayConditionCreationError < StandardError; end
      class UpsertQuestionDisplayCondition
        include Dry::Monads[:result]
        # @param survey_repo [Survey] The repository for accessing survey data.
        # @param question_repo [Question] The repository for accessing question data.
        # @param display_condition_repo [DisplayCondition] The repository for accessing display condition data.
        # @param add_answer_condition_to_question_command [Domains::SurveyDesign::DisplayCondition::AddAnswerConditionToQuestionCommand] The command to add an answer condition to a question.
        # @param choose_parent_question_command [Domains::SurveyDesign::DisplayCondition::ChooseParentQuestionCommand] The command to choose a parent question for display conditions.
        # @param add_select_option_from_parent_question_command [Domains::SurveyDesign::DisplayCondition::AddSelectOptionFromParentQuestionCommand] The command to add a select option from a parent question.
        # @param command_handler_factory [SurveyDesign::Commands::Factory] The survey design command handler factory.
        # @param logger [Splunk::Logger] The logger for logging events.
        def initialize(
          survey_repo: Survey.extras(read: {mode: :secondary}),
          question_repo: Question.extras(read: {mode: :secondary}),
          display_condition_repo: DisplayCondition.extras(read: {mode: :secondary}),
          add_answer_condition_to_question_command: Domains::SurveyDesign::DisplayCondition::AddAnswerConditionToQuestionCommand,
          choose_parent_question_command: Domains::SurveyDesign::DisplayCondition::ChooseParentQuestionCommand,
          add_select_option_from_parent_question_command: Domains::SurveyDesign::DisplayCondition::AddSelectOptionFromParentQuestionCommand,
          command_handler_factory: SurveyDesign::Commands::Factory.new,
          logger: Splunk::Logger.new(Rails.logger)
        )
          @survey_repo = survey_repo
          @question_repo = question_repo
          @display_condition_repo = display_condition_repo
          @add_answer_condition_to_question_command = add_answer_condition_to_question_command
          @choose_parent_question_command = choose_parent_question_command
          @add_select_option_from_parent_question_command = add_select_option_from_parent_question_command
          @command_handler_factory = command_handler_factory
          @logger = logger
        end

        # Executes the command to upsert a question display condition.
        # @param survey_aggregate_id [String] The aggregate ID of the survey.
        # @param question_id [String] The aggregate ID of the question.
        # @param display_condition_specifications [Hash] The specifications for the display condition, including answer branching rules.
        # @param metadata [EventFramework::Event::Metadata] Metadata for the operation.
        # @return [Dry::Monads::Result] The result of the operation, either Success or Failure.
        def call(survey_aggregate_id:, question_id:, display_condition_specifications:, metadata:)
          survey = survey_repo.where(aggregate_id: survey_aggregate_id).first
          raise InvalidParametersError, "Survey not found" unless survey

          question = question_repo.where(aggregate_id: question_id).first
          raise QuestionNotFoundError, "Question not found" unless question

          specs = display_condition_specifications.deep_symbolize_keys
          answer_branching_rule = specs[:answer_branching_rule]

          handle_answer_branching_rule(survey_aggregate_id, question_id, answer_branching_rule, metadata) if answer_branching_rule.present?

          Success({status: :created, data: {id: question.id.to_s, aggregate_id: question_id}})
        rescue QuestionNotFoundError => e
          handle_failure(status: :not_found, message: e.message, survey_aggregate_id: survey_aggregate_id, question_aggregate_id: question_id)
        rescue InvalidParametersError, UpdateNotImplementedError, DisplayConditionCreationError => e
          handle_failure(status: :bad_request, message: e.message, survey_aggregate_id: survey_aggregate_id, question_aggregate_id: question_id)
        rescue => e
          handle_failure(status: :internal_server_error, message: e.message, survey_aggregate_id: survey_aggregate_id, question_aggregate_id: question_id)
        end

        private

        attr_reader :survey_repo, :question_repo, :display_condition_repo, :add_answer_condition_to_question_command, :choose_parent_question_command, :add_select_option_from_parent_question_command, :command_handler_factory, :logger

        def get_handler_for(survey_aggregate_id, command_class)
          command_handler_factory.handler_for(survey_id: survey_aggregate_id, command_class: command_class)
        end

        def add_answer_condition_to_question(survey_aggregate_id, aggregate_id, question_aggregate_id, metadata)
          command_class = add_answer_condition_to_question_command
          result = command_class.validate(
            aggregate_id: aggregate_id,
            question_id: question_aggregate_id,
            added_at: Time.now.utc
          )
          raise DisplayConditionCreationError, result.errors.to_h if result.failure?

          handler = get_handler_for(survey_aggregate_id, command_class)
          handler_result = handler.call(
            command: command_class.new(result.to_h),
            metadata: metadata
          )
          raise DisplayConditionCreationError, "Failed adding answer condition to question" unless handler_result == "success"
        end

        def choose_parent_question(survey_aggregate_id, aggregate_id, parent_question_id, metadata)
          command_class = choose_parent_question_command
          result = command_class.validate(
            aggregate_id: aggregate_id,
            parent_question_id: parent_question_id,
            chosen_at: Time.now.utc
          )
          raise DisplayConditionCreationError, result.errors.to_h if result.failure?

          handler = get_handler_for(survey_aggregate_id, command_class)
          handler_result = handler.call(
            command: command_class.new(result.to_h),
            metadata: metadata
          )
          raise DisplayConditionCreationError, "Failed choosing parent question" unless handler_result == "success"
        end

        def add_select_options(survey_aggregate_id, aggregate_id, select_option_ids, metadata)
          command_class = add_select_option_from_parent_question_command
          handler = get_handler_for(survey_aggregate_id, command_class)
          # for each select option ids
          # add select option from parent question - select_option_id
          select_option_ids.each do |select_option_id|
            result = command_class.validate(
              aggregate_id: aggregate_id,
              select_option_id: select_option_id,
              added_at: Time.now.utc
            )
            raise DisplayConditionCreationError, result.errors.to_h if result.failure?

            handler_result = handler.call(
              command: command_class.new(result.to_h),
              metadata: metadata
            )
            raise DisplayConditionCreationError, "Failed adding select option from parent question" unless handler_result == "success"
          end
        end

        def handle_answer_branching_rule(survey_aggregate_id, question_id, answer_branching_rule, metadata)
          aggregate_id = answer_branching_rule[:id]
          parent_question_id = answer_branching_rule[:parent_question_id]
          select_option_ids = answer_branching_rule[:select_option_ids]
          parent_question = question_repo.where(aggregate_id: parent_question_id).first
          raise InvalidParametersError, "Parent question not found" unless parent_question
          raise InvalidParametersError, "Select option IDs are required" if select_option_ids.blank?

          parent_question_select_option_ids = parent_question.select_options.map(&:select_option_id)
          # check if select_option_ids are valid
          raise InvalidParametersError, "Invalid select option IDs" unless select_option_ids.all? { |id| parent_question_select_option_ids.include?(id) }

          display_condition = display_condition_repo.where(aggregate_id: aggregate_id).first
          dc_select_option_ids = display_condition&.select_option_aggregate_ids || []
          return if display_condition && (dc_select_option_ids.sort == select_option_ids.sort)
          # TODO: https://cultureamp.atlassian.net/browse/ERV-2162
          # Note: Currently, it will just add new select options to the existing display condition.
          # We need to handle deleting old select options when we implement the update functionality.
          # deleted_select_option_ids = dc_select_option_ids - select_option_ids

          new_select_option_ids = select_option_ids - dc_select_option_ids

          add_answer_condition_to_question(survey_aggregate_id, aggregate_id, question_id, metadata) unless display_condition
          choose_parent_question(survey_aggregate_id, aggregate_id, parent_question_id, metadata) if display_condition&.parent_question_aggregate_id != parent_question.aggregate_id
          add_select_options(survey_aggregate_id, aggregate_id, new_select_option_ids, metadata)
        end

        def handle_failure(status:, message:, survey_aggregate_id:, question_aggregate_id:)
          logger.log(
            app: "murmur",
            message: message,
            module: self.class.name,
            status: status,
            survey_aggregate_id: survey_aggregate_id,
            question_aggregate_id: question_aggregate_id
          )

          Failure({status: status, message: message})
        end
      end
    end
  end
end
