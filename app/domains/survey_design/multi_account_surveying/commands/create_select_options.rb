module SurveyDesign
  module MultiAccountSurveying
    module Commands
      class SelectOptionsCreationError < StandardError; end

      class CreateSelectOptions
        include Dry::Monads[:result]

        ##
        # @param question_repo [Question] The repository for questions.
        # @param add_select_option_command [Domains::SurveyDesign::SurveyQuestion::AddSelectOptionCommand] The command to add a select option.
        # @param command_handler_factory [SurveyDesign::Commands::Factory] The survey design command handler factory.
        # @param logger [Splunk::Logger] The logger for logging messages.
        def initialize(
          question_repo: Question.extras(read: {mode: :secondary}),
          select_option_repo: ->(question_aggregate_id:, select_option_id:) {
            question = Question.extras(read: {mode: :secondary}).where(aggregate_id: question_aggregate_id).first
            question.select_options.where(select_option_id: select_option_id).first
          },
          add_select_option_command: Domains::SurveyDesign::SurveyQuestion::AddSelectOptionCommand,
          command_handler_factory: SurveyDesign::Commands::Factory.new,
          logger: Splunk::Logger.new(Rails.logger)
        )
          @question_repo = question_repo
          @select_option_repo = select_option_repo
          @add_select_option_command = add_select_option_command
          @command_handler_factory = command_handler_factory
          @logger = logger
        end

        # @param question_aggregate_id [UUID] The aggregate ID of the question.
        # @param select_options_specifications [Array] The specifications for the select options.
        # @return [Dry::Monads::Result] The result of the operation. Success or Failure.
        def call(question_aggregate_id:, select_options_specifications:, metadata:)
          question = question_repo.where(aggregate_id: question_aggregate_id).first
          raise SelectOptionsCreationError, "Question not found" unless question

          raise SelectOptionsCreationError, "Invalid question type: '#{question.class}'" unless question.select_question?

          data = select_options_specifications.map { |select_option_specifications|
            specs = select_option_specifications.deep_symbolize_keys
            # Generate positioned_after_select_option_id to determine select option order for the question.
            desired_position = specs[:sort_term] && specs[:sort_term][:en].to_i
            positioned_after_select_option_id = find_previous_select_option_id(position: desired_position, question: question)

            result = add_select_option_command.validate(
              aggregate_id: question_aggregate_id,
              select_option_id: specs[:select_option_id],
              positioned_after_select_option_id: positioned_after_select_option_id, # When this field is nil, the command sets default order.
              value: specs[:value_translations],
              added_at: Time.now.utc
            )
            raise SelectOptionsCreationError, result.errors.to_h if result.failure?

            command = add_select_option_command.new(result.to_h)
            add_select_option_command_handler = command_handler_factory.handler_for(survey_id: question.survey_aggregate_id, command_class: add_select_option_command)
            handler_result = add_select_option_command_handler.call(command: command, metadata: metadata)

            raise SelectOptionsCreationError, "Failed adding a select option" unless handler_result == "success"

            select_option = select_option_repo.call(question_aggregate_id: question_aggregate_id, select_option_id: specs[:select_option_id])

            {id: select_option.id.to_s, aggregate_id: select_option.select_option_id}
          }.sort_by { |so| so[:id] } # Sort by mongo id to enable test easily.

          Success({status: :ok, data: data})
        rescue SelectOptionsCreationError => e
          handle_failure(status: :bad_request, message: e.message, question_aggregate_id: question_aggregate_id)
        rescue => e
          handle_failure(status: :internal_server_error, message: e.message, question_aggregate_id: question_aggregate_id)
        end

        private

        attr_reader :question_repo, :select_option_repo, :add_select_option_command, :command_handler_factory, :logger

        def find_previous_select_option_id(position:, question:)
          return if position.to_i <= 0

          previous_position = position.to_i - 1
          # See Projectors::QuestionProjector#reorder_select_options for how sort_term value is defined.
          formatted_sort_term = format(SelectOption::SORT_ORDER_FORMAT, previous_position)

          question.reload.select_options.where(sort_term: formatted_sort_term).pluck(:select_option_id).first
        end

        def handle_failure(status:, message:, question_aggregate_id:)
          logger.log(
            app: "murmur",
            message: message,
            module: self.class.name,
            status: status,
            question_aggregate_id: question_aggregate_id
          )

          Failure({status: status, message: message})
        end
      end
    end
  end
end
