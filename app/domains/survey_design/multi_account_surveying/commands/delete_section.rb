module SurveyDesign
  module MultiAccountSurveying
    module Commands
      class ENPSProtectionError < StandardError; end
      class InvalidParameterError < StandardError; end
      class DeletionFailedError < StandardError; end
      class DeleteSection
        include Dry::Monads[:result]

        # @param section_repo [Theme] The repository for sections.
        # @param survey_repo [Survey] The repository for surveys.
        # @param stq_repo [SurveyToQuestion] The repository for survye to questions.
        # @param delete_section_command [Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionCommand] The command to remove a section.
        # @param enps_protection_checker [ENPSProtection] The checker for ENPS protection.
        # @param logger [Splunk::Logger] The logger for logging messages.
        # @param command_handler_factory [SurveyDesign::Commands::Factory] The survey design command handler factory.
        def initialize(
          section_repo: Theme.extras(read: {mode: :secondary}),
          survey_repo: Survey.extras(read: {mode: :secondary}),
          stq_repo: ->(survey_id:, section_id:) {
            survey = Survey.where(aggregate_id: survey_id).first
            section = Theme.where(section_id: section_id).first
            survey.stqs.where(theme_id: section.id, status: :active)
          },
          delete_section_command: Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionCommand,
          enps_protection_checker: ENPSProtection,
          logger: Splunk::Logger.new(Rails.logger),
          command_handler_factory: SurveyDesign::Commands::Factory.new
        )
          @section_repo = section_repo
          @survey_repo = survey_repo
          @stq_repo = stq_repo
          @delete_section_command = delete_section_command
          @enps_protection_checker = enps_protection_checker
          @logger = logger
          @command_handler_factory = command_handler_factory
        end

        # Removes a section from a survey.
        # @param survey_aggregate_id [String] The aggregate ID of the survey.
        # @param section_id [String] The ID of the section to be removed.
        # @param metadata [EventFramework::Event::Metadata] The metadata for the event.
        # @return [Dry::Monads::Result] The result of the operation.
        # @return [Failure] If the operation fails, it returns a failure with the status and message.
        def call(survey_aggregate_id:, section_id:, metadata:)
          # Block modification of ENPS 11 factor
          if enps_protection_checker.enps_11_section?(section_id)
            raise ENPSProtectionError, "Blocked attempt to delete ENPS 11 section"
          end

          survey = survey_repo.where(aggregate_id: survey_aggregate_id).first
          raise InvalidParameterError, "Survey not found" unless survey
          raise InvalidParameterError, "Survey is not event sourced" unless survey.event_sourced?

          section = section_repo.where(section_id: section_id, survey_id: survey.id.to_s).first
          if section.nil? || section.status == :deleted
            return Success({status: :no_content})
          end

          survey_to_questions = stq_repo.call(survey_id: survey_aggregate_id, section_id: section_id)
          raise DeletionFailedError, "Section has questions" if survey_to_questions.any?

          result = delete_section_command.validate(
            aggregate_id: survey.survey_capture_layout_id,
            section_id: section_id,
            removed_at: Time.now.utc
          )

          raise InvalidParameterError, result.errors.to_h if result.failure?

          command = delete_section_command.new(result.to_h)
          delete_section_command_handler = command_handler_factory.handler_for(survey_id: survey_aggregate_id, command_class: delete_section_command)
          handler_result = delete_section_command_handler.call(command: command, metadata: metadata)

          # handle_result can be a failure or success
          # It does not return failure message
          raise DeletionFailedError, "Failed deleting section" unless handler_result == "success"

          Success({status: :accepted})
        rescue ENPSProtectionError, InvalidParameterError, DeletionFailedError => e
          handle_failure(status: :bad_request, message: e.message, survey_aggregate_id: survey_aggregate_id)
        rescue => e
          handle_failure(status: :internal_server_error, message: e.message, survey_aggregate_id: survey_aggregate_id)
        end

        private

        attr_reader :section_repo, :survey_repo, :stq_repo, :delete_section_command,
          :enps_protection_checker, :logger, :command_handler_factory

        def handle_failure(status:, message:, survey_aggregate_id:)
          logger.log(
            app: "murmur",
            message: message,
            module: self.class.name,
            status: status,
            survey_aggregate_id: survey_aggregate_id
          )

          Failure({status: status, message: message})
        end
      end
    end
  end
end
