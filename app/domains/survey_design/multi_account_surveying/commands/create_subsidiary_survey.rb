module SurveyDesign
  module MultiAccountSurveying
    module Commands
      class InvalidNameError < StandardError; end

      class DeletionError < StandardError; end
      class CreateSubsidiarySurvey
        include Dry::Monads[:result]

        # @param survey_generator [SurveyTemplates::Commands::CreateSurveyFromTemplate] The command to create a survey from a template
        # @param survey_repo [Survey] The repository for surveys
        # @param survey_template_repo [SurveyTemplate] The repository for survey templates
        # @param account_repo [Account] The repository for accounts
        # @param rename_command [Domains::SurveyDesign::Survey::RenameCommand] The command for renaming surveys.
        # @param command_handler_factory [SurveyDesign::Commands::Factory] The survey design command handler factory.
        # @param question_deleter [Questions::Commands::DeleteQuestion] The command to delete questions
        # @param factor_deleter [SurveyFactors::Commands::DeleteFactor] The command to delete factors
        # @param section_deleter [Sections::Commands::RemoveSection] The command to delete sections
        # @param logger [Splunk::Logger] The logger for logging errors
        def initialize(
          survey_generator: SurveyTemplates::Commands::CreateSurveyFromTemplate.new,
          survey_repo: Survey,
          survey_template_repo: SurveyTemplate.extras(read: {mode: :secondary}),
          account_repo: Account.extras(read: {mode: :secondary}),
          rename_command: Domains::SurveyDesign::Survey::RenameCommand,
          command_handler_factory: SurveyDesign::Commands::Factory.new,
          question_deleter: Questions::Commands::DeleteQuestion.new,
          factor_deleter: SurveyFactors::Commands::DeleteFactor.new,
          section_deleter: Sections::Commands::RemoveSection.new,
          logger: Splunk::Logger.new(Rails.logger)
        )
          @survey_generator = survey_generator
          @survey_repo = survey_repo
          @survey_template_repo = survey_template_repo
          @account_repo = account_repo
          @rename_command = rename_command
          @command_handler_factory = command_handler_factory
          @question_deleter = question_deleter
          @factor_deleter = factor_deleter
          @section_deleter = section_deleter
          @logger = logger
        end

        # Creates a new survey for a subsidiary account
        # @param survey_specifications [Hash] The specifications for the survey to be updated.
        # @param account_aggregate_id [UUID] The aggregate ID of the account to create the survey for
        # @param metadata [EventFramework::Event::Metadata] Additional metadata for the survey
        # @param survey_template [SurveyTemplate] The survey template to use for creating the survey
        # @param account [Account] The account to create the survey for
        # @param account_admin [Person] The account admin to create the survey for
        # @return [Success] if the survey was created successfully
        # @return [Failure] if the survey could not be created
        def call(survey_specifications:, account_aggregate_id:, metadata:, survey_template: nil, account: nil, account_admin: nil)
          specs = survey_specifications.deep_stringify_keys
          survey_aggregate_id = specs["aggregate_id"]
          return handle_failure(status: :bad_request, message: "Survey aggregate id not provided", account_aggregate_id: account_aggregate_id) unless survey_aggregate_id
          # We only allow creating snapshot engagement surveys
          return handle_failure(status: :bad_request, message: "Survey must be a snapshot engagement", account_aggregate_id: account_aggregate_id) unless snapshot_engagement_specs?(specs)

          survey_template ||= find_survey_template
          account ||= find_account(account_aggregate_id)
          account_admin ||= find_account_admin(account)

          # Raise an error if the survey template, account, or account admin is not found
          return handle_failure(status: :bad_request, message: "Invalid survey template", account_aggregate_id: account_aggregate_id) unless survey_template
          return handle_failure(status: :bad_request, message: "Invalid account", account_aggregate_id: account_aggregate_id) unless account
          return handle_failure(status: :bad_request, message: "Invalid admin", account_aggregate_id: account_aggregate_id) unless account_admin

          survey = survey_generator.call(
            template: survey_template,
            user: account_admin,
            account: account,
            aggregate_id_override: survey_aggregate_id,
            authorization: nil # This sets permission for participant filter. I don't think we need to assign this upon creation
          )

          # Update name
          update_survey_name(survey_aggregate_id, specs["name_translations"], metadata)
          survey.org_survey_aggregate_id = specs["org_survey_aggregate_id"]
          survey.org_survey_region = specs["org_survey_region"].to_sym
          survey.type = specs["type"].to_sym
          survey.survey_period_type = specs["survey_period_type"].to_sym

          # Delete questions, factors and themes that come from the template
          delete_questions(survey, metadata)
          delete_factors(survey)
          delete_sections(survey, metadata)

          survey.update!

          Success({status: :created, data: {id: survey.id.to_s, aggregate_id: survey.aggregate_id}})
        rescue Mongoid::Errors::Validations => e
          handle_failure(status: :bad_request, message: e.message, account_aggregate_id: account_aggregate_id)
        rescue DeletionError, InvalidNameError => e
          # Delete the survey if deletion fails
          survey_repo.where(aggregate_id: survey_aggregate_id).delete_all
          handle_failure(status: :bad_request, message: e.message, account_aggregate_id: account_aggregate_id)
        rescue => e
          handle_failure(status: :internal_server_error, message: e.message, account_aggregate_id: account_aggregate_id)
        end

        private

        attr_reader :survey_generator, :survey_repo, :survey_template_repo, :account_repo,
          :rename_command, :command_handler_factory, :question_deleter, :factor_deleter, :section_deleter, :logger

        def handle_failure(status:, message:, account_aggregate_id:)
          logger.log(
            app: "murmur",
            message: message,
            module: self.class.name,
            status: status,
            account_aggregate_id: account_aggregate_id
          )

          Failure({status: status, message: message})
        end

        # Return a custom survey template with a single dummy question
        # @return [SurveyTemplate]
        def find_survey_template
          # Only fetching the custom survey template with a single dummy question
          # There are other templates with a single question, but they are custom unattributed survey
          # There are other custom survey templates with multiple questions
          templates = survey_template_repo.where("metadata.template_type": "custom_survey")
          return unless templates.present?

          templates.find { |st| st["content"]["questions"].count <= 1 }
        end

        def find_account(account_aggregate_id)
          # Fetch the account from the repository
          account_repo.where(aggregate_id: account_aggregate_id).first
        end

        def find_account_admin(account)
          account&.administrators&.first
        end

        def update_survey_name(survey_aggregate_id, name_translations, metadata)
          name_translations.each do |locale, name|
            update_name_translation(survey_aggregate_id, name, locale, metadata)
          end
        end

        def snapshot_engagement_specs?(specs)
          specs["type"] == "engagement" && specs["survey_period_type"] == "snapshot"
        end

        def update_name_translation(survey_aggregate_id, name, locale, metadata)
          result = rename_command.validate(
            aggregate_id: survey_aggregate_id,
            name: name,
            locale: locale,
            renamed_at: Time.now.utc
          )

          raise InvalidNameError, result.errors.to_h.to_s if result.failure?

          command = rename_command.new(result.to_h)
          rename_command_handler = command_handler_factory.handler_for(survey_id: survey_aggregate_id, command_class: rename_command)
          rename_command_handler.call(command: command, metadata: metadata)
        end

        def delete_questions(survey, metadata)
          results = survey.stqs.map { |stq|
            question_deleter.call(
              survey: survey,
              survey_to_question: stq,
              metadata: metadata
            )
          }

          ensure_no_deletion_failures(results)
        end

        def delete_factors(survey)
          results = survey.factors.map { |factor|
            result = factor_deleter.call(
              survey_id: survey.id,
              factor_aggregate_id: factor.aggregate_id
            )

            # DeleteFactor command returns a tuple with status and message
            if result.first == :ok
              Success({})
            else
              # If the deletion fails, we need to return a failure result
              # with the status and message from the command
              Failure({status: result.first, message: result.last})
            end
          }

          ensure_no_deletion_failures(results)
        end

        def delete_sections(survey, metadata)
          results = survey.themes.map { |theme|
            section_deleter.call(
              survey: survey,
              section_to_remove: theme,
              metadata: metadata
            )
          }

          ensure_no_deletion_failures(results)
        end

        def ensure_no_deletion_failures(results)
          _success, failed_results = results.partition { |result| result.success? }

          # get all the failed messages
          if failed_results.present?
            failure_messages = failed_results.map { |result| result.failure[:message] }.join(", ")
            raise DeletionError.new, failure_messages
          end
        end
      end
    end
  end
end
