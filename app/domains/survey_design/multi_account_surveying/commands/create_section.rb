module SurveyDesign
  module MultiAccountSurveying
    module Commands
      class InvalidParameterError < StandardError; end
      class CreateSection
        include Dry::Monads[:result]

        # @param survey_repo [Survey] The repository for surveys.
        # @param section_repo [Theme] The repository for sections.
        # @param add_section_command [Domains::SurveyDesign::SurveyCaptureLayout::AddSectionCommand] The command to add a section.
        # @param command_handler_factory [SurveyDesign::Commands::Factory] The survey design command handler factory.
        # @param logger [Splunk::Logger] The logger for logging messages.
        def initialize(
          survey_repo: Survey.extras(read: {mode: :secondary}),
          section_repo: Theme,
          add_section_command: Domains::SurveyDesign::SurveyCaptureLayout::AddSectionCommand,
          command_handler_factory: SurveyDesign::Commands::Factory.new,
          logger: Splunk::Logger.new(Rails.logger)
        )
          @survey_repo = survey_repo
          @section_repo = section_repo
          @add_section_command = add_section_command
          @command_handler_factory = command_handler_factory
          @logger = logger
        end

        # @param survey_aggregate_id [String] The aggregate ID of the survey.
        # @param section_specifications [Hash] The specifications for the section.
        # @param metadata [EventFramework::Event::Metadata] The metadata for the event.
        # @return [Dry::Monads::Result] The result of the operation.
        # @return [Failure] If the operation fails, it returns a failure with the status and message.
        def call(survey_aggregate_id:, section_specifications:, metadata:)
          # XXX: https://github.com/dry-rb/dry-schema/issues/296
          specs = section_specifications.deep_symbolize_keys
          raise InvalidParameterError, "Unsupported Intended Purpose" if specs[:intended_purpose] == Domains::Enums::SectionIntendedPurposes::INTERVIEW

          survey = survey_repo.where(aggregate_id: survey_aggregate_id).first
          return handle_failure(status: :bad_request, message: "Survey not found", survey_aggregate_id: survey_aggregate_id) unless survey

          # Generate positioned_after_section_id
          positioned_after_section_id = find_previous_section_id(specs[:position], survey)

          result = add_section_command.validate(
            aggregate_id: survey.survey_capture_layout_id,
            section_id: specs[:section_id],
            name: specs[:name_translations],
            short_description: specs[:short_description],
            long_description: specs[:long_description],
            intended_purpose: specs[:intended_purpose],
            code: specs[:code],
            positioned_after_section_id: positioned_after_section_id,
            added_at: Time.now.utc
          )

          raise InvalidParameterError, result.errors.to_h if result.failure?

          command = add_section_command.new(result.to_h)
          add_section_command_handler = command_handler_factory.handler_for(survey_id: survey_aggregate_id, command_class: add_section_command)
          handler_result = add_section_command_handler.call(command: command, metadata: metadata)
          # handle_result can be a failure or success
          # It does not return failure message
          return handle_failure(status: :bad_request, message: "Failed adding section", survey_aggregate_id: survey_aggregate_id) unless handler_result == "success"

          section = section_repo.where(section_id: specs[:section_id]).first

          Success({status: :created, data: {id: section.id.to_s, aggregate_id: section.section_id}})
        rescue Mongoid::Errors::Validations => e
          handle_failure(status: :bad_request, message: e.message, survey_aggregate_id: survey_aggregate_id)
        rescue InvalidParameterError => e
          handle_failure(status: :bad_request, message: e.message, survey_aggregate_id: survey_aggregate_id)
        rescue => e
          handle_failure(status: :internal_server_error, message: e.message, survey_aggregate_id: survey_aggregate_id)
        end

        private

        attr_reader :survey_repo, :section_repo, :add_section_command, :command_handler_factory, :logger

        def find_previous_section_id(position, survey)
          return if position.to_i <= 0

          previous_section_position = position.to_i - 1

          survey.themes.where(order: previous_section_position).pluck(:section_id).first
        end

        def handle_failure(status:, message:, survey_aggregate_id:)
          logger.log(
            app: "murmur",
            message: message,
            module: self.class.name,
            status: status,
            survey_aggregate_id: survey_aggregate_id
          )

          Failure({status: status, message: message})
        end
      end
    end
  end
end
