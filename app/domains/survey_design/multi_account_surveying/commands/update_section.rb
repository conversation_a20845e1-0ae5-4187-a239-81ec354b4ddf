module SurveyDesign
  module MultiAccountSurveying
    module Commands
      class ENPSProtectionError < StandardError; end
      class InvalidParameterError < StandardError; end
      class SectionNotFoundError < StandardError; end
      class SectionUpdateError < StandardError; end
      class UnknownSectionIntendedPurposeError < StandardError; end
      class UpdateSection
        include Dry::Monads[:result]

        # @param section_repo [Domains::SurveyDesign::SurveyCaptureLayout::SectionRepository] The repository for sections.
        # @param survey_repo [Domains::SurveyDesign::SurveyCaptureLayout::SurveyRepository] The repository for surveys.
        # @param move_section_command [Domains::SurveyDesign::SurveyCaptureLayout::MoveSectionCommand] The command to move a section.
        # @param rename_section_command [Domains::SurveyDesign::SurveyCaptureLayout::RenameSectionCommand] The command to rename a section.
        # @param change_section_long_description_command [Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionLongDescriptionCommand] The command to change a section's long description.
        # @param change_section_short_description_command [Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionShortDescriptionCommand] The command to change a section's short description.
        # @param enps_protection_checker [ENPSProtection] The service to check ENPS protection.
        # @param logger [Splunk::Logger] The logger for logging messages.
        # @param command_handler_factory [SurveyDesign::Commands::Factory] The survey design command handler factory.
        def initialize(
          section_repo: Theme.extras(read: {mode: :secondary}),
          survey_repo: Survey.extras(read: {mode: :secondary}),
          move_section_command: Domains::SurveyDesign::SurveyCaptureLayout::MoveSectionCommand,
          rename_section_command: Domains::SurveyDesign::SurveyCaptureLayout::RenameSectionCommand,
          change_section_long_description_command: Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionLongDescriptionCommand,
          change_section_short_description_command: Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionShortDescriptionCommand,
          enps_protection_checker: ENPSProtection,
          logger: Splunk::Logger.new(Rails.logger),
          command_handler_factory: SurveyDesign::Commands::Factory.new
        )
          @section_repo = section_repo
          @survey_repo = survey_repo
          @move_section_command = move_section_command
          @rename_section_command = rename_section_command
          @change_section_long_description_command = change_section_long_description_command
          @change_section_short_description_command = change_section_short_description_command
          @enps_protection_checker = enps_protection_checker
          @logger = logger
          @command_handler_factory = command_handler_factory
        end

        # Updates an existing section in a survey with the provided specifications.
        # @param survey_aggregate_id [String] The aggregate ID of the survey.
        # @param section_specifications [Hash] The specifications for the section to update.
        # @param metadata [EventFramework::Event::Metadata] The metadata for the event.
        # @return [Dry::Monads::Result] The result of the operation.
        # @return [Failure] If the operation fails, it returns a failure with the status and message.
        def call(survey_aggregate_id:, section_specifications:, metadata:)
          specs = section_specifications.deep_stringify_keys
          section_id = specs["section_id"]
          # Block modification of ENPS 11 factor
          if enps_protection_checker.enps_11_section?(section_id)
            raise ENPSProtectionError, "Blocked attempt to delete ENPS 11 section"
          end

          survey = survey_repo.where(aggregate_id: survey_aggregate_id).first
          raise InvalidParameterError, "Survey not found" unless survey
          raise InvalidParameterError, "Survey is not event sourced" unless survey.event_sourced?

          section = section_repo.where(section_id: section_id, survey_id: survey.id.to_s).first
          raise SectionNotFoundError, "Section not found" unless section

          aggregate_id = survey.survey_capture_layout_id

          # Intended Purpose
          if specs["intended_purpose"]
            # intended_purpose is not a mongo field, it determines the type of section
            updated_type = type(specs["intended_purpose"])
            raise InvalidParameterError, "Not allowed to change intended purpose" if updated_type != section.type
          end

          # Position
          if specs["position"]
            position = specs["position"].to_i
            change_section_position(section_id, position, survey, metadata) if position != section.order
          end

          # Name Translations
          if specs["name_translations"]
            updated_names = find_translation_changes(section.name_translations, specs["name_translations"])
            updated_names.each do |locale, text|
              rename_section(section_id, text, locale, aggregate_id, survey, metadata)
            end
          end

          # Long Description Translations
          if specs["long_description"]
            updated_desc = find_translation_changes(section.long_desc_translations, specs["long_description"])
            updated_desc.each do |locale, text|
              change_section_long_description(section_id, text, locale, aggregate_id, survey, metadata)
            end
          end

          # Short Description Translations
          if specs["short_description"]
            updated_desc = find_translation_changes(section.short_desc_translations, specs["short_description"])
            updated_desc.each do |locale, text|
              change_section_short_description(section_id, text, locale, aggregate_id, survey, metadata)
            end
          end

          Success({status: :created, data: {id: section.id.to_s, aggregate_id: section.section_id}})
        rescue Mongoid::Errors::Validations, InvalidParameterError, ENPSProtectionError, I18n::InvalidLocale, UnknownSectionIntendedPurposeError, SectionUpdateError => e
          handle_failure(status: :bad_request, message: e.message, survey_aggregate_id: survey_aggregate_id)
        rescue SectionNotFoundError => e
          handle_failure(status: :not_found, message: e.message, survey_aggregate_id: survey_aggregate_id)
        rescue => e
          handle_failure(status: :internal_server_error, message: e.message, survey_aggregate_id: survey_aggregate_id)
        end

        private

        attr_reader :section_repo, :survey_repo, :move_section_command, :rename_section_command,
          :change_section_long_description_command, :change_section_short_description_command,
          :enps_protection_checker, :logger, :command_handler_factory

        def find_translation_changes(existing_translations, new_translations)
          new_translations_map = new_translations.map { |translation|
            [translation["locale"], translation["text"]]
          }.to_h

          new_translations_map.select { |locale, text| existing_translations[locale] != text }
        end

        def change_section_position(section_id, new_position, survey, metadata)
          # Generate positioned_after_section_id
          positioned_after_section_id = find_previous_section_id(section_id, new_position, survey.id.to_s)

          result = move_section_command.validate(
            aggregate_id: survey.survey_capture_layout_id,
            section_id: section_id,
            positioned_after_section_id: positioned_after_section_id,
            moved_at: Time.now.utc
          )

          raise InvalidParameterError, result.errors.to_h if result.failure?

          command = move_section_command.new(result.to_h)
          move_section_command_handler = command_handler_factory.handler_for(survey_id: survey.aggregate_id, command_class: move_section_command)
          handler_result = move_section_command_handler.call(command: command, metadata: metadata)
          raise SectionUpdateError, "Failed to move section" unless handler_result == "success"
        end

        def find_previous_section_id(section_id, position, survey_id)
          return if position <= 0

          previous_section_position = position - 1

          previous_section_id = section_repo.where(survey_id: survey_id, order: previous_section_position).pluck(:section_id).first

          if previous_section_id == section_id
            # Finding next section if the section is moving to the next position
            # Because next section with move to the same position as current section
            return find_previous_section_id(section_id, position + 1, survey_id)
          end

          previous_section_id
        end

        def rename_section(section_id, new_name, locale, aggregate_id, survey, metadata)
          result = rename_section_command.validate(
            aggregate_id: aggregate_id,
            section_id: section_id,
            name: new_name,
            locale: locale,
            renamed_at: Time.now.utc
          )

          raise InvalidParameterError, result.errors.to_h if result.failure?

          command = rename_section_command.new(result.to_h)
          rename_section_command_handler = command_handler_factory.handler_for(survey_id: survey.aggregate_id, command_class: rename_section_command)
          handler_result = rename_section_command_handler.call(command: command, metadata: metadata)
          raise SectionUpdateError, "Failed renaming section" unless handler_result == "success"
        end

        def change_section_long_description(section_id, new_long_description, locale, aggregate_id, survey, metadata)
          result = change_section_long_description_command.validate(
            aggregate_id: aggregate_id,
            section_id: section_id,
            text: new_long_description,
            locale: locale,
            changed_at: Time.now.utc
          )

          raise InvalidParameterError, result.errors.to_h if result.failure?

          command = change_section_long_description_command.new(result.to_h)
          change_section_long_description_command_handler = command_handler_factory.handler_for(survey_id: survey.aggregate_id, command_class: change_section_long_description_command)
          handler_result = change_section_long_description_command_handler.call(command: command, metadata: metadata)
          raise SectionUpdateError, "Failed changing long description" unless handler_result == "success"
        end

        def change_section_short_description(section_id, new_short_description, locale, aggregate_id, survey, metadata)
          result = change_section_short_description_command.validate(
            aggregate_id: aggregate_id,
            section_id: section_id,
            text: new_short_description,
            locale: locale,
            changed_at: Time.now.utc
          )

          raise InvalidParameterError, result.errors.to_h if result.failure?

          command = change_section_short_description_command.new(result.to_h)
          change_section_short_description_command_handler = command_handler_factory.handler_for(survey_id: survey.aggregate_id, command_class: change_section_short_description_command)
          handler_result = change_section_short_description_command_handler.call(command: command, metadata: metadata)
          raise SectionUpdateError, "Failed changing short description" unless handler_result == "success"
        end

        def handle_failure(status:, message:, survey_aggregate_id:)
          logger.log(
            app: "murmur",
            message: message,
            module: self.class.name,
            status: status,
            survey_aggregate_id: survey_aggregate_id
          )

          Failure({status: status, message: message})
        end

        def type(intended_purpose)
          case intended_purpose
          when Domains::Enums::SectionIntendedPurposes::DEMOGRAPHIC
            :segment
          when Domains::Enums::SectionIntendedPurposes::STANDARD
            :culture
          when Domains::Enums::SectionIntendedPurposes::INTERVIEW
            :interview
          else
            raise UnknownSectionIntendedPurposeError, "Unable to find section type for intended_purpose: #{intended_purpose}"
          end
        end
      end
    end
  end
end
