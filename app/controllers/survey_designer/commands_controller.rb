module SurveyDesigner
  class CommandsController < AdministerSurveyController
    LOGGER = Splunk::Logger.new(Rails.logger)

    component :survey

    include EventFramework::ControllerHelpers::MetadataHelper
    include EventFramework::ControllerHelpers::CommandHelper

    include VerifySameOrigin
    replace_csrf_with_origin_check
    allows_jwt_authentication
    allows_access_to :all_authenticated_users
    before_action :check_permission

    # Actions related to question
    before_action :check_enps_protection_question, only: [:make_question_mandatory, :make_question_optional, :delete_question, :change_question_type, :change_question_text, :change_question_short_text, :change_rating_scale, :hide_question_from_capture, :add_answer_condition_to_question, :remove_answer_condition_from_question]
    before_action :check_enps_protection_question_position, only: [:position_question]

    # Actions related to section
    before_action :check_enps_protection_section, only: [:add_question, :remove_section, :rename_section, :move_section, :restore_section, :change_section_long_description, :change_section_short_description, :remove_section_descriptions]

    expose :survey, fetch: -> do
      # We need to fetch from unscoped criteria to fetch the deleted survey from Paranoia in order to restore them
      if action_name.to_sym == :restore_survey
        Survey.unscoped.find(params[:survey_id])
      else
        Surveys::Queries::FindByIdOrAggregateId.new.call(id: params[:survey_id])
      end
    end

    def add_section
      handle(Domains::SurveyDesign::SurveyCaptureLayout::AddSectionCommand, {
        added_at: Time.now.utc
      }) do
        # HACK: we create a factor when a section gets a name (only for culture/standard sections)
        # (mimicking the section creation on the old survey designer)
        if request.request_parameters[:intended_purpose] == "standard"
          name, locale = name_in_default_locale(request.request_parameters)
          create_factor(survey: survey, locale: locale, name: name)
        end
      end
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def make_question_mandatory
      handle(Domains::SurveyDesign::SurveyQuestion::MakeMandatoryCommand, {
        made_mandatory_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def make_question_optional
      handle(Domains::SurveyDesign::SurveyQuestion::MakeOptionalCommand, {
        made_optional_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def remove_section
      handle(Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionCommand, {
        removed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def rename_section
      old_section = survey.themes.find_by(section_id: params[:section_id])
      old_section_name = I18n.with_locale(default_locale) { old_section.name }

      handle(Domains::SurveyDesign::SurveyCaptureLayout::RenameSectionCommand, {
        renamed_at: Time.now.utc
      }) do
        # HACK: we create a factor when a section gets a name (only for culture/standard sections)
        # (mimicking the section creation on the old survey designer)
        if old_section.type == :culture
          if old_section_name.strip.empty? && old_section_name != params[:name].strip
            create_factor(survey: survey, locale: params[:locale], name: params[:name])
          end
        end
      end
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def add_question
      handle(Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand, {
        added_at: Time.now.utc,
        # NOTE: Defaulting intended purpose for now on this command to feedback.
        # This will need to be adjusted when the New Survey Designer is updated to
        # support more survey types, such as "onboard" and "exit".
        intended_purpose: Domains::Enums::SurveyQuestionIntendedPurposes::FEEDBACK
      }) do
        # HACK: if the question belongs to a section with a matching factor, we assign this factor to the question
        # (mimicking the section creation on the old survey designer)
        question = Question.find_by(aggregate_id: params[:aggregate_id])
        survey_to_question = survey.reload.survey_to_questions.find_for_question(question.id)
        attach_factor(survey: survey, survey_to_question: survey_to_question)
      end
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def add_demographic_question
      handle(Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand, {
        intended_purpose: Domains::Enums::SurveyQuestionIntendedPurposes::DEMOGRAPHIC,
        question_type: Domains::Enums::SurveyQuestionTypes::SELECT,
        selection_limit: 1,
        cloned_from_curated_question_reference_code: nil,
        cloned_from_survey_question_id: nil,
        added_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def delete_question
      handle(Domains::SurveyDesign::SurveyQuestion::DeleteCommand, {
        deleted_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def change_question_type
      handle(Domains::SurveyDesign::SurveyQuestion::ChangeQuestionTypeCommand, {
        changed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def add_select_option
      handle(Domains::SurveyDesign::SurveyQuestion::AddSelectOptionCommand, {
        added_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def add_select_options
      handle(Domains::SurveyDesign::SurveyQuestion::AddSelectOptionsCommand, {
        added_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error => e
      render json: {error: e.inspect}, status: :internal_server_error
    end

    def change_question_text
      handle(Domains::SurveyDesign::SurveyQuestion::ChangeTextCommand, {
        changed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def change_selection_limit
      handle(Domains::SurveyDesign::SurveyQuestion::ChangeSelectionLimitCommand, {
        changed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def change_rating_scale
      handle(Domains::SurveyDesign::SurveyQuestion::ChangeRatingScaleCommand, {
        changed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def rename_select_option
      handle(Domains::SurveyDesign::SurveyQuestion::RenameSelectOptionCommand, {
        renamed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def delete_select_option
      handle(Domains::SurveyDesign::SurveyQuestion::DeleteSelectOptionCommand, {
        deleted_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def enable_other_select_option
      handle(Domains::SurveyDesign::SurveyQuestion::EnableOtherSelectOptionCommand, {
        enabled_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def disable_other_select_option
      handle(Domains::SurveyDesign::SurveyQuestion::DisableOtherSelectOptionCommand, {
        disabled_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def change_other_select_option_label
      handle(
        Domains::SurveyDesign::SurveyQuestion::ChangeOtherSelectOptionLabel,
        {changed_at: Time.now.utc}
      )
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def move_section
      handle(Domains::SurveyDesign::SurveyCaptureLayout::MoveSectionCommand, {
        moved_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def position_question
      handle(Domains::SurveyDesign::SurveyCaptureLayout::PositionQuestionCommand, {
        moved_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def position_demographic_sections
      handle(Domains::SurveyDesign::SurveyCaptureLayout::PositionDemographicSectionsCommand, {
        moved_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def move_select_option
      handle(Domains::SurveyDesign::SurveyQuestion::MoveSelectOptionCommand, {
        moved_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def restore_section
      handle(Domains::SurveyDesign::SurveyCaptureLayout::RestoreSectionCommand, {
        restored_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def restore_question
      handle(Domains::SurveyDesign::SurveyQuestion::RestoreCommand, {
        restored_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def hide_question_from_capture
      handle(Domains::SurveyDesign::SurveyCaptureLayout::HideQuestionFromCaptureCommand, {
        hidden_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def change_section_long_description
      handle(Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionLongDescriptionCommand, {
        changed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def change_section_short_description
      handle(Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionShortDescriptionCommand, {
        changed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def remove_section_descriptions
      handle(Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionDescriptionsCommand, {
        removed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def delete_survey
      handle(Domains::SurveyDesign::Survey::DeleteCommand, {
        deleted_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def restore_survey
      handle(Domains::SurveyDesign::Survey::RestoreCommand, {
        restored_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def add_answer_condition_to_question
      handle(Domains::SurveyDesign::DisplayCondition::AddAnswerConditionToQuestionCommand, {
        added_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def choose_parent_question
      handle(Domains::SurveyDesign::DisplayCondition::ChooseParentQuestionCommand, {
        chosen_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def add_select_option_from_parent_question
      handle(Domains::SurveyDesign::DisplayCondition::AddSelectOptionFromParentQuestionCommand, {
        added_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def remove_select_option_from_parent_question
      handle(Domains::SurveyDesign::DisplayCondition::RemoveSelectOptionFromParentQuestionCommand, {
        removed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def change_question_short_text
      handle(Domains::SurveyDesign::SurveyQuestion::ChangeShortTextCommand, {
        changed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def remove_answer_condition_from_question
      handle(Domains::SurveyDesign::DisplayCondition::RemoveAnswerConditionFromQuestionCommand, {
        removed_at: Time.now.utc
      })
    rescue Domains::Error, EventFramework::Error
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    # Legacy commands

    def add_demographic_condition
      handle(SurveyDesigner::LegacyDomains::SurveyQuestion::AddDemographicCondition::Command)
    rescue
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def add_demographic_condition_value
      handle(SurveyDesigner::LegacyDomains::SurveyQuestion::AddDemographicConditionValue::Command)
    rescue
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def remove_demographic_condition
      handle(SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveDemographicCondition::Command)
    rescue
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def remove_demographic_condition_value
      handle(SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveDemographicConditionValue::Command)
    rescue
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def remove_all_demographic_conditions
      handle(SurveyDesigner::LegacyDomains::SurveyQuestion::RemoveAllDemographicConditions::Command)
    rescue
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def select_demographic_condition_question
      handle(SurveyDesigner::LegacyDomains::SurveyQuestion::SelectConditionDemographicQuestion::Command)
    rescue
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    def set_condition_matcher_type
      handle(SurveyDesigner::LegacyDomains::SurveyQuestion::SetConditionMatcherType::Command)
    rescue
      render json: {error: "generic_command_exception"}, status: :internal_server_error
    end

    private

    def command_factory
      @command_factory ||= SurveyDesign::Commands::Factory.new
    end

    def check_enps_protection_question
      if ENPSProtection.enps_11_question?(params[:aggregate_id])
        log_enps_protection("Blocked attempt to modify/delete ENPS 11 question")
        render_bad_request
      end
    end

    def check_enps_protection_question_position
      if ENPSProtection.enps_11_question?(params[:question_id])
        log_enps_protection("Blocked attempt to change position of ENPS 11 question")
        render_bad_request
      elsif ENPSProtection.enps_11_section?(params[:section_id])
        log_enps_protection("Blocked attempted to move question into ENPS section")
        render_bad_request
      end
    end

    def check_enps_protection_section
      if ENPSProtection.enps_11_section?(params[:section_id])
        log_enps_protection("Blocked attempted to modify ENPS section")
        render_bad_request
      end
    end

    def log_enps_protection(msg)
      LOGGER.log(
        app: "murmur",
        msg: msg,
        module: "commands_controller.#{params[:action]}",
        account_id: current_user&.account&.id.to_s,
        user_id: current_user&.id.to_s,
        query_string: request.query_string,
        path: request.path,
        remote_ip: request.remote_ip
      )
    end

    def check_permission
      render_forbidden unless Authorization.permitted?(
        user_id: current_user.aggregate_id,
        resource_id: account&.aggregate_id,
        permission: Account::Permissions::ADMINISTER_SURVEY
      ) || Authorization.permitted?(
        user_id: current_user.aggregate_id,
        resource_id: survey&.aggregate_id,
        permission: Account::Permissions::ADMINISTER_SURVEY
      )
    end

    def check_event_source_command_disabled?(survey)
      !survey.enabled?(Flags::SURVEY_DESIGNER_COMMANDS_ENABLED)
    end

    def handle(command_class, attributes = {})
      validated_params = validate_params_for_command(request.request_parameters.merge(attributes), command_class)
      if check_event_source_command_disabled?(survey)
        render json: "Temporarily Disabled".to_json, status: :service_unavailable
      elsif validated_params.success?
        command_attributes = validated_params.to_h

        # XXX: https://github.com/dry-rb/dry-schema/issues/296
        command_attributes = command_attributes.deep_symbolize_keys

        command = command_class.new(command_attributes)
        handler = command_factory.handler_for(survey_id: survey.aggregate_id, command_class: command_class)
        handler.call(
          command: command,
          metadata: build_metadata
        )

        yield if block_given?

        render json: "ok".to_json, status: :created
      else
        render json: validated_params.errors(full: true).to_h, status: :unprocessable_entity
      end
    rescue Domains::AlreadyActionedException => e
      log_exception(exception: e, auto_notify: false)
      render json: e.to_json, status: :conflict
    rescue Domains::Error, EventFramework::Error => e
      log_exception(exception: e, auto_notify: true)
      raise
    end

    def log_exception(exception:, auto_notify: true)
      Bugsnag.notify(exception, auto_notify)

      Rails.logger.error(Splunk::Logger::LogfmtFormatter.from_exception(exception))
    end

    def create_factor(survey:, locale:, name:)
      # Just create factors in default locale
      # TODO: We will circle back to this when we start event sourcing factors
      return unless locale == default_locale

      # Don't create factors with empty names, we'll create them the first time
      # they're renamed
      name = name.strip
      return if name.empty?

      # Find or Create a factor with the same name when a section is created
      factor = Factor.find_or_create_detached_from_survey(
        name: name,
        survey: survey,
        locale: locale
      )

      # If the factor with the same name is an ENPS 11 factor, create a new factor
      if ENPSProtection.enps_11_factor?(factor.aggregate_id)
        factor = Factor.create(name: name)
      end

      factor_attributes = {
        status: :active,
        short_desc: name,
        long_desc: name
      }
      survey_service.add_factor(survey, factor, factor_attributes, locale)
    end

    def attach_factor(survey:, survey_to_question:)
      # Run factor section correspondence only based on english locale
      I18n.with_locale(default_locale) do
        english_section_name = survey_to_question.theme.name
        factors = survey.factors.where(name: english_section_name.strip, status: :active)

        factors_without_enps_factor = factors.filter { |factor| !ENPSProtection.enps_11_factor?(factor.aggregate_id) }

        survey_to_question.factors = factors_without_enps_factor

        factors_aggregate_ids = factors.map(&:aggregate_id)
        survey_to_question.question.update(factor_aggregate_ids: factors_aggregate_ids)

        survey_to_question.save!
      end
    end

    def default_locale
      "en"
    end

    def name_in_default_locale(_params)
      name = request.request_parameters[:name].detect { |n| n["locale"] == default_locale } || {}
      [name[:text].to_s, name[:locale]]
    end
  end
end
