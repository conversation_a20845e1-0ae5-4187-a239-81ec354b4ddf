class DemoController < ApplicationController
  include <PERSON><PERSON><PERSON><PERSON><PERSON>
  include Authentication::FusionAuthHelper

  allows_access_to :all_authenticated_users
  component :demo
  layout "demo", only: :index

  CAPTURE_DEMONSTRATION = Appamp::Settings.config["capture_demonstration_ids"]

  # List of all core demo users
  # https://docs.google.com/spreadsheets/d/1kygvD95NVB19snTqqVkWIWqqF78U60tQUIcd32dgMEM/edit?usp=sharing
  ALLOW_LIST = Appamp::Settings.config["demo_users_allow_list"]

  # GET /demo/capture?type=[capture|exit|onboard]
  # GET /demo/capture?sid=[valid survey id]
  def capture
    # Find the type of survey wanted and redirect to it.
    survey_id = params[:sid]
    if survey_id.present?
      id = CAPTURE_DEMONSTRATION.values.detect { |v| v == survey_id }
      return render_not_found if id.nil?
    else
      id = CAPTURE_DEMONSTRATION[params[:type]] || CAPTURE_DEMONSTRATION.first.last
    end
    survey = Survey.where(id: id).first
    return render_not_found if survey.nil?

    path = demonstration_survey_design_path(survey, secret: survey.demonstration_secret, locale: params[:locale])
    redirect_to path
  end

  # Demonstration URL. Puts up the instruction page with a get started
  # button.
  def index
    return redirect_to session_sign_in_path unless current_user && current_user.account.subdomain == Account::CULTURE_AMP_SUBDOMAIN

    current_account = account_from_subdomain

    # New way to demo as Mackenzie Parker in Personal Demo Accounts
    if current_account.region == Region::PDE
      perform_demo_as("Mackenzie Parker")

    # Temporarily leave old code as such to work for accounts like Piedpiper and Hooli.
    # To be deprecated once all sellers are moved over to PDEs.
    else
      return render_not_found unless current_account&.demonstrable?
      demo_user = current_account.demo_user
      return render_not_found unless demo_user

      sign_in_as(demo_user)
      render :index, locals: {subdomain: params[:sd]}
    end
  end

  # GET /demo/user?name=mackenzie-parker
  def demo_as_employee
    param_name = params[:name]
    employee_full_name = param_name.split("-").map(&:capitalize).join(" ")
    perform_demo_as(employee_full_name)
  end

  # Sign the user in and redirect them
  def redirect
    return redirect_to session_sign_in_path unless current_user && current_user.account.subdomain == Account::CULTURE_AMP_SUBDOMAIN

    current_account = account_from_subdomain

    unless current_account.region == Region::PDE
      return render_not_found unless current_account&.demonstrable?
      demo_user = current_account.demo_user
      return render_not_found unless demo_user

      sign_in_as(demo_user)
    end

    redirect_home
  end

  private

  def perform_demo_as(employee_full_name)
    return redirect_to session_sign_in_path unless current_user
    return render_not_found unless ALLOW_LIST.include?(employee_full_name)
    current_account = account_from_subdomain
    return render_not_found unless current_account.region == Region::PDE && current_user.account.subdomain == Account::CULTURE_AMP_SUBDOMAIN

    demo_user = Users::Queries::FindByNameForAccount.new.call(name: employee_full_name, account_aggregate_id: current_account.aggregate_id)
    return render_not_found unless demo_user

    sign_in_as(demo_user)
    render :index, locals: {subdomain: params[:sd]}
  end

  def account_from_subdomain
    subdomain = params[:sd] || request.subdomains.first
    Account.subdomain(subdomain)
  end

  def sign_in_as(user)
    stop_masquerading

    if fusionauth_jwt_post_signin_enabled?(get_subdomain_from_request)
      # Sign-in to FusionAuth using Passwordless Login API
      jwt, refresh_token = fusionauth_passwordless_login(user)

      if jwt && refresh_token
        set_fusionauth_cookies(jwt: jwt, refresh_token: refresh_token)
      end
    else
      sign_in user
    end
    set_user_data_cookie_by_region(user_info(user))
  end
end
