class ParticipantController < AdministerSurveyController
  include Controlamp::DeprecatedModelCentric

  component :survey
  layout false, except: [:index]

  allows_access_to :all_authenticated_users

  rescue_from(Errors::InvalidParticipantError) { |_e| render_unprocessable_entity }

  expose(:people) { people_in_context }

  expose(:participant_ids) { survey.continuous? ? all_participant_ids : all_participant_ids | invited_ids }
  expose(:participant_kiosk_codes_by_id) { survey.kiosk_codes_by_user_id }
  expose(:participant_response_ids_by_id) { survey.response_ids_by_user_id }
  expose(:all_participant_ids) { survey.all_participant_ids.to_set }
  expose(:invited_ids) { survey.invited_participant_ids.to_set }

  expose(:view_by) { survey_demographics_without_self_report }
  expose(:view_by_demographics) { demographics_for_grouping }
  expose(:view_by_so) do
    survey_demographics_without_self_report.question.select_options.detect { |so| so.id.to_s == params[:select_option_id] } if survey_demographics_without_self_report.present?
  end
  expose(:viewable_select_options) { survey_demographics_without_self_report.question.select_options.in(demographic_value_id: active_participants_by_demographic_value.keys) if survey_demographics_without_self_report.present? }
  expose(:master) { account.master_survey }
  expose(:pending_ids) { all_participant_ids - invited_ids }

  expose(:not_selectable_people_ids) { participant_ids - all_selectable_people_ids }
  expose(:all_selectable_people_ids) { survey_participants.all_ids[:selectable_participant_ids] }

  expose(:survey_participants) { ViewModels::SurveyParticipants.for_survey(survey) }

  expose(:exclude_after_date) { survey.exclude_participants_after }
  expose(:participant_titleblock_props) { titleblock_props }
  expose(:survey_configuration_actions) { construct_survey_participant_actions }
  expose(:participant_subtitle_props) { construct_participant_subtitle_props }

  expose(:participant_select_job) { Jobs::SurveyLaunch::ParticipantsSelectJob.where(:survey => survey, :status.in => [:new, :active, :pending, :error]) }
  expose(:participant_select_job_running) { participant_select_job.exists? }

  expose(:new_participant_view) { survey.enabled?(Flags::NEW_PARTICIPATION_LIST_VIEW) }

  expose(:participants_export_props) { {surveyName: survey.name, surveyAggregateId: survey.aggregate_id, exportParticipantsUrl: survey_participants_exports_path, securityToken: form_authenticity_token, data: SurveyDesign::Queries::SurveyDemographics.call(survey: survey)} }

  expose(:all_participant_counts) do
    survey.design? ? Participants::PreLaunch::ParticipantCounts.call(survey: survey) : Participants::PostLaunch::ParticipantCounts.call(survey: survey)
  end
  # any demographic values that do not have active participants will not be represented in this hash
  expose(:active_participants_by_demographic_value) { Participants::Queries::ActiveEmployees.by_demographic_values(survey: survey, demographic_stq: survey_demographics_without_self_report) if survey_demographics_without_self_report.present? }

  expose(:all_demographic_participant_counts) { survey.design? ? Participants::PreLaunch::DemographicParticipantCounts : Participants::PostLaunch::DemographicParticipantCounts }

  expose(:survey_invite_job_id) { survey_invite_job_id }

  expose(:last_bulk_import_props) { Props::LastBulkImportNotification::SurveyParticipantProps.new(survey: survey).to_hash }

  expose(:can_view_employee_data) { can_view_employee_data?(current_user.aggregate_id, account.aggregate_id) }

  expose(:can_manage_survey_participant) { can_manage_survey_participant? }

  before_action only: [:select_all, :deselect_all, :update] do
    head(409) if participant_select_job_running && new_participant_view
  end

  before_action :check_survey_support_admin_permission, except: [:update_exclude_after]
  before_action :check_survey_admin_permission, only: [:update_exclude_after]

  def index
    respond_to do |format|
      format.html do
        @import_props = Props::Participant::ImportProps.new(survey_id: survey.id, survey_aggregate_id: survey.aggregate_id, survey_status: survey.status).to_hash
        if xhr?
          if new_participant_view && params[:term].try(:lstrip).empty?
            render template: "participant/_new_participant_list",
                   locals: survey_participants.overall_counts.merge(so: nil),
                   layout: false
          else
            render template: "participant/_participant_list",
                   locals: survey_participants.overall_counts,
                   layout: false
          end
        end
      end
      format.js do
        render template: "participant/_paginated_people", layout: false if params[:page].present?
      end
    end
  end

  def view_by_index
    if xhr?
      locals = participant_counts
      active_people_ids = view_by_so.nil? || view_by.nil? ? [] : Participants::Queries::ActiveEmployees.by_demographic_value_for_demographic(survey: survey, demographic_stq: view_by, demographic_value_id: view_by_so.demographic_value_id)
      locals[:people] = Person.in(id: active_people_ids).where(account: account).order_by(:name_sort.asc)

      render template: "participant/_participant_list", layout: false, locals: locals
    else
      redirect_to action: :index
    end
  end

  def update
    person = model_from_request!(Person, :id)
    if selected
      survey.add_participant! person
    else
      remove_participant(person)
    end

    if xhr?
      render template: "participant/_person", layout: false, locals: {person: person}
    else
      redirect_to_participation_index
    end
  end

  def update_exclude_after
    survey.exclude_participants_after = params[:exclude_after].present? ? DateMethods.extract_date_from_param(params[:exclude_after], survey.exclude_participants_after) : nil
    survey.save!

    redirect_to_participation_index
  end

  def select_all
    if survey_demographics_without_self_report.present?
      update_all(all_participant_ids | selectable_user_ids_for_view, :select)
    else
      update_all(all_selectable_people_ids, :select)
    end

    redirect_to_participation_index
  end

  def deselect_all
    if survey_demographics_without_self_report.present?
      update_all(all_participant_ids - pending_selectable_user_ids_for_view, :deselect)
    else
      update_all(invited_ids, :deselect)
    end

    redirect_to_participation_index
  end

  def invited_participants_count
    render json: {invitedParticipantsCount: all_participant_counts[:pending]}
  end

  protected

  def remove_participant(person)
    return if survey.has_invited_participant?(person) && !survey.continuous?

    survey.remove_participant!(person)
  end

  def selected
    params[:selected] == "on"
  end

  def update_all(participant_ids, intent = :select)
    if survey.enabled?(Flags::NEW_PARTICIPATION_LIST_VIEW)
      Jobs::SurveyLaunch::ParticipantsSelectJob.create!(survey: survey, account: account, intent: intent, participant_ids: participant_ids.to_a)
    else
      survey.participant_ids = participant_ids.to_a
    end
  end

  def selectable_user_ids_for_view
    all_selectable_people_ids & user_ids_for_select_option(view_by_so)
  end

  def pending_selectable_user_ids_for_view
    pending_ids & selectable_user_ids_for_view
  end

  def user_ids_for_select_option(select_option)
    return [] unless select_option

    so_user_ids = user_ids_for_select_option_for_survey(survey, select_option)
    so_user_ids += user_ids_for_select_option_from_employees(select_option, invited_ids) if master.present?
    so_user_ids
  end

  def user_ids_for_select_option_for_survey(survey, select_option, exclude_user_ids = [])
    exclude_user_aggregate_ids = Person.where(id: exclude_user_ids).only(:aggregate_id).map(&:aggregate_id)

    Participants::Queries::Responses::ForSelectOptionInSurvey.new.call(
      survey_aggregate_id: survey.aggregate_id,
      question_aggregate_id: survey_demographics_without_self_report.question.aggregate_id,
      select_option_id: select_option&.id,
      exclude_user_aggregate_ids: exclude_user_aggregate_ids
    ).pluck(:user_mongo_id).map { |id| BSON::ObjectId(id) }.to_set
  end

  def user_ids_for_select_option_from_employees(select_option, exclude_user_ids = [])
    query = account.people.active.only(:id)

    if survey_demographics_without_self_report.question[:core_data_type]
      Participants::CoreDemographicsUserIds.fetch(
        account: account,
        select_option: select_option,
        exclude_user_ids: exclude_user_ids,
        people_query: account.people.active
      )
    else
      query = standard_demographic_query(select_option, query)
      query = query.nin(id: exclude_user_ids.to_a) if exclude_user_ids.present?
      query.pluck(:id).to_set
    end
  end

  def standard_demographic_query(select_option, query)
    if select_option.present?
      query.where("demographic_value_assignments.demographic_value_id" => select_option.demographic_value_id)
    else
      sos = survey_demographics_without_self_report.question.select_options
      sos = sos.respond_to?(:active) ? sos.active : sos
      demographic_value_ids = sos.map(&:demographic_value_id)
      query.nin("demographic_value_assignments.demographic_value_id" => demographic_value_ids)
    end
  end

  def redirect_to_participation_index
    xhr? ? head(200) : (redirect_to survey_participant_index_path(survey))
  end

  def people_in_context(paginate = true)
    people = account.people.associated.order_by(:name_sort.asc)

    people = people.active
    people = filtered_people(people)
    people = invited_and_filtered_people(people)

    term = params[:term].try(:lstrip)
    people = people.with_search_term(term) if term
    people = paginated_people(people) if paginate
    people
  end

  def paginated_people(people)
    if params[:action] == "search"
      people.paginate(page: 1, per_page: 20)
    else
      people.paginate(page: params[:page], per_page: 100)
    end
  end

  def demographics_for_grouping
    return [] unless survey.allow_grouping_of_participants?

    Demographics::Queries::SurveyDemographicsWithoutSelfReport.new.call(survey: survey).value_or([])
  end

  def construct_survey_participant_actions
    import_participants_action =
      {
        label: I18n.t("survey_admin.actions.import_participants"),
        actionType: "link",
        class: "import-participants-link",
        iconType: "participant",
        path: "/app/survey-participant-importer/#{account.aggregate_id}/survey/#{survey.aggregate_id}/select-file"
      }

    export_participants_action =
      {
        label: I18n.t("survey_admin.actions.export_participants"),
        actionType: current_user.can_export_survey_participants_with_demographics?(survey) ? "class" : "exportParticipants",
        iconType: "participant",
        class: current_user.can_export_survey_participants_with_demographics?(survey) ? "surveyParticipantsExportTrigger" : "surveyParticipantsExport",
        exportParticipantsUrl: "/surveys/#{survey.aggregate_id}/participants_exports",
        securityToken: form_authenticity_token
      }

    export_kiosk_codes_action =
      {
        label: I18n.t("survey_admin.actions.export_kiosk_codes"),
        actionType: "modal",
        iconType: "kiosk",
        modalPath: new_survey_kiosk_codes_export_path(survey)
      }

    actions = [survey.kiosk? ? export_kiosk_codes_action : export_participants_action]
    if can_import_participants?
      actions.unshift(import_participants_action)
    end
    actions
  end

  def construct_participant_subtitle_props
    Props::Participant::FilterProps.new(participant_filter: survey.participant_filter).to_hash
  end

  def filtered_people(people)
    Services::PeopleFilteredByDemographic.new(survey: survey, people: people).call
  end

  def participant_counts
    select_option = view_by_so # reassigning to a clearer variable name

    {
      participant_list: select_option,
      selected: select_option.nil? ? 0 : demographic_participant_counts(select_option.demographic_value_id)[:participating],
      selectable: select_option.nil? ? 0 : demographic_participant_counts(select_option.demographic_value_id)[:active_people],
      invited: select_option.nil? ? 0 : demographic_participant_counts(select_option.demographic_value_id)[:invited],
      pending: select_option.nil? ? 0 : demographic_participant_counts(select_option.demographic_value_id)[:participating]
    }
  end

  def demographic_participant_counts(demographic_value_id)
    @demographic_participant_counts ||= all_demographic_participant_counts.call(
      survey: survey,
      demographic_stq: survey_demographics_without_self_report,
      demographic_value_id: demographic_value_id,
      active_by_demographic_value: active_participants_by_demographic_value
    )
  end

  def invited_and_filtered_people(people)
    filtered_people_and_invited_participants_ids = all_participant_ids.to_a + people.pluck(:id)
    account.people.associated.where(:id.in => filtered_people_and_invited_participants_ids)
  end

  def survey_invite_job_id
    job = Jobs::SurveyLaunch::SurveyInviteJob.where(survey_id: survey.id).ne(status: :complete).first_by_id
    job || Jobs::SurveyLaunch::SurveyInviteJob.create!(account: account, survey: survey, type: "invite", executor: current_user, status: :hold)
  end

  def survey_demographics_without_self_report
    @survey_demographics_without_self_report ||= Demographics::Queries::SurveyDemographicsFindByIdWithoutSelfReport.new.call(survey: survey, id: params[:view_by]).value_or(nil)
  end

  def can_view_employee_data?(user_id, account_id)
    Authorization.permitted?(
      user_id: user_id,
      resource_id: account_id,
      permission: Account::Permissions::VIEW_SURVEY_EMPLOYEE_DATA
    )
  end

  def can_import_participants?
    current_user.can_administer_survey?(survey) && !survey.closed?
  end

  def can_manage_survey_participant?
    Authorization.permitted?(
      user_id: current_user.aggregate_id,
      resource_id: account&.aggregate_id,
      permission: Account::Permissions::MANAGE_SURVEY_PARTICIPANT
    ) || Authorization.permitted?(
      user_id: current_user.aggregate_id,
      resource_id: survey&.aggregate_id,
      permission: Account::Permissions::MANAGE_SURVEY_PARTICIPANT
    )
  end

  def check_survey_support_admin_permission
    return render_forbidden unless can_manage_survey_participant?
  end

  def check_survey_admin_permission
    return render_forbidden unless Authorization.permitted?(
      user_id: current_user.aggregate_id,
      resource_id: account&.aggregate_id,
      permission: Account::Permissions::ADMINISTER_SURVEY
    ) || Authorization.permitted?(
      user_id: current_user.aggregate_id,
      resource_id: survey&.aggregate_id,
      permission: Account::Permissions::ADMINISTER_SURVEY
    )
  end
end
