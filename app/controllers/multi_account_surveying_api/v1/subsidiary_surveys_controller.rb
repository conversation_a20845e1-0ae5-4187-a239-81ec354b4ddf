module MultiAccountSurveyingApi
  module V1
    class SubsidiarySurveysController < ApplicationController
      include HttpSignatureAuthentication
      include VerifySameOrigin
      include Dry::Monads[:result]

      replace_csrf_with_origin_check

      allows_access_to :anonymous_users

      before_action :http_signature_authenticated?, :validate_headers, :snake_case_params

      # This action is used to create or update a survey for a subsidiary account
      # It will create a new survey if the survey does not exist
      # It will update the existing survey with provided aggregate_id
      def upsert_survey
        result = upsert_subsidiary_survey_command.call(
          account_aggregate_id: account_aggregate_id,
          survey_specifications: survey_specifications,
          metadata: build_metadata
        )

        render_monadic(result)
      end

      def upsert_section
        # code generation logic from https://github.com/cultureamp/survey-design-ui/blob/main/apps/survey-designer/src/views/Editor/components/EditorHeader/components/AddSectionMenuItem/AddSectionMenuItem.tsx#L25
        code = "#{account.subdomain}.section.#{section_specifications["section_id"].split("-")[0]}"
        result = upsert_section_command.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: section_specifications.merge(
            "code" => code
          ),
          metadata: build_metadata
        )

        render_monadic(result)
      end

      def delete_section
        result = delete_section_command.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: params.require(:section_id),
          metadata: build_metadata
        )

        render_monadic(result)
      end

      def upsert_factor
        result = upsert_factor_command.call(
          survey_aggregate_id: survey_aggregate_id,
          factor_specifications: factor_specifications
        )

        render_monadic(result)
      end

      def upsert_question
        # code generation logic from app/client/modules/ca-survey-designer/src/SurveyDesigner/Common/Types.elm
        code = "#{account.subdomain}.question.#{question_specifications["question_id"].split("-")[0]}"
        # other_option is optional
        other_option = params.dig(:question_specifications, :other_option)&.permit(
          value_translations: [:text, :locale] # Array [{ {text: "Option", locale: "en"} }]
        ).to_h

        result = upsert_question_command.call(
          survey_aggregate_id: survey_aggregate_id,
          question_specifications: question_specifications.merge(
            "code" => code,
            "other_option" => other_option
          ),
          select_option_specifications: select_options_specifications,
          metadata: build_metadata
        )

        render_monadic(result)
      end

      def delete_survey_question
        result = delete_survey_question_command.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: params.require(:question_id),
          metadata: build_metadata
        )

        render_monadic(result)
      end

      def upsert_question_display_condition
        specs = params.require(:question_display_condition_specifications)
        question_id = specs.require(:question_id)
        answer_branching_rule = params.dig(:question_display_condition_specifications, :answer_branching_rule)&.permit(
          :id, # UUID
          :parent_question_id, # UUID
          select_option_ids: [] # Array of UUIDs
        ).to_h

        result = upsert_question_display_condition_command.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          display_condition_specifications: {
            answer_branching_rule: answer_branching_rule
          },
          metadata: build_metadata
        )

        render_monadic(result)
      end

      private

      def render_monadic(result)
        case result
        in Success({status:, data:})
          render status: status, json: data.deep_transform_keys { |key| key.to_s.camelize(:lower) }
        in Success({status:})
          render status: status, json: {}
        in Failure({status:, message:})
          render status: status, json: {error: message}
        else
          render status: :internal_server_error, json: {error: "An unexpected error occurred"}
        end
      end

      def upsert_subsidiary_survey_command
        @upsert_subsidiary_survey_command ||= SurveyDesign::MultiAccountSurveying::Commands::UpsertSubsidiarySurvey.new
      end

      def upsert_section_command
        @upsert_section_command ||= SurveyDesign::MultiAccountSurveying::Commands::UpsertSection.new
      end

      def delete_section_command
        @delete_section_command ||= SurveyDesign::MultiAccountSurveying::Commands::DeleteSection.new
      end

      def upsert_factor_command
        @upsert_factor_command ||= SurveyDesign::MultiAccountSurveying::Commands::UpsertFactor.new
      end

      def upsert_question_command
        @upsert_question_command ||= SurveyDesign::MultiAccountSurveying::Commands::UpsertQuestion.new
      end

      def delete_survey_question_command
        @delete_survey_question_command ||= SurveyDesign::MultiAccountSurveying::Commands::DeleteSurveyQuestion.new
      end

      def upsert_question_display_condition_command
        @upsert_question_display_condition_command ||= SurveyDesign::MultiAccountSurveying::Commands::UpsertQuestionDisplayCondition.new
      end

      def account_aggregate_id
        # This is the aggregate ID of the subsidiary account to create the survey for
        params.require(:account_aggregate_id)
      end

      def survey_aggregate_id
        # This is the aggregate ID of the subsidiary survey to upsert section for
        params.require(:survey_aggregate_id)
      end

      def user_aggregate_id
        request.headers["X-User-Id"]
      end

      def correlation_id
        request.headers["X-Correlation-Id"]
      end

      def survey_specifications
        # This is the survey specifications to create or update the survey with
        # OpenApi specification for this endpoint is in the file
        # doc/openapi/src/multi-account-surveying-api/resources/upsert_subsidiary_survey.yaml
        params.require(:survey_specifications).permit(
          :aggregate_id,
          :org_survey_aggregate_id,
          :org_survey_region,
          :survey_period_type,
          :type,
          name_translations: {},
          flags: {},
          configuration: {}
        ).to_h
      end

      def section_specifications
        # This is the section specifications to create or update the section with
        # OpenApi specification for this endpoint is in the file
        # doc/openapi/src/multi-account-surveying-api/resources/upsert_section.yaml
        params.require(:section_specifications).permit(
          :section_id, # UUID
          :position, # Integer
          :intended_purpose, # String
          long_description: [:text, :locale], # Array [{ {text: "", locale: "en"} }]
          name_translations: [:text, :locale], # Array [{ {text: "", locale: "en"} }]
          short_description: [:text, :locale] # Array [{ {text: "", locale: "en"} }]
        ).to_h
      end

      def question_specifications
        # This is the question specifications to create or update the question with
        # OpenApi specification for this endpoint is in the file
        # doc/openapi/src/multi-account-surveying-api/resources/upsert_question.yaml
        params.require(:question_specifications).permit(
          :question_id, # UUID
          :section_id, # UUID
          :question_type, # Domains::Enums::SURVEY_QUESTION_TYPES
          :rating_scale, # Domains::Enums::RATING_SURVEY_QUESTION_SCALES
          :intended_purpose, # Domains::Enums::SURVEY_QUESTION_INTENDED_PURPOSES
          :stq_order, # Integer
          :selection_limit, # Integer
          :is_mandatory, # Boolean
          description_translations: [:text, :locale], # Array [{text: "", locale: "en"}]
          factor_ids: [] # Array of UUIDs
        ).to_h
      end

      def select_options_specifications
        params.dig(:question_specifications, :select_options)&.map do |spec|
          spec.permit(
            :select_option_id, # UUID
            value_translations: [:text, :locale], # Array [{ {text: "Option", locale: "en"} }]
            sort_term: {} # Hash {en: "00001"}
          ).to_h
        end
      end

      def factor_specifications
        # This is the factor specifications to create or update the factor with
        # OpenApi specification for this endpoint is in the file
        # doc/openapi/src/multi-account-surveying-api/resources/upsert_factor.yaml
        params.require(:factor_specifications).permit(
          :factor_id, # UUID
          :is_index_factor, # Boolean
          :is_key_factor, # Boolean
          name_translations: [:text, :locale] # Array [{ {text: "Factor", locale: "en"} }]
        ).to_h
      end

      def validate_headers
        # This could be cross-region, so we cannot do authorization check here
        # Just checking basics eg. valid account and has user ID and correlation ID in the headers

        account = Account.where(aggregate_id: account_aggregate_id).first
        render_forbidden unless account && user_aggregate_id.present? && correlation_id.present?
      end

      # snake_case the query params and all other params
      def snake_case_params
        params.deep_transform_keys!(&:underscore)
      end

      def build_metadata
        EventFramework::Event::Metadata.new(
          user_id: current_user.aggregate_id,
          account_id: account&.aggregate_id,
          correlation_id: correlation_id
        )
      end

      def http_signature_authenticated?
        render_forbidden unless authenticated?
      end
    end
  end
end
