class SummaryInsightReportController < ReportsController
  component :report
  include MultiFormatReport
  REPORT_TYPE = ReportDataTypes::SUMMARY_INSIGHT_REPORT
  expose(:action_framework_id) { action_planning_service.action_framework_id(survey, report_access_grant) }

  expose(:report_data) do
    {
      reportType: REPORT_TYPE,
      summaryInsightReportData: summary_insight_report_data.merge(
        questionsReportPath: paths.questions(overwrite_redirection: "true"),
        takeActionReportPath: paths.new_action_framework_report,
        createActionPath: paths.create_action_without_focus(from: request.fullpath),
        actionFrameworkApiRootPath: paths.survey_action_framework_path(survey.id, action_framework_id),
        treasureData: treasure_data_props,
        summaryView: params[:summaryView],
        actionFrameworkEnabled: has_action_framework_enabled?,
        reportOwner: report_access_grant.report_owner?,
        selectFocusAreaCreateActionEnabled: survey.enabled?(Flags::SELECT_FOCUS_AREA_CREATE_ACTION)
      ),
      uiText: ui_text,
      reportTypes: report_access_grant.report.report_types
    }
  end

  def show
    render_report
  end

  private

  def reporting_rules
    Reporting::Confidentiality::StandardRules.new.call(
      survey: survey
    )
  end

  def summary_insight_report_data
    [
      render_action_framework_info,
      render_all_results,
      render_displayed_factor_name_info,
      render_displayed_factor_score_info,
      render_participation_info,
      render_comparisons_info,
      render_highest_rating_questions_info,
      render_recommender_questions_info,
      render_report_commentary
    ].map(&:data).reduce(&:merge)
  end

  def treasure_data_props
    Props::TreasureDataProps.new(
      survey_id: survey.id.to_s,
      user_aggregate_id: current_user.aggregate_id,
      comparison_key: context.comparison_key,
      report_access_grant: report_access_grant
    ).to_hash
  end

  def ui_text
    Props::SummaryInsightTranslationProps.new.summary_insight_report(survey_significant_population: survey.significant_population)
  end

  def render_action_framework_info
    Reports::SummaryInsightReport::ActionFrameworkInfo.new(survey, report_access_grant)
  end

  def render_all_results
    Reports::SummaryInsightReport::AllResults.new(all_results_report?)
  end

  def render_displayed_factor_name_info
    Reports::SummaryInsightReport::DisplayedFactorName.new(
      survey: survey,
      factors_for_reports: factors_for_reports
    )
  end

  def render_displayed_factor_score_info
    Reports::SummaryInsightReport::DisplayedFactorScore.new(
      survey: survey,
      report_data_service: report_data_service,
      comparison_key: context.comparison_key,
      factors_for_reports: factors_for_reports
    )
  end

  def render_participation_info
    Reports::SummaryInsightReport::Participation.new(report_data_service)
  end

  def render_comparisons_info
    Reports::SummaryInsightReport::Comparisons.new(
      survey: survey,
      anchor: context.anchor,
      anchored_to_top_of_hierarchy: context.anchored_to_top_of_hierarchy?,
      all_results_report: all_results_report?,
      full_reporting_line: full_reporting_line?
    )
  end

  def render_highest_rating_questions_info
    helper = Reports::SummaryInsightReport::HighestRatingQuestions.new(
      survey: survey,
      report_data_service: report_data_service,
      comparison_key: context.comparison_key,
      questions_for_reports: questions_for_reports,
      significant_population: reporting_rules.significance
    )
    add_has_actions_and_links(helper)
  end

  def render_recommender_questions_info
    helper = Reports::SummaryInsightReport::RecommendedQuestions.new(
      survey: survey,
      report_data_service: report_data_service,
      focus_engine_report_data_service: focus_engine_report_data_service,
      questions_for_reports: questions_for_reports,
      all_results_report: all_results_report?,
      comparison_key: context.comparison_key
    )
    add_has_actions_and_links(helper)
  end

  def render_report_commentary
    Reports::SummaryInsightReport::DisplayedReportCommentary.new(
      report_id: report_id,
      preferred_locale: locale,
      default_locale: survey.default_locale
    )
  end

  def add_has_actions_and_links(helper)
    data = helper.data
    data.each do |_key, questions|
      questions.each do |hash|
        hash[:hasActions] = question_ids_with_actions.include?(hash[:id])
        hash[:_links] = {
          viewAction: paths.new_action_framework_report,
          createAction: survey_report_create_action_path(survey.id, report_id, hash[:id])
        }
      end
    end

    Struct.new(:data).new(data)
  end

  def question_ids_with_actions
    @_question_ids_with_actions ||= begin
      result = ActionFrameworks::ReaderService.new.actions(action_framework_id)
      return [] if result.error?

      result.data.flat_map { |action| action.focus_areas.map(&:question_id) }.uniq
    end
  end

  def has_action_framework_enabled?
    report = Report.where(id: report_id).first
    survey.action_framework_enabled?(report)
  end
end
