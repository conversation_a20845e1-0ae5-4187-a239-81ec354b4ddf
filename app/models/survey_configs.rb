# This module encapsulates all the querying of the different Flags and Configs
# applicable to a particular survey instance.
# @see app/models/flags.rb
# @see app/models/configs.rb
# @see tested in spec/models/survey_config_spec.rb
module SurveyConfigs
  # Config related
  def auth_code
    config(Configs::ANONYMOUS_AUTHORIZATION_CODE)
  end

  def auth_code=(str)
    if str.present? && str != ""
      assign_config(Configs::ANONYMOUS_AUTHORIZATION_CODE, str)
    else
      clear_config(Configs::ANONYMOUS_AUTHORIZATION_CODE)
    end
  end

  def employee_id_kiosk_code?
    config(Configs::KIOSK_CODE) == Configs::EMPLOYEE_ID_KIOSK_CODE
  end

  # @return [ Array ] a list of locales supported by this survey, in order of
  # general priority
  def supported_locales
    @supported_locales ||= (config(Configs::SUPPORTED_LOCALES) || []).map(&:to_s)
  end

  def likert_scale_color_schema
    config(Configs::CAPTURE_LIKERT_COLOR_SCHEMA)
  end

  def default_locale
    supported_locales.first
  end

  def support_email
    config(Configs::SUPPORT_EMAIL) || Appamp::Settings.email_support
  end

  def workflow
    config(Configs::LIFECYCLE_WORKFLOW)
  end

  def full_workflow?
    workflow == Configs::LIFECYCLE_WORKFLOW_FULL
  end

  def raw_data_extract_enabled?
    config(Configs::RAW_DATA_EXTRACT) == Configs::ENABLED
  end

  def coached?
    config(Configs::EFFECTIVENESS_360_COACHED) == Configs::ENABLED
  end

  def multilingual?
    config(Configs::SUPPORTED_LOCALES).length > 1
  end

  def recommended_questions_experiment_enabled?
    enabled?(Flags::RECOMMENDED_QUESTIONS_EXPERIMENT_ENABLED)
  end

  def show_question_benchmark_information?
    return false unless template_type == :engagement_survey

    enabled?(Flags::SHOW_BENCHMARKABLE_QUESTIONS)
  end

  def effectiveness_360_employee_driven?
    three_sixty? && config(Configs::EFFECTIVENESS_360_EMPLOYEE_DRIVEN) == Configs::ENABLED
  end

  def valid_lifecycle_participant?(user)
    return false unless lifecycle?
    return true unless effectiveness_360_employee_driven?

    has_participant?(user)
  end

  # When a survey has "transparent" responses, we allow controlled access to individual responses.
  # This is the typical case for onboard and exit surveys, but they can be configured otherwise.
  def has_transparent_responses?
    lifecycle? && config(Configs::SIGNIFICANCE_LIFECYCLE_POPULATION) <= 0
  end

  def significant_population
    config(lifecycle? ? Configs::SIGNIFICANCE_LIFECYCLE_POPULATION : Configs::SIGNIFICANCE_POPULATION)
  end

  def comment_significant_population
    config(lifecycle? ? Configs::SIGNIFICANCE_LIFECYCLE_POPULATION : Configs::SIGNIFICANCE_COMMENT_POPULATION)
  end

  def indirect_identification_protection_level
    config(lifecycle? ? Configs::LIFECYCLE_INDIRECT_IDENTIFICATION_PROTECTION_LEVEL : Configs::INDIRECT_IDENTIFICATION_PROTECTION_LEVEL)
  end

  # Flag related
  def allow_emails?
    if three_sixty?
      config(Configs::SEND_THREE_SIXTY_EMAILS) == Flags::ENABLED
    elsif engagement?
      send_engagement_emails?
    else
      true # disabling emails not supported for li surveys always allow emails
    end
  end

  def automatic_invite_trigger_enabled?
    enabled?(Flags::TRIGGER_LIFECYCLE_INVITES)
  end

  def significant_driver_analysis
    config(Configs::SIGNIFICANCE_DRIVER_ANALYSIS)
  end

  def automatic_invite_trigger=(enabled)
    flag = enabled ? Flags::ENABLED : Flags::DISABLED
    assign_flag!(Flags::TRIGGER_LIFECYCLE_INVITES, flag)
  end

  def report_sharing_enabled?
    enabled?(Flags::REPORT_SHARING)
  end

  def hierarchy_reports_enabled?
    if FeatureFlags::Queries::ValueForAccount.new.call(
      account_aggregate_id: account&.aggregate_id,
      flag_name: FeatureFlags::Flags::HIERARCHY_REPORTS_TOGGLE_CUTOVER,
      fallback_value: false
    )
      FeatureFlags::Queries::ValueForSurvey.new.call(
        survey: self,
        flag_name: FeatureFlags::Flags::HIERARCHY_REPORTS,
        fallback_value: true
      )
    else
      enabled?(Flags::HIERARCHY_REPORTS)
    end
  end

  def action_framework_flag_enabled?
    FeatureFlags::Queries::ValueForSurvey.new.call(
      survey: self,
      flag_name: FeatureFlags::Flags::MODULE_TOGGLES_ACTION_FRAMEWORK,
      fallback_value: true
    )
  end

  def action_framework_enabled?(report = nil)
    # A temporary guard clause against multi demographic reports, for customer 0 release.
    # Report sharing will be enabling action framework in the future.
    return false if report&.multi_demographic_report?
    (survey_period_type == :snapshot || survey_period_type == :adhoc) && action_framework_flag_enabled? && (report.blank? || report.af_enabled)
  end

  def report_owner_enabled?
    enabled?(Flags::ACTION_DRIVER)
  end

  def action_framework_feedback_request_enabled?
    action_framework_enabled? && enabled?(Flags::ACTION_FRAMEWORK_FEEDBACK_REQUEST)
  end

  def action_framework_new_select_focus_enabled?
    action_framework_enabled? && enabled?(Flags::ACTION_FRAMEWORK_NEW_SELECT_FOCUS)
  end

  def action_framework_call_to_action_insight_report_enabled?(report)
    action_framework_enabled?(report) &&
      enabled?(Flags::ACTION_FRAMEWORK_CALL_TO_ACTION_INSIGHT_REPORT)
  end

  def new_action_framework_effectiveness_enabled?
    three_sixty? && enabled?(Flags::NEW_ACTION_FRAMEWORK_EFFECTIVENESS)
  end

  def new_action_framework_effectiveness_phase_2_enabled?
    new_action_framework_effectiveness_enabled? && enabled?(Flags::NEW_ACTION_FRAMEWORK_EFFECTIVENESS_PHASE_2)
  end

  def action_framework_permission_enabled?
    action_framework_flag_enabled? && enabled?(Flags::ACTION_FRAMEWORK_PERMISSION)
  end

  def action_dashboard_report_enabled?
    (survey_period_type == :snapshot || survey_period_type == :adhoc) &&
      enabled?(Flags::ACTION_DASHBOARD_REPORT) &&
      (
        enabled?(Flags::ACTION_DASHBOARD_FOR_ALL_TIME) ||
        closed_after_we_started_recording_report_view_timestamp?
      )
  end

  def focus_agent_enabled?
    (survey_period_type == :snapshot || survey_period_type == :adhoc) && enabled?(Flags::FOCUS_AGENT)
  end

  def allow_comments_on_rating_questions?
    enabled?(Flags::QUESTION_LEVEL_COMMENTS)
  end

  def free_text_questions_enabled?
    enabled?(Flags::FREE_TEXT_QUESTIONS_REPORT)
  end

  def summary_insight_report_enabled?
    survey_period_type == :snapshot &&
      report_sharing_enabled? &&
      enabled?(Flags::SUMMARY_INSIGHT_REPORT) &&
      has_index_factor? &&
      has_index_factor_questions?
  end

  def overall_label_config
    config(Configs::OVERALL_LABEL)
  end

  def legacy_comment_tags_enabled?
    enabled?(Flags::LEGACY_COMMENT_TAGS)
  end

  def powerpoint_export_enabled?
    enabled?(Flags::POWERPOINT_EXPORT) &&
      powerpoint_template.present? &&
      powerpoint_template != :none
  end

  def experiment_understand_localised_powerpoint_enabled?
    enabled?(Flags::EXPERIMENT_UNDERSTAND_LOCALISED_POWERPOINT) && powerpoint_export_enabled?
  end

  def allow_grouping_of_participants?
    survey_grouping = explicit_config(Configs::ALLOW_GROUP_PARTICIPANTS)
    return survey_grouping == Configs::ENABLED if survey_grouping.present? # Explicitly defined on the survey

    account_grouping = account&.explicit_config(Configs::ALLOW_GROUP_PARTICIPANTS)
    return account_grouping == Configs::ENABLED if account_grouping.present? # Explicitly defined on the account

    people_count = account.present? ? account.people.associated.count : 0
    return false if people_count > Configs::ALLOW_GROUP_PARTICIPANT_LIMIT # People count is above the limit

    config(Configs::ALLOW_GROUP_PARTICIPANTS) == Configs::ENABLED # Fallback to default config
  end

  def custom_report_enabled?
    enabled?(Flags::CUSTOM_REPORT)
  end

  def send_engagement_emails?
    config(Configs::SEND_ENGAGEMENT_EMAILS) == Configs::ENABLED
  end

  def send_engagement_ms_teams_messages?
    config(Configs::SEND_ENGAGEMENT_MS_TEAMS_MESSAGES) == Configs::ENABLED
  end

  def text_analytics_report_enabled?
    enabled?(Flags::TEXT_ANALYTICS_REPORT)
  end

  def comment_language_detection_enabled?
    text_analytics_report_enabled? &&
      enabled?(Flags::EXPERIMENT_UNDERSTAND_COMMENT_TRANSLATIONS)
  end

  def comment_translations_enabled?
    comment_language_detection_enabled? &&
      !!account&.comment_translations_disclaimer_accepted?
  end

  def max_report_demographic_spread_size
    config(Configs::MAXIMUM_REPORT_DEMOGRAPHIC_SPREAD_SIZE)
  end

  def show_reviewer_names?
    config(Configs::SHOW_THREE_SIXTY_REVIEWER_NAMES) == Configs::ENABLED
  end

  def experiment_understand_branched_question_reporting_enabled?
    enabled?(Flags::EXPERIMENT_UNDERSTAND_BRANCHED_QUESTION_REPORTING)
  end

  def exclude_no_comment_classifications?
    enabled?(Flags::COMMENTS_EXCLUDE_NO_COMMENT_CLASSIFICATIONS)
  end

  def adhoc_comments_chronological_order_enabled?
    enabled?(Flags::EXPERIMENT_UNDERSTAND_ADHOC_COMMENTS_CHRONOLOGICAL_ORDER)
  end

  def self_service_comparison_loading_enabled?
    enabled?(Flags::EXPERIMENT_UNDERSTAND_SELF_SERVICE_COMPARISON_LOADING)
  end

  def experiment_trend_enabled?
    enabled?(Flags::EXPERIMENT_A_AND_U_DEV_ENGAGEMENT_TREND)
  end

  def questions_to_exclude_from_driver_analysis
    config(Configs::QUESTIONS_TO_EXCLUDE_FROM_DRIVER_ANALYSIS) || []
  end

  def editable_slack_communications_enabled?
    enabled?(Flags::EDITABLE_SLACK_COMMUNICATIONS)
  end

  def improved_communications_enabled?
    enabled?(Flags::IMPROVED_COMMS_CONFIGURATION)
  end

  def ms_teams_for_lifecycle_enabled?
    enabled?(Flags::MS_TEAMS_FOR_LIFECYCLE)
  end

  def ms_teams_for_effectiveness_enabled?
    enabled?(Flags::MS_TEAMS_FOR_EFFECTIVENESS)
  end

  def comment_reply_enabled?
    config(Configs::COMMENT_REPLIES) == Configs::ENABLED
  end

  def improved_comms_configuration_enabled?
    enabled?(Flags::IMPROVED_COMMS_CONFIGURATION)
  end

  def improved_hierarchical_reports_filtering_enabled?
    enabled?(Flags::IMPROVED_HIERARCHICAL_REPORTS_FILTERING)
  end

  def results_notification_enabled?
    enabled?(Flags::CREATE_SHARE_RESULTS_NOTIFICATION_RECORD)
  end

  def automatic_comparisons_v2_enabled?
    enabled?(Flags::AUTOMATIC_COMPARISONS_V2)
  end

  def use_qms_as_source_of_truth_enabled?
    enabled?(Flags::USE_QMS_AS_SOURCE_OF_TRUTH)
  end

  def comparisons_table_ux_uplift_enabled?
    enabled?(Flags::COMPARISONS_TABLE_UX_UPLIFT)
  end

  def survey_admin_overview_page_enabled?
    enabled?(Flags::SURVEY_ADMIN_OVERVIEW_PAGE)
  end

  def remove_unreportable_demographic_values_from_filter_enabled?
    enabled?(Flags::REMOVE_UNREPORTABLE_DEMOGRAPHIC_VALUES_FROM_FILTER)
  end

  def create_sent_comms_translations?
    !three_sixty? && enabled?(Flags::CREATE_SENT_COMMS_TRANSLATIONS)
  end

  def update_slack_settings_location_enabled?
    enabled?(Flags::UPDATE_SLACK_SETTINGS_LOCATION)
  end

  def show_results_to_action_guide_text_ms_teams?
    config(Configs::SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS) == Configs::ENABLED
  end

  def new_reporting_factors_page_enabled?
    enabled?(Flags::NEW_REPORTING_FACTORS_PAGE)
  end

  private

  def closed_after_we_started_recording_report_view_timestamp?
    closed_at.nil? || closed_at > DateTime.new(2017, 10, 4)
  end
end
