module ReportAccessGrantable
  include ReportSharing::Queries::MultiDemographicFeatureFlag

  delegate :available_select_options,
    :base_demographic_stq,
    :all_results_report?,
    :filter_demographic_stqs,
    :comment_filter_stq_id,
    :insight_report_type,
    :select_option_for_id,
    :stq_by_select_option_id,
    :stq_id_by_select_option_id,
    to: :report

  def select_option
    return @select_option if defined?(@select_option)
    @select_option ||= select_option_for_id(select_option_id) if select_option_id.present?
  end

  def select_options
    @select_options ||= select_option_ids.map(&method(:select_option_for_id)).compact
    # Ensure all select options are valid
    return [] if @select_options.size != select_option_ids.size

    @select_options
  end

  def scope
    stq_select_option = select_options.each_with_object({}) { |so, h| h[stq_by_select_option_id(so.id)] = so }
    stq_select_option.size == select_options.size ? stq_select_option : {}
  end

  def adjust_report_filters(report_filters)
    return [] if filter_demographic_stqs.blank?

    report_filters = report_filters.select { |filter|
      !filter.anchor? && filter_demographic_stqs.include?(filter.survey_to_question)
    }

    [*create_anchor_filter].concat(report_filters)
  end

  def adjust_comment_filters(report_filters)
    comment_filters = report_filters.select { |filter|
      stq = filter.survey_to_question
      is_valid_stq = !filter.anchor? && stq.present?
      if allowed_filters.flexible_comments_filters_enabled?
        is_valid_stq && (allowed_filters.flexible_comment_filter_stqs.include?(stq) || stq.id == comment_filter_stq_id)
      else
        is_valid_stq && stq.id == comment_filter_stq_id
      end
    }

    adjusted_report_filters = [*create_anchor_filter]
    adjusted_report_filters << comment_filters if comment_filters.present?
    adjusted_report_filters.flatten
  end

  def comment_filter_stq
    report.survey.survey_to_questions.where(id: comment_filter_stq_id).first
  end

  def allowed_filter_demographic_stqs(stqs)
    stqs.select do |stq|
      report.filter_demographic_stq_ids.include?(stq.id)
    end
  end

  def comment_filters_valid?(report_filters)
    return false unless can_view_comments?

    adjust_comment_filters(report_filters).to_set == report_filters.to_set
  end

  def can_view?(report_type, format = :html)
    case report_type
    when FiltersController::REPORT_TYPE
      true
    when ReportMetadataController::REPORT_TYPE
      true
    when ParticipationReportController::REPORT_TYPE
      can_view_participation?
    when SummaryInsightReportController::REPORT_TYPE
      can_view_summary? || (can_view_standard? &&
        (insight_report_type == :insight_report || insight_report_type == :lifecycle_insight_report || export?(format)))
    when InsightReportController::REPORT_TYPE,
         Trend::ApiController::REPORT_TYPE
      can_view_standard? &&
        (insight_report_type == :insight_report || export?(format))
    when LifecycleInsightReportController::REPORT_TYPE,
         LifecycleSurveyTrend::ScoresController::REPORT_TYPE
      can_view_standard? && insight_report_type == :lifecycle_insight_report
    when QuestionInsightReportController::REPORT_TYPE,
         SelectQuestionReportController::REPORT_TYPE,
         FactorInsightReportController::REPORT_TYPE,
         FreeTextQuestionsReportController::REPORT_TYPE
      can_view_standard?
    when ActionFrameworks::NewActionFrameworkReportController::REPORT_TYPE
      can_view_action_framework?
    when NewComparisonReportController::REPORT_TYPE,
         DemographicReportController::REPORT_TYPE
      can_view_advanced?
    when TextAnalytics::ReportsController::REPORT_TYPE,
         TextAnalytics::ExportsController::REPORT_TYPE
      can_view_comments?
    when ActionFrameworks::ActionDashboardController::REPORT_TYPE
      can_view_action_dashboard?
    else
      false
    end
  end

  def can_view_comments?
    report.show_comments? && can_view_standard?
  end

  def can_view_action_framework?
    report.action_framework_enabled? && can_view_standard?
  end

  def can_view_action_dashboard?
    has_report_owner_access? || has_hr_business_partner_access?
  end

  def has_report_owner_access?
    report.survey.enabled?(Flags::ACTION_DASHBOARD_SHARING) && report_owner? && !report.multi_demographic_report?
  end

  def has_hr_business_partner_access?
    # HR BP access grant
    (survey.action_dashboard_report_enabled? && !survey.archived && report_consumer.hr_business_partner?)
  end

  def create_anchor_filter(is_anchor = true)
    user_id = respond_to?(:report_consumer) && report_consumer&.aggregate_id || nil
    if has_access_to_view_multi_demographic_reports?(
      user_id: user_id,
      account_id: survey&.account&.aggregate_id,
      survey_id: survey.aggregate_id
    )
      scope.map { |stq, select_option|
        ReportFilter.new(stq, select_option, is_anchor)
      }.then do |filters|
        return filters unless filters.empty?
        [AllResultsReportFilter.new(is_anchor)]
      end
    else
      [create_anchor_filter_legacy(is_anchor)]
    end
  end

  private

  def export?(format)
    # Array.include? method compares symbol of :xls to a Mime::Type class
    # Whereas == approach compares symbol :xls to a Mime::Type shortcut (which provides :xls)
    # See: CSE-3353 for more info
    format == :xls || format == :csv || format == :xlsx
  end

  def report_and_consumer_accounts_match
    return unless report.try(:survey).try(:account_id) != report_consumer.try(:account_id)

    errors.add(:base, "must be in the same account as the report")
  end

  def can_view_participation?
    !report.survey.lifecycle?
  end

  # report view no longer can be anything other than :advanced or :standard.
  # Historically at the time of  shared reports creation, user could opt to make it advanced, standard, summary or participation.
  # Latter two options are no longer available. But code references are not removed, perhaps in order to keep the old reports working
  def can_view_summary?
    report.report_view == :summary
  end

  def can_view_standard?
    report.report_view == :standard || can_view_advanced?
  end

  def can_view_advanced?
    report.report_view == :advanced
  end

  def create_anchor_filter_legacy(is_anchor = true)
    if base_demographic_stq
      ReportFilter.new(base_demographic_stq, select_option, is_anchor)
    else
      AllResultsReportFilter.new(is_anchor)
    end
  end

  def allowed_filters
    Comments::Filters.new(report.survey, report)
  end
end
