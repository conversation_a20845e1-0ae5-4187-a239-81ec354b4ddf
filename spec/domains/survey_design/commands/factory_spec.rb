require "rails_helper"

RSpec.describe SurveyDesign::Commands::Factory do
  let(:survey_id) { SecureRandom.uuid }

  describe "#handler_for" do
    it "every handler is able to handle their commands" do
      factory = described_class.new
      described_class::EVENT_FRAMEWORK_HANDLER_MAP.each do |command_class, handler_class|
        handler = factory.handler_for(survey_id: survey_id, command_class: command_class)
        expect(handler).to be_a(handler_class)
        expect(handler.send(:command_class)).to eq(command_class)
      end
    end

    it "returns an instance of the handler for the given command" do
      factory = described_class.new
      command_class = Domains::SurveyDesign::SurveyQuestion::ChangeTextCommand

      handler = factory.handler_for(survey_id: survey_id, command_class: command_class)
      expect(handler).to be_a(Domains::SurveyDesign::SurveyQuestion::ChangeTextCommandHandler)
      expect(handler.send(:command_class)).to eq(Domains::SurveyDesign::SurveyQuestion::ChangeTextCommand)
    end

    it "raises an error if no handler is found for the command" do
      factory = described_class.new
      command_class = class_double("UnknownCommand")

      expect { factory.handler_for(survey_id: survey_id, command_class: command_class) }.to raise_error(SurveyDesign::Commands::Factory::HandlerNotFoundError)
    end
  end

  describe "#event_framework_handler_class_for" do
    it "every handler is able to handle their commands" do
      factory = described_class.new
      described_class::EVENT_FRAMEWORK_HANDLER_MAP.each do |command_class, handler_class|
        registered_handler_class = factory.send(:event_framework_handler_class_for, command_class: command_class)
        expect(handler_class).to eq(registered_handler_class)

        expect(handler_class.send(:command_class)).to eq(command_class)
        expect(handler_class.new).to respond_to(:call)
      end
    end

    it "returns the correct handler class for a given command" do
      factory = described_class.new
      command_class = Domains::SurveyDesign::SurveyQuestion::ChangeTextCommand

      handler_class = factory.send(:event_framework_handler_class_for, command_class: command_class)
      expect(handler_class).to eq(Domains::SurveyDesign::SurveyQuestion::ChangeTextCommandHandler)
      expect(handler_class.send(:command_class)).to eq(command_class)
    end

    it "raises an error if no handler is found for the command" do
      factory = described_class.new
      command_class = class_double("UnknownCommand")

      expect { factory.send(:event_framework_handler_class_for, command_class: command_class) }.to raise_error(SurveyDesign::Commands::Factory::HandlerNotFoundError)
    end
  end
end
