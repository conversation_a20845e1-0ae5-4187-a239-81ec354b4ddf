require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Commands::DeleteSection do
  let(:account) { FactoryBot.create(:account, :with_admin) }
  let(:section_repo) { Theme.extras(read: {mode: :secondary}) }
  let(:survey_repo) { Survey.extras(read: {mode: :secondary}) }
  let(:delete_section_command) { Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionCommand }
  let(:delete_section_command_handler) { Domains::SurveyDesign::SurveyCaptureLayout::RemoveSectionCommandHandler.new }
  let(:create_section_command) { SurveyDesign::MultiAccountSurveying::Commands::CreateSection.new(logger: logger) }
  let(:enps_protection_checker) { ENPSProtection }
  let(:account_aggregate_id) { account.aggregate_id }
  let(:survey_aggregate_id) { "ba619b05-d8ea-4a65-9d79-3094caefa2cf" }
  let(:section_id) { "********-1234-5678-1234-************" }
  let(:section_specifications) do
    {
      "section_id" => section_id,
      "position" => "1",
      "intended_purpose" => "standard",
      "name_translations" => [
        {"text" => "Test Name", "locale" => "en"},
        {"text" => "Test Name FR", "locale" => "fr"}
      ],
      "short_description" => [
        {"text" => "Short Description", "locale" => "en"},
        {"text" => "Short Description FR", "locale" => "fr"}
      ],
      "long_description" => [
        {"text" => "Long Description", "locale" => "en"},
        {"text" => "Long Description FR", "locale" => "fr"}
      ],
      "code" => "#{account.subdomain}.section.#{section_id.split("-")[0]}"
    }
  end

  let(:metadata) do
    EventFramework::Event::Metadata.new(
      user_id: "e6419ccd-0bd6-4e14-bbd0-53ea8649fa78",
      account_id: account_aggregate_id,
      correlation_id: "6cdb1a76-5a5c-4cd8-8ce2-94da6cf97602"
    )
  end

  let(:logger) { double(:logger) }
  let(:command_handler_factory) { instance_double(SurveyDesign::Commands::Factory) }

  subject { described_class.new(delete_section_command: delete_section_command, enps_protection_checker: enps_protection_checker, logger: logger, command_handler_factory: command_handler_factory) }

  before do
    allow(logger).to receive(:log)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: delete_section_command).and_return(delete_section_command_handler)

    survey_template = FactoryBot.create(:survey_template, :custom_survey)
    # Create an event sourced survey
    SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
      template: survey_template,
      user: account.administrators.first,
      account: account,
      aggregate_id_override: survey_aggregate_id,
      authorization: nil
    )

    # Create a section to delete
    create_section_command.call(
      survey_aggregate_id: survey_aggregate_id,
      section_specifications: section_specifications,
      metadata: metadata
    )
  end

  describe "#call" do
    context "when the section exists" do
      it "deletes the section successfully and returns status :accepted" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_success
        expect(result.value!).to eq({status: :accepted})

        section = section_repo.where(section_id: section_id).first
        expect(section.status).to eq(:deleted)
      end
    end

    context "when the section does not exist" do
      it "returns status :no_content" do
        section_id = SecureRandom.uuid
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_success
        expect(result.value!).to include(status: :no_content)

        section = section_repo.where(section_id: section_id).first
        expect(section).to be_nil
      end
    end

    context "when the survey does not exist" do
      it "returns a failure" do
        survey_aggregate_id = SecureRandom.uuid

        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: include("Survey not found"),
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when the survey is not event sourced" do
      it "returns a failure" do
        survey = FactoryBot.create(:survey)

        result = subject.call(
          survey_aggregate_id: survey.aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request, message: "Survey is not event sourced")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: include("Survey is not event sourced"),
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey.aggregate_id
        )
      end
    end

    context "when invalid parameters are provided" do
      let(:delete_section_command) { double(:delete_section_command) }

      before do
        allow(delete_section_command).to receive(:validate).and_return(
          OpenStruct.new(
            failure?: true,
            errors: {section_id: ["Invalid section_id"]}
          )
        )
      end

      it "returns a failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: include("Invalid section_id"))
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: include("Invalid section_id"),
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when handler returns failure" do
      let(:delete_section_command_handler) { double(:delete_section_command_handler) }

      before do
        allow(delete_section_command_handler).to receive(:call).and_return("failure")
      end

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Failed deleting section")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Failed deleting section",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when an unexpected error occurs" do
      before do
        allow(delete_section_command_handler).to receive(:call).and_raise(StandardError, "Unexpected error")
      end

      it "returns failure with internal_server_error status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :internal_server_error)
        expect(result.failure).to include(message: "Unexpected error")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Unexpected error",
          module: described_class.name,
          status: :internal_server_error,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when the section has active questions" do
      let(:stq_repo) { double(:stq_repo) }
      subject { described_class.new(stq_repo: stq_repo, logger: logger) }

      before do
        allow(stq_repo).to receive(:call)
          .with(survey_id: survey_aggregate_id, section_id: section_id)
          .and_return([double(:stq)])
      end

      it "returns a failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Section has questions")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Section has questions",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when the section has deleted questions" do
      let(:stq_repo) { double(:stq_repo) }
      subject { described_class.new(stq_repo: stq_repo, logger: logger) }

      before do
        allow(stq_repo).to receive(:call)
          .with(survey_id: survey_aggregate_id, section_id: section_id)
          .and_return([])
      end

      it "deletes the section successfully and returns status :accepted" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_success
        expect(result.value!).to eq({status: :accepted})

        section = section_repo.where(section_id: section_id).first
        expect(section.status).to eq(:deleted)
      end
    end

    context "when the section is ENPS protected" do
      let(:enps_protection_checker) { double(:enps_protection_checker) }

      before do
        allow(enps_protection_checker).to receive(:enps_11_section?).with(section_id).and_return(true)
      end

      it "returns a failure" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to eq({status: :bad_request, message: "Blocked attempt to delete ENPS 11 section"})
        expect(logger).to have_received(:log).with(
          app: "murmur",
          module: "SurveyDesign::MultiAccountSurveying::Commands::DeleteSection",
          status: :bad_request,
          message: "Blocked attempt to delete ENPS 11 section",
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end
  end
end
