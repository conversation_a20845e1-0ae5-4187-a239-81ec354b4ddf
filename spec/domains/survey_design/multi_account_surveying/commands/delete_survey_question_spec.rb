require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Commands::DeleteSurveyQuestion do
  let(:account) { FactoryBot.create(:account, :with_admin) }
  let(:question_repo) { Question.extras(read: {mode: :secondary}) }
  let(:survey_repo) { Survey.extras(read: {mode: :secondary}) }
  let(:delete_question_command) { Domains::SurveyDesign::SurveyQuestion::DeleteCommand }
  let(:delete_question_command_handler) { Domains::SurveyDesign::SurveyQuestion::DeleteCommandHandler.new }
  let(:command_handler_factory) { instance_double(SurveyDesign::Commands::Factory) }
  let(:create_section_command) { SurveyDesign::MultiAccountSurveying::Commands::CreateSection.new(logger: logger) }
  # TODO replace with MAS add question command when ERV-2161 is merged
  let(:add_question_command) { Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand }
  let(:add_question_command_handler) { Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommandHandler.new }
  let(:account_aggregate_id) { account.aggregate_id }
  let(:survey_aggregate_id) { "ba619b05-d8ea-4a65-9d79-3094caefa2cf" }
  let(:question_id) { SecureRandom.uuid }
  let(:section_id) { SecureRandom.uuid }
  let(:section_specifications) do
    {
      "section_id" => section_id,
      "position" => "1",
      "intended_purpose" => "standard",
      "name_translations" => [
        {"text" => "Test Name", "locale" => "en"},
        {"text" => "Test Name FR", "locale" => "fr"}
      ],
      "short_description" => [
        {"text" => "Short Description", "locale" => "en"},
        {"text" => "Short Description FR", "locale" => "fr"}
      ],
      "long_description" => [
        {"text" => "Long Description", "locale" => "en"},
        {"text" => "Long Description FR", "locale" => "fr"}
      ],
      "code" => "#{account.subdomain}.section.#{section_id.split("-")[0]}"
    }
  end

  let(:metadata) do
    EventFramework::Event::Metadata.new(
      user_id: "e6419ccd-0bd6-4e14-bbd0-53ea8649fa78",
      account_id: account_aggregate_id,
      correlation_id: SecureRandom.uuid
    )
  end

  let(:logger) { double(:logger) }

  let(:event_source) { Domains::SurveyDesign.container.resolve("event_store.source") }

  subject { described_class.new(delete_question_command: delete_question_command, enps_protection_checker: ENPSProtection, logger: logger, command_handler_factory: command_handler_factory) }

  before do
    allow(logger).to receive(:log)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: delete_question_command).and_return(delete_question_command_handler)

    survey_template = FactoryBot.create(:survey_template, :custom_survey)
    # Create an event sourced survey
    SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
      template: survey_template,
      user: account.administrators.first,
      account: account,
      aggregate_id_override: survey_aggregate_id,
      authorization: nil
    )

    # Create a section to delete
    create_section_command.call(
      survey_aggregate_id: survey_aggregate_id,
      section_specifications: section_specifications,
      metadata: metadata
    )

    event_source.get_after(0).each do |event|
      Domains::SurveyDesign::Projectors::SurveyCommandProjector.new.handle_event(event)
    end

    # Create a question to delete
    # TODO replace this part with the MAS add question command (which encapsulates the command+handler) after ERV-2161 is merged
    add_question_params = {
      aggregate_id: question_id,
      survey_id: survey_aggregate_id,
      code: SecureRandom.uuid,
      question_type: "rating",
      rating_scale: "agreement",
      section_id: section_id,
      text: [{text: "Something", locale: "en"}],
      positioned_after_question_id: nil,
      cloned_from_curated_question_reference_code: nil,
      cloned_from_survey_question_id: nil,
      added_at: Time.now.utc,
      intended_purpose: Domains::Enums::SurveyQuestionIntendedPurposes::FEEDBACK
    }

    command_attributes = add_question_params.to_h
    command_attributes = command_attributes.deep_symbolize_keys
    command = add_question_command.new(
      command_attributes
    )
    add_question_command_handler.call(command: command, metadata: metadata)
  end

  describe "#call" do
    context "when the question exists" do
      it "deletes the question and returns status :accepted" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          metadata: metadata
        )

        expect(result).to be_success
        expect(result.value!).to eq({status: :accepted})

        question = question_repo.where(aggregate_id: question_id).first
        survey = survey_repo.where(aggregate_id: survey_aggregate_id).first
        stq = survey.stqs.where(question_id: question.id).first

        expect(stq.status).to eq(:deleted)
      end
    end

    context "when the question does not exist" do
      it "returns status :no_content" do
        question_id = SecureRandom.uuid
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          metadata: metadata
        )

        expect(result).to be_success
        expect(result.value!).to include(status: :no_content)

        question = question_repo.where(aggregate_id: question_id).first
        expect(question).to be_nil
      end
    end

    context "when the question has already been deleted" do
      before do
        subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          metadata: metadata
        )
      end

      it "returns status :no_content" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          metadata: metadata
        )

        expect(result).to be_success
        expect(result.value!).to include(status: :no_content)

        question = question_repo.where(aggregate_id: question_id).first
        survey = survey_repo.where(aggregate_id: survey_aggregate_id).first
        stq = survey.stqs.where(question_id: question.id).first

        expect(stq.status).to eq(:deleted)
      end
    end

    context "when the survey does not exist" do
      it "returns a failure" do
        survey_aggregate_id = SecureRandom.uuid

        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: include("Survey not found"),
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when the survey is not event sourced" do
      it "returns a failure" do
        survey = FactoryBot.create(:survey)

        result = subject.call(
          survey_aggregate_id: survey.aggregate_id,
          question_id: question_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request, message: "Survey is not event sourced")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: include("Survey is not event sourced"),
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey.aggregate_id
        )
      end
    end

    context "when handler returns failure" do
      let(:delete_question_command_handler) { double(:delete_question_command_handler) }

      before do
        allow(delete_question_command_handler).to receive(:call).and_return("failure")
      end

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Failed deleting question")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Failed deleting question",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when an unexpected error occurs" do
      before do
        allow(delete_question_command_handler).to receive(:call).and_raise(StandardError, "Unexpected error")
      end

      it "returns failure with internal_server_error status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :internal_server_error)
        expect(result.failure).to include(message: "Unexpected error")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Unexpected error",
          module: described_class.name,
          status: :internal_server_error,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end
  end
end
