require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Commands::CreateSelectOptions do
  let(:account) { FactoryBot.create(:account, :with_admin) }

  # Parameters for #initialize
  let(:add_select_option_command) { Domains::SurveyDesign::SurveyQuestion::AddSelectOptionCommand }
  let(:add_select_option_command_handler) { Domains::SurveyDesign::SurveyQuestion::AddSelectOptionCommandHandler.new }
  let(:command_handler_factory) { instance_double(SurveyDesign::Commands::Factory) }
  let(:logger) { double(:logger) }

  subject {
    described_class.new(
      add_select_option_command: add_select_option_command,
      command_handler_factory: command_handler_factory,
      logger: logger
    )
  }

  # Parameters for the question specifications
  let(:select_option_id_1) { "a0d2f305-7ba1-44e5-a698-d9ef9e357b55" }
  let(:select_option_id_2) { "a0d2f305-7ba1-44e5-a698-d9ef9e357b56" }
  let(:select_option_id_3) { "a0d2f305-7ba1-44e5-a698-d9ef9e357b57" }

  let(:select_option_order_1) { {"en" => "00000"} }
  let(:select_option_order_2) { {"en" => "00001"} }
  let(:select_option_order_3) { {"en" => "00002"} }
  let(:select_option_id) { "12345678-1234-5678-1234-567812345678" }
  # Parameters for #call
  let(:question_aggregate_id) { "12345678-1234-5678-1234-567812345678" }
  let(:select_options_specifications) {
    [
      {
        "select_option_id" => select_option_id_1,
        "value_translations" => [{"text" => "Test Option 1", "locale" => "en"}],
        "sort_term" => select_option_order_1
      },
      {
        "select_option_id" => select_option_id_2,
        "value_translations" => [{"text" => "Test Option 2", "locale" => "en"}],
        "sort_term" => select_option_order_2
      },
      {
        "select_option_id" => select_option_id_3,
        "value_translations" => [{"text" => "Test Option 3", "locale" => "en"}],
        "sort_term" => select_option_order_3
      }
    ]
  }
  let(:metadata) do
    EventFramework::Event::Metadata.new(
      user_id: account.administrators.first.aggregate_id,
      account_id: account.aggregate_id,
      correlation_id: SecureRandom.uuid
    )
  end

  let(:question_type) { Domains::Enums::SurveyQuestionTypes::SELECT }

  before do
    allow(logger).to receive(:log)

    # Create an event sourced survey
    survey = setup_survey
    # Stub a section and survey question aggregate to support event sourced question creation
    section = setup_section(survey)
    stub_survey_query(survey)
    stub_survey_capture_layout_aggregate(survey, section)
    # Create an event sourced question
    setup_question(survey, section)
    # make command factory return the command handler set up on this test
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey.aggregate_id, command_class: add_select_option_command).and_return(add_select_option_command_handler)
  end

  describe "#call" do
    let(:question) { Question.where(aggregate_id: question_aggregate_id).first }
    let(:expected_result) { {status: :ok, data: question.select_options.map { |so| {id: so.id.to_s, aggregate_id: so.select_option_id} }.sort_by { |so| so[:id] }} }

    context "when all parameters are valid" do
      it "creates new select options and positions correctly, then returns success" do
        result = subject.call(
          question_aggregate_id: question_aggregate_id,
          select_options_specifications: select_options_specifications,
          metadata: metadata
        )

        select_option_1 = question.select_options.where(select_option_id: select_option_id_1).first
        select_option_2 = question.select_options.where(select_option_id: select_option_id_2).first
        select_option_3 = question.select_options.where(select_option_id: select_option_id_3).first

        expect(select_option_1).to have_attributes(
          select_option_id: select_option_id_1,
          value: "Test Option 1",
          sort_term: "00000"
        )
        expect(select_option_2).to have_attributes(
          select_option_id: select_option_id_2,
          value: "Test Option 2",
          sort_term: "00001"
        )
        expect(select_option_3).to have_attributes(
          select_option_id: select_option_id_3,
          value: "Test Option 3",
          sort_term: "00002"
        )

        expect(result).to be_success
        expect(result.value!).to eq(expected_result)
      end
    end

    context "when all parameters are valid with unspecified sort terms" do
      let(:select_option_order_1) { nil }
      let(:select_option_order_2) { nil }
      let(:select_option_order_3) { nil }

      # @reference: Projectors::QuestionProjector#reorder_select_options
      it "creates new select options and positions by default order, then returns success" do
        result = subject.call(
          question_aggregate_id: question_aggregate_id,
          select_options_specifications: select_options_specifications,
          metadata: metadata
        )

        select_option_1 = question.select_options.where(select_option_id: select_option_id_1).first
        select_option_2 = question.select_options.where(select_option_id: select_option_id_2).first
        select_option_3 = question.select_options.where(select_option_id: select_option_id_3).first

        expect(select_option_1).to have_attributes(
          select_option_id: select_option_id_1,
          value: "Test Option 1",
          sort_term: "00002"
        )
        expect(select_option_2).to have_attributes(
          select_option_id: select_option_id_2,
          value: "Test Option 2",
          sort_term: "00001"
        )
        expect(select_option_3).to have_attributes(
          select_option_id: select_option_id_3,
          value: "Test Option 3",
          sort_term: "00000"
        )

        expect(result).to be_success
        expect(result.value!).to eq(expected_result)
      end
    end

    context "when question is not found" do
      it "returns failure with bad_request status" do
        non_existent_question_aggregate_id = "00000000-0000-0000-0000-000000000000"

        result = subject.call(
          question_aggregate_id: non_existent_question_aggregate_id,
          select_options_specifications: select_options_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Question not found")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Question not found",
          module: described_class.name,
          status: :bad_request,
          question_aggregate_id: non_existent_question_aggregate_id
        )
      end
    end

    context "when question is not a select question" do
      let(:question_type) { Domains::Enums::SurveyQuestionTypes::RATING }

      it "returns failure with bad_request status" do
        result = subject.call(
          question_aggregate_id: question_aggregate_id,
          select_options_specifications: select_options_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Invalid question type: 'RatingQuestion'")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Invalid question type: 'RatingQuestion'",
          module: described_class.name,
          status: :bad_request,
          question_aggregate_id: question_aggregate_id
        )
      end
    end

    context "when a required parameter is missing or invalid" do
      let(:select_option_id_1) { nil }
      let(:select_option_id_2) { nil }
      let(:select_option_id_3) { nil }

      it "returns failure with bad_request status" do
        result = subject.call(
          question_aggregate_id: question_aggregate_id,
          select_options_specifications: select_options_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "{:select_option_id=>[\"must be a UUID\"]}")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "{:select_option_id=>[\"must be a UUID\"]}",
          module: described_class.name,
          status: :bad_request,
          question_aggregate_id: question_aggregate_id
        )
      end
    end

    context "when handler returns failure" do
      let(:add_select_option_command_handler) { double(:add_select_option_command_handler) }
      before do
        allow(add_select_option_command_handler).to receive(:call).and_return("failure")
      end

      it "returns failure with bad_request status" do
        result = subject.call(
          question_aggregate_id: question_aggregate_id,
          select_options_specifications: select_options_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure[:message]).to include("Failed adding a select option")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Failed adding a select option",
          module: described_class.name,
          status: :bad_request,
          question_aggregate_id: question_aggregate_id
        )
      end
    end

    context "when an unexpected error occurs" do
      before do
        allow(add_select_option_command_handler).to receive(:call).and_raise(StandardError, "Unexpected error")
      end

      it "returns failure with internal_server_error status" do
        result = subject.call(
          question_aggregate_id: question_aggregate_id,
          select_options_specifications: select_options_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :internal_server_error)
        expect(result.failure).to include(message: "Unexpected error")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Unexpected error",
          module: described_class.name,
          status: :internal_server_error,
          question_aggregate_id: question_aggregate_id
        )
      end
    end

    private

    def setup_survey
      survey_template = FactoryBot.create(:survey_template, :custom_survey)
      SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
        template: survey_template,
        user: account.administrators.first,
        account: account,
        aggregate_id_override: "ba619b05-d8ea-4a65-9d79-3094caefa2cf",
        authorization: nil
      )
    end

    def setup_section(survey)
      FactoryBot.create(:theme, survey: survey, section_id: "61a8cc45-fdf5-426f-a8cb-f9241259c792", name: [{locale: "en", text: "Overall"}])
    end

    ##
    # Stub the SurveyQuery to return SurveyQuery::Result.
    def stub_survey_query(survey)
      allow(Domains::SurveyDesign::Queries::SurveyQuery).to receive(:call).and_return(
        Domains::SurveyDesign::Queries::SurveyQuery::Result.new(
          survey_id: survey.aggregate_id,
          account_id: account.aggregate_id,
          survey_capture_layout_id: survey.survey_capture_layout_id
        )
      )
    end

    ##
    # Stub the SurveyCaptureLayoutAggregate to return SurveyCaptureLayoutAggregate::Section.
    def stub_survey_capture_layout_aggregate(survey, section)
      aggregate = Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate.build(survey.survey_capture_layout_id)
      section_intended_purpose = Domains::Enums::SectionIntendedPurposes::STANDARD
      # Create section aggregate
      aggregate_section = Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate::Section.new(
        id: section.section_id,
        name: [{locale: "en", text: section.name}],
        short_description: [{locale: "en", text: section.name}],
        long_description: [{locale: "en", text: section.name}],
        code: section.code,
        intended_purpose: section_intended_purpose
      )

      aggregate.send(:sections_by_purpose)[section_intended_purpose] << aggregate_section
      allow(Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate).to receive(:build).with(survey.survey_capture_layout_id).and_return(aggregate)
    end

    def setup_question(survey, section)
      command = Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand.new(
        survey_id: survey.aggregate_id,
        aggregate_id: question_aggregate_id,
        text: [{text: "Test Text", locale: "en"}],
        code: "#{account.subdomain}.question.#{question_aggregate_id.split("-")[0]}",
        positioned_after_question_id: nil,
        section_id: section.section_id,
        intended_purpose: Domains::Enums::SurveyQuestionIntendedPurposes::FEEDBACK,
        question_type: question_type,
        rating_scale: Domains::Enums::RatingSurveyQuestionScales::AGREEMENT,
        selection_limit: 1,
        cloned_from_curated_question_reference_code: nil,
        cloned_from_survey_question_id: nil,
        added_at: Time.now.utc
      )
      Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommandHandler.new.call(
        command: command,
        metadata: metadata
      )
    end
  end
end
