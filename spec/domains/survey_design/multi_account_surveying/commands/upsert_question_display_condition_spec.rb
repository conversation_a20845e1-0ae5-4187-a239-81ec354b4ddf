require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Commands::UpsertQuestionDisplayCondition do
  let(:add_answer_condition_to_question_command) { Domains::SurveyDesign::DisplayCondition::AddAnswerConditionToQuestionCommand }
  let(:add_answer_condition_to_question_command_handler) { Domains::SurveyDesign::DisplayCondition::AddAnswerConditionToQuestionCommandHandler.new }
  let(:choose_parent_question_command) { Domains::SurveyDesign::DisplayCondition::ChooseParentQuestionCommand }
  let(:choose_parent_question_command_handler) { Domains::SurveyDesign::DisplayCondition::ChooseParentQuestionCommandHandler.new }
  let(:add_select_option_from_parent_question_command) { Domains::SurveyDesign::DisplayCondition::AddSelectOptionFromParentQuestionCommand }
  let(:add_select_option_from_parent_question_command_handler) { Domains::SurveyDesign::DisplayCondition::AddSelectOptionFromParentQuestionCommandHandler.new }
  let(:command_handler_factory) { instance_double(SurveyDesign::Commands::Factory) }
  let(:logger) { double(:logger) }

  let(:account) { FactoryBot.create(:account, :with_admin) }
  let(:survey) { FactoryBot.create(:survey, account: account) }
  let(:survey_aggregate_id) { survey.aggregate_id }
  let(:metadata) do
    EventFramework::Event::Metadata.new(
      user_id: SecureRandom.uuid,
      account_id: SecureRandom.uuid,
      correlation_id: SecureRandom.uuid
    )
  end
  let(:parent_question) { FactoryBot.create(:single_select_question, survey_aggregate_id: survey_aggregate_id, select_options: %w[abc def xyz]) }
  let(:select_options) { parent_question.select_options }
  let(:question) { FactoryBot.create(:free_text_question, survey_aggregate_id: survey_aggregate_id) }
  let(:display_condition_id) { SecureRandom.uuid }
  let(:display_condition_specifications) do
    {
      answer_branching_rule: {
        id: display_condition_id,
        parent_question_id: parent_question.aggregate_id,
        select_option_ids: select_options.map(&:select_option_id).take(2) # Taking first two options for the test
      }
    }
  end

  subject do
    described_class.new(
      add_answer_condition_to_question_command: add_answer_condition_to_question_command,
      choose_parent_question_command: choose_parent_question_command,
      add_select_option_from_parent_question_command: add_select_option_from_parent_question_command,
      command_handler_factory: command_handler_factory,
      logger: logger
    )
  end

  before do
    allow(logger).to receive(:log)

    # Mocking the command handler to return success
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: add_answer_condition_to_question_command).and_return(add_answer_condition_to_question_command_handler)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: choose_parent_question_command).and_return(choose_parent_question_command_handler)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: add_select_option_from_parent_question_command).and_return(add_select_option_from_parent_question_command_handler)

    # Mocking projection database inserts
    Domains::SurveyDesign.database(:projections)[:survey_command_projection].insert(
      account_id: account.aggregate_id,
      survey_id: survey_aggregate_id,
      survey_capture_layout_id: SecureRandom.uuid
    )

    Domains::SurveyDesign.database(:projections)[:question_command_projection_a].insert(
      question_id: parent_question.aggregate_id,
      survey_id: survey_aggregate_id,
      account_id: account.aggregate_id
    )

    Domains::SurveyDesign.database(:projections)[:question_command_projection_a].insert(
      question_id: question.aggregate_id,
      survey_id: survey_aggregate_id,
      account_id: account.aggregate_id
    )

    # Mocking the display condition select options query to return valid parent
    allow(Domains::SurveyDesign::Queries::DisplayConditionSelectOptionsQuery).to receive(:has_valid_parent?).and_return(true)
  end

  describe "#call" do
    context "when answer branching rule is provided" do
      context "when the survey and question exist" do
        it "creates a display condition with the correct specifications" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_id: question.aggregate_id,
            display_condition_specifications: display_condition_specifications,
            metadata: metadata
          )

          expect(result).to be_success
          expect(result.value!).to include(status: :created)

          # Verify that the display condition was created
          display_condition = DisplayCondition.where(
            question_id: question.id,
            parent_question_id: parent_question.id
          ).first
          expect(display_condition).not_to be_nil
          expect(display_condition.select_option_aggregate_ids).to match_array(display_condition_specifications[:answer_branching_rule][:select_option_ids])
        end
      end

      context "when display condition already exists" do
        context "when select options are the same" do
          it "should be idempotent and not create a duplicate" do
            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              question_id: question.aggregate_id,
              display_condition_specifications: display_condition_specifications,
              metadata: metadata
            )

            expect(result).to be_success
            expect(result.value!).to include(status: :created)
            # Call again to check idempotency
            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              question_id: question.aggregate_id,
              display_condition_specifications: display_condition_specifications,
              metadata: metadata
            )

            expect(result).to be_success
            expect(result.value!).to include(status: :created)
            # Verify that the display condition was not duplicated
            display_conditions = DisplayCondition.where(
              question_id: question.id,
              parent_question_id: parent_question.id
            )
            expect(display_conditions.count).to eq(1)
            expect(display_conditions.first.select_option_aggregate_ids).to match_array(display_condition_specifications[:answer_branching_rule][:select_option_ids])
          end
        end

        context "when select options changes" do
          it "adds new select options to the existing display condition" do
            # Creating display condition with initial select options
            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              question_id: question.aggregate_id,
              display_condition_specifications: display_condition_specifications,
              metadata: metadata
            )

            expect(result).to be_success
            expect(result.value!).to include(status: :created)

            # Call again with different select options
            select_options_aggregate_ids = select_options.map(&:select_option_id).take(3) # Adding one more option
            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              question_id: question.aggregate_id,
              display_condition_specifications: {
                answer_branching_rule: {
                  id: display_condition_id,
                  parent_question_id: parent_question.aggregate_id,
                  select_option_ids: select_options_aggregate_ids
                }
              },
              metadata: metadata
            )

            expect(result).to be_success
            expect(result.value!).to include(status: :created)
            # Verify that the display condition was not duplicated
            display_conditions = DisplayCondition.where(
              question_id: question.id,
              parent_question_id: parent_question.id
            )
            expect(display_conditions.count).to eq(1)
            expect(display_conditions.first.select_option_aggregate_ids).to match_array(select_options_aggregate_ids)
          end
        end
      end

      context "when survey does not exist" do
        it "returns a failure with an appropriate error message" do
          result = subject.call(
            survey_aggregate_id: SecureRandom.uuid,
            question_id: question.aggregate_id,
            display_condition_specifications: display_condition_specifications,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to include(status: :bad_request, message: "Survey not found")
        end
      end

      context "when question does not exist" do
        it "returns a failure with an appropriate error message" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_id: SecureRandom.uuid,
            display_condition_specifications: display_condition_specifications,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to include(status: :not_found, message: "Question not found")
        end
      end

      context "when the parent question does not exist" do
        it "returns a failure with an appropriate error message" do
          invalid_parent_question_id = SecureRandom.uuid
          display_condition_specifications[:answer_branching_rule][:parent_question_id] = invalid_parent_question_id

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_id: question.aggregate_id,
            display_condition_specifications: display_condition_specifications,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to include(status: :bad_request, message: "Parent question not found")
        end
      end

      context "when select option IDs are invalid" do
        it "returns a failure with an appropriate error message" do
          display_condition_specifications[:answer_branching_rule][:select_option_ids] = ["invalid_id"]

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_id: question.aggregate_id,
            display_condition_specifications: display_condition_specifications,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to include(status: :bad_request, message: "Invalid select option IDs")
        end
      end

      context "when command validation error" do
        let(:add_answer_condition_to_question_command) { double(:add_answer_condition_to_question_command) }

        before do
          allow(add_answer_condition_to_question_command).to receive(:validate).and_return(
            OpenStruct.new(
              failure?: true,
              errors: {aggregate_id: ["Invalid aggregate_id"]}
            )
          )
        end

        it "returns a failure with an appropriate error message" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_id: question.aggregate_id,
            display_condition_specifications: display_condition_specifications,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to include(status: :bad_request, message: include("Invalid aggregate_id"))
          expect(logger).to have_received(:log).with(
            app: "murmur",
            message: include("Invalid aggregate_id"),
            module: "SurveyDesign::MultiAccountSurveying::Commands::UpsertQuestionDisplayCondition",
            status: :bad_request,
            survey_aggregate_id: survey_aggregate_id,
            question_aggregate_id: question.aggregate_id
          )
        end
      end

      context "when handler returns failure" do
        let(:add_answer_condition_to_question_command_handler) { double(:add_answer_condition_to_question_command_handler) }

        before do
          allow(add_answer_condition_to_question_command_handler).to receive(:call).and_return("failure")
        end

        it "returns failure with bad_request status" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_id: question.aggregate_id,
            display_condition_specifications: display_condition_specifications,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to include(status: :bad_request, message: include("Failed adding answer condition to question"))
          expect(logger).to have_received(:log).with(
            app: "murmur",
            message: include("Failed adding answer condition to question"),
            module: "SurveyDesign::MultiAccountSurveying::Commands::UpsertQuestionDisplayCondition",
            status: :bad_request,
            survey_aggregate_id: survey_aggregate_id,
            question_aggregate_id: question.aggregate_id
          )
        end
      end
    end
  end
end
