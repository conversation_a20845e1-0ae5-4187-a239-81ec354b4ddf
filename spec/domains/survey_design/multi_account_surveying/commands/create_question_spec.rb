require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Commands::CreateQuestion do
  let(:account) { FactoryBot.create(:account, :with_admin) }
  let(:account_aggregate_id) { account.aggregate_id }

  # Parameters for the question specifications
  let(:question_id) { "********-1234-5678-1234-5678********" }
  let(:rating_scale) { Domains::Enums::RatingSurveyQuestionScales::AGREEMENT }
  let(:question_type) { Domains::Enums::SurveyQuestionTypes::RATING }
  let(:section_id) { "38470b33-b57b-4122-bff1-411fe36c365e" }
  let(:factor_id) { "dc2b21cb-4b97-45b6-b8a4-b3715a70d622" }
  let(:factor_ids) { [factor_id] }
  let(:description_translations) {
    [
      {"text" => "Test Text", "locale" => "en"},
      {"text" => "Test Text FR", "locale" => "fr"}
    ]
  }
  let(:selection_limit) { nil }
  let(:intended_purpose) { Domains::Enums::SurveyQuestionIntendedPurposes::FEEDBACK }
  let(:stq_order) { nil }
  let(:code) { "#{account.subdomain}.question.********" }
  let(:is_mandatory) { "false" }

  # Parameters for #initialize
  let(:add_question_command) { Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand }
  let(:add_question_command_handler) { Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommandHandler.new }
  let(:make_question_mandatory_command) { Domains::SurveyDesign::SurveyQuestion::MakeMandatoryCommand }
  let(:make_question_mandatory_command_handler) { Domains::SurveyDesign::SurveyQuestion::MakeMandatoryCommandHandler.new }
  let(:enable_other_select_option_command) { Domains::SurveyDesign::SurveyQuestion::EnableOtherSelectOptionCommand }
  let(:enable_other_select_option_command_handler) { Domains::SurveyDesign::SurveyQuestion::EnableOtherSelectOptionCommandHandler.new }
  let(:change_other_select_option_label_command) { Domains::SurveyDesign::SurveyQuestion::ChangeOtherSelectOptionLabel }
  let(:change_other_select_option_label_command_handler) { Domains::SurveyDesign::SurveyQuestion::ChangeOtherSelectOptionLabelCommandHandler.new }
  let(:command_handler_factory) { instance_double(SurveyDesign::Commands::Factory) }
  let(:logger) { double(:logger) }

  subject {
    described_class.new(
      add_question_command: add_question_command,
      command_handler_factory: command_handler_factory,
      logger: logger
    )
  }

  # Parameters for #call
  let(:survey_aggregate_id) { "ba619b05-d8ea-4a65-9d79-3094caefa2cf" }
  let(:question_specifications) do
    {
      "question_id" => question_id,
      "rating_scale" => rating_scale,
      "question_type" => question_type,
      "section_id" => section_id,
      "factor_ids" => factor_ids,
      "description_translations" => description_translations,
      "selection_limit" => selection_limit,
      "intended_purpose" => intended_purpose,
      "stq_order" => stq_order,
      "code" => code,
      "select_option_specifications" => nil,
      "is_mandatory" => is_mandatory
    }
  end
  let(:metadata) do
    EventFramework::Event::Metadata.new(
      user_id: "e6419ccd-0bd6-4e14-bbd0-53ea8649fa78",
      account_id: account_aggregate_id,
      correlation_id: "6cdb1a76-5a5c-4cd8-8ce2-94da6cf97602"
    )
  end

  before do
    allow(logger).to receive(:log)
    # Create an event sourced survey
    survey = setup_survey
    # Setup a section, factor and previous positioned question/stq
    section = setup_section(survey)
    setup_factor(survey)
    previous_question = setup_previous_positioned_question(survey, section)
    # Stub SurveyQuery and SurveyCaptureLayoutAggregate for command handler
    stub_survey_query(survey)
    stub_survey_capture_layout_aggregate(survey, section, previous_question)
    # make command factory return the command handler set up on this test
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey.aggregate_id, command_class: add_question_command).and_return(add_question_command_handler)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey.aggregate_id, command_class: make_question_mandatory_command).and_return(make_question_mandatory_command_handler)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey.aggregate_id, command_class: enable_other_select_option_command).and_return(enable_other_select_option_command_handler)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey.aggregate_id, command_class: change_other_select_option_label_command).and_return(change_other_select_option_label_command_handler)
  end

  describe "#call" do
    let(:survey) { Survey.where(aggregate_id: survey_aggregate_id).first }
    let(:question) { Question.where(aggregate_id: question_id).first }
    let(:stq) { survey.stqs.where(question_id: question).first }
    let(:section) { survey.themes.where(section_id: section_id).first }
    let(:factor) { survey.factors.where(aggregate_id: factor_id).first }

    context "when all parameters are valid" do
      let(:expected_result) { {status: :created, data: {id: question.id.to_s, aggregate_id: question.aggregate_id, stq_id: stq.id.to_s}} }

      ### RatingQuestion
      context "for a rating question" do
        let(:question_type) { Domains::Enums::SurveyQuestionTypes::RATING }
        let(:selection_limit) { nil }

        it "creates a new question and associated stq, then returns success" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_specifications: question_specifications,
            metadata: metadata
          )

          expect(question.is_a?(RatingQuestion)).to eq(true)
          expect(question).to have_attributes(
            aggregate_id: question_id,
            description: "Test Text",
            code: code,
            scale: rating_scale,
            survey_aggregate_id: survey_aggregate_id,
            account_id: account.id
          )

          expect(stq).to have_attributes(
            question_id: question.id,
            order: 0,
            type: question.type,
            theme_id: section.id,
            factor_ids: [factor.id]
          )

          expect(result).to be_success
          expect(result.value!).to eq(expected_result)
        end
      end

      ### FreeTextQuestion
      context "for a free text question" do
        let(:question_type) { Domains::Enums::SurveyQuestionTypes::FREE_TEXT }
        let(:selection_limit) { nil }

        it "creates a new question and associated stq, then returns success" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_specifications: question_specifications,
            metadata: metadata
          )

          expect(question.is_a?(FreeTextQuestion)).to eq(true)
          expect(question).to have_attributes(
            aggregate_id: question_id,
            description: "Test Text",
            code: code,
            scale: rating_scale,
            survey_aggregate_id: survey_aggregate_id,
            account_id: account.id
          )

          expect(stq).to have_attributes(
            question_id: question.id,
            order: 0,
            type: question.type,
            theme_id: section.id,
            factor_ids: [factor.id]
          )
          expect(result).to be_success
          expect(result.value!).to eq(expected_result)
        end
      end

      shared_examples "select question creation" do |selection_limit, question_class|
        let(:question_type) { Domains::Enums::SurveyQuestionTypes::SELECT }
        let(:selection_limit) { selection_limit }

        it "creates a new question, associated stq, and select options, then returns success" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_specifications: question_specifications,
            select_option_specifications: build_select_options_specifications,
            metadata: metadata
          )

          expect(question.is_a?(question_class)).to eq(true)
          expect(question).to have_attributes(
            aggregate_id: question_id,
            description: "Test Text",
            code: code,
            scale: rating_scale,
            survey_aggregate_id: survey_aggregate_id,
            account_id: account.id
          )

          expect(stq).to have_attributes(
            question_id: question.id,
            order: 0,
            type: question.type,
            theme_id: section.id,
            factor_ids: [factor.id]
          )

          expect(question.select_options.count).to eq(3)

          expected_result = build_expected_result(question, stq, question.select_options)

          expect(result).to be_success
          expect(result.value!).to eq(expected_result)
        end
      end

      context "for a single select question" do
        it_behaves_like "select question creation", 1, SingleSelectQuestion
      end

      context "for a multi select question" do
        it_behaves_like "select question creation", 3, MultiSelectQuestion
      end

      def build_select_options_specifications
        [
          {
            "select_option_id" => "a0d2f305-7ba1-44e5-a698-d9ef9e357b55",
            "value_translations" => [{"text" => "Test Option 1", "locale" => "en"}],
            "sort_term" => {"en" => "00000"}
          },
          {
            "select_option_id" => "a0d2f305-7ba1-44e5-a698-d9ef9e357b56",
            "value_translations" => [{"text" => "Test Option 2", "locale" => "en"}],
            "sort_term" => {"en" => "00001"}
          },
          {
            "select_option_id" => "a0d2f305-7ba1-44e5-a698-d9ef9e357b57",
            "value_translations" => [{"text" => "Test Option 3", "locale" => "en"}],
            "sort_term" => {"en" => "00002"}
          }
        ]
      end

      def build_expected_result(question, stq, select_options = [])
        {
          status: :created,
          data: {
            id: question.id.to_s,
            aggregate_id: question.aggregate_id,
            stq_id: stq.id.to_s,
            select_options: select_options.map { |option| {id: option.id.to_s, aggregate_id: option.select_option_id} }
          }
        }
      end

      ## Test positioning of STQ
      context "with a specific stq order" do
        let(:stq_order) { 1 }

        it "positions the stq correctly" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_specifications: question_specifications,
            metadata: metadata
          )

          expect(result).to be_success
          expect(result.value!).to eq(expected_result)
          expect(stq.order).to eq(stq_order)
        end
      end

      context "when question mark as mandatory" do
        let(:is_mandatory) { "true" }

        it "creates a new mandatory question" do
          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            question_specifications: question_specifications,
            metadata: metadata
          )

          expect(question).to have_attributes(
            aggregate_id: question_id,
            description: "Test Text",
            code: code,
            scale: rating_scale,
            survey_aggregate_id: survey_aggregate_id,
            account_id: account.id,
            mandatory: true
          )

          expect(result).to be_success
          expect(result.value!).to eq(expected_result)
        end
      end

      context "when other option is provided" do
        context "when SelectQuestion" do
          let(:question_type) { Domains::Enums::SurveyQuestionTypes::SELECT }

          it "creates a new question with the other option" do
            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              question_specifications: question_specifications.merge(
                "other_option" => {
                  "value_translations" => [{"text" => "Other", "locale" => "en"}]
                }
              ),
              select_option_specifications: build_select_options_specifications,
              metadata: metadata
            )

            expected_result = build_expected_result(question, stq, question.select_options)

            expect(result).to be_success
            expect(result.value!).to eq(expected_result)
            expect(question.other_option).to be_truthy
            expect(question.custom_other_option_label).to eq("Other")
          end
        end

        context "when other questions" do
          let(:question_type) { Domains::Enums::SurveyQuestionTypes::RATING }

          it "creates a new question without the other option" do
            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              question_specifications: question_specifications.merge(
                "other_option" => {
                  "value_translations" => [{"text" => "Other", "locale" => "en"}]
                }
              ),
              metadata: metadata
            )

            expect(result).to be_success
            expect(result.value!).to eq(expected_result)
            expect { question.other_option }.to raise_error(NoMethodError)
          end
        end
      end

      context "when other option is not provided" do
        context "when SelectQuestion" do
          let(:question_type) { Domains::Enums::SurveyQuestionTypes::SELECT }

          it "creates a new question without the other option" do
            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              question_specifications: question_specifications,
              select_option_specifications: build_select_options_specifications,
              metadata: metadata
            )

            expected_result = build_expected_result(question, stq, question.select_options)

            expect(result).to be_success
            expect(result.value!).to eq(expected_result)
            expect(question.other_option).to be_falsey
          end
        end

        context "when other questions" do
          let(:question_type) { Domains::Enums::SurveyQuestionTypes::FREE_TEXT }

          it "creates a new question without the other option" do
            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              question_specifications: question_specifications,
              metadata: metadata
            )

            expect(result).to be_success
            expect(result.value!).to eq(expected_result)
            expect(question.other_option).to be_falsey
          end
        end
      end
    end

    context "when survey is not found" do
      it "returns failure with bad_request status" do
        non_existing_survey_aggregate_id = "00000000-0000-0000-0000-000000000000"

        result = subject.call(
          survey_aggregate_id: non_existing_survey_aggregate_id,
          question_specifications: question_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Survey not found")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Survey not found",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: non_existing_survey_aggregate_id
        )
      end
    end

    context "when section is not found" do
      let(:question_specifications) { super().merge("section_id" => "00000000-0000-0000-0000-000000000000") }

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_specifications: question_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Section not found with id '00000000-0000-0000-0000-000000000000'")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Section not found with id '00000000-0000-0000-0000-000000000000'",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when a required parameter is missing" do
      let(:question_specifications) { super().except("description_translations") }

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_specifications: question_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "{:text=>[\"must be filled\"]}")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "{:text=>[\"must be filled\"]}",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when handler returns failure" do
      let(:add_question_command_handler) { double(:add_question_command_handler) }
      before do
        allow(add_question_command_handler).to receive(:call).and_return("failure")
      end

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_specifications: question_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure[:message]).to include("Failed adding question")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Failed adding question",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when question is created but stq is not" do
      let(:stq_repo) { double(:stq_repo) }
      let(:subject) {
        described_class.new(
          stq_repo: stq_repo,
          add_question_command: add_question_command,
          command_handler_factory: command_handler_factory,
          logger: logger
        )
      }

      before do
        allow(stq_repo).to receive(:call).and_return(double(find_for_question: nil))
      end

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_specifications: question_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure[:message]).to include("Stq creation for the question '#{question.id}' is failed")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Stq creation for the question '#{question.id}' is failed",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when factor ids are invalid" do
      let(:factor_ids) { ["00000000-0000-0000-0000-000000000000"] }

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_specifications: question_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "{:errors=>[\"Could not find active factor with aggregate_id '00000000-0000-0000-0000-000000000000'\"]}")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "{:errors=>[\"Could not find active factor with aggregate_id '00000000-0000-0000-0000-000000000000'\"]}",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when an unexpected error occurs" do
      before do
        allow(add_question_command_handler).to receive(:call).and_raise(StandardError, "Unexpected error")
      end

      it "returns failure with internal_server_error status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          question_specifications: question_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :internal_server_error)
        expect(result.failure).to include(message: "Unexpected error")

        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Unexpected error",
          module: described_class.name,
          status: :internal_server_error,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end
  end

  private

  def setup_survey
    survey_template = FactoryBot.create(:survey_template, :custom_survey)
    SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
      template: survey_template,
      user: account.administrators.first,
      account: account,
      aggregate_id_override: survey_aggregate_id,
      authorization: nil
    )
  end

  def setup_section(survey)
    FactoryBot.create(:theme, survey: survey, section_id: section_id, name: [{locale: "en", text: "Overall"}])
  end

  def setup_factor(survey)
    FactoryBot.create(:factor, survey: survey, aggregate_id: factor_id)
  end

  def setup_previous_positioned_question(survey, section)
    previous_positioned_question = FactoryBot.create(:rating_question, account: account)
    FactoryBot.create(:survey_to_question, survey: survey, theme_id: section.id, question: previous_positioned_question, order: 0)

    previous_positioned_question
  end

  ##
  # Stub the SurveyQuery to return SurveyQuery::Result.
  # This is used to determine survey assignment for stq by command handler.
  def stub_survey_query(survey)
    allow(Domains::SurveyDesign::Queries::SurveyQuery).to receive(:call).and_return(
      Domains::SurveyDesign::Queries::SurveyQuery::Result.new(
        survey_id: survey_aggregate_id,
        account_id: account_aggregate_id,
        survey_capture_layout_id: survey.survey_capture_layout_id
      )
    )
  end

  ##
  # Stub the SurveyCaptureLayoutAggregate to return SurveyCaptureLayoutAggregate::Section.
  # This is used to determine section assignment and question positioning for stq by command handler.
  def stub_survey_capture_layout_aggregate(survey, section, previous_positioned_question)
    aggregate = Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate.build(survey.survey_capture_layout_id)
    # Create section aggregate
    section_intended_purpose = Domains::Enums::SectionIntendedPurposes::STANDARD
    aggregate_section = Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate::Section.new(
      id: section.section_id,
      name: [{locale: "en", text: section.name}],
      short_description: [{locale: "en", text: section.name}],
      long_description: [{locale: "en", text: section.name}],
      code: section.code,
      intended_purpose: section_intended_purpose
    )
    # Set previous question to section aggregate
    aggregate_section.questions << previous_positioned_question.aggregate_id

    aggregate.send(:sections_by_purpose)[section_intended_purpose] << aggregate_section

    allow(Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate).to receive(:build).with(survey.survey_capture_layout_id).and_return(aggregate)
  end
end
