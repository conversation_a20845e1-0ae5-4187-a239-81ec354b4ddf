require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Commands::UpdateSection do
  let(:section_repo) { Theme.extras(read: {mode: :secondary}) }
  let(:survey_repo) { Survey.extras(read: {mode: :secondary}) }
  let(:move_section_command) { Domains::SurveyDesign::SurveyCaptureLayout::MoveSectionCommand }
  let(:move_section_command_handler) { Domains::SurveyDesign::SurveyCaptureLayout::MoveSectionCommandHandler.new }
  let(:rename_section_command) { Domains::SurveyDesign::SurveyCaptureLayout::RenameSectionCommand }
  let(:rename_section_command_handler) { Domains::SurveyDesign::SurveyCaptureLayout::RenameSectionCommandHandler.new }
  let(:change_section_long_description_command) { Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionLongDescriptionCommand }
  let(:change_section_long_description_command_handler) { Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionLongDescriptionCommandHandler.new }
  let(:change_section_short_description_command) { Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionShortDescriptionCommand }
  let(:change_section_short_description_command_handler) { Domains::SurveyDesign::SurveyCaptureLayout::ChangeSectionShortDescriptionCommandHandler.new }
  let(:enps_protection_checker) { ENPSProtection }
  let(:logger) { double(:logger) }
  let(:command_handler_factory) { instance_double(SurveyDesign::Commands::Factory) }

  let(:account) { FactoryBot.create(:account, :with_admin) }
  let(:account_aggregate_id) { account.aggregate_id }
  let(:survey_aggregate_id) { "ba619b05-d8ea-4a65-9d79-3094caefa2cf" }
  let(:survey) { survey_repo.where(aggregate_id: survey_aggregate_id).first }
  let(:section_id) { "********-1234-5678-1234-************" }
  let(:code) { "#{account.subdomain}.section.#{section_id.split("-")[0]}" }
  let(:position) { "1" }
  let(:intended_purpose) { "standard" }
  let(:name_translations) do
    [
      {"text" => "Test Name", "locale" => "en"},
      {"text" => "Test Name FR", "locale" => "fr"}
    ]
  end
  let(:short_description) do
    [
      {"text" => "Short Description", "locale" => "en"},
      {"text" => "Short Description FR", "locale" => "fr"}
    ]
  end
  let(:long_description) do
    [
      {"text" => "Long Description", "locale" => "en"},
      {"text" => "Long Description FR", "locale" => "fr"}
    ]
  end
  let(:section_specifications) do
    {
      "section_id" => section_id,
      "position" => position,
      "intended_purpose" => intended_purpose,
      "name_translations" => name_translations,
      "short_description" => short_description,
      "long_description" => long_description,
      "code" => code
    }
  end

  let(:metadata) do
    EventFramework::Event::Metadata.new(
      user_id: "e6419ccd-0bd6-4e14-bbd0-53ea8649fa78",
      account_id: account_aggregate_id,
      correlation_id: "6cdb1a76-5a5c-4cd8-8ce2-94da6cf97602"
    )
  end

  subject do
    described_class.new(
      section_repo: section_repo,
      survey_repo: survey_repo,
      move_section_command: move_section_command,
      rename_section_command: rename_section_command,
      change_section_long_description_command: change_section_long_description_command,
      change_section_short_description_command: change_section_short_description_command,
      enps_protection_checker: enps_protection_checker,
      logger: logger,
      command_handler_factory: command_handler_factory
    )
  end

  before do
    allow(logger).to receive(:log)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: move_section_command).and_return(move_section_command_handler)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: rename_section_command).and_return(rename_section_command_handler)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: change_section_long_description_command).and_return(change_section_long_description_command_handler)
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: change_section_short_description_command).and_return(change_section_short_description_command_handler)

    survey_template = FactoryBot.create(:survey_template, :custom_survey)
    # Create an event sourced survey
    SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
      template: survey_template,
      user: account.administrators.first,
      account: account,
      aggregate_id_override: survey_aggregate_id,
      authorization: nil
    )

    # Create a section in the survey
    SurveyDesign::MultiAccountSurveying::Commands::CreateSection.new.call(
      survey_aggregate_id: survey_aggregate_id,
      section_specifications: section_specifications,
      metadata: metadata
    )
  end

  describe "#call" do
    context "when all parameters are valid" do
      context "when position is updated" do
        before do
          # Create a before section to test position update
          SurveyDesign::MultiAccountSurveying::Commands::CreateSection.new.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: {
              "section_id" => SecureRandom.uuid,
              "position" => "2",
              "intended_purpose" => "standard",
              "name_translations" => [{"text" => "Before Section", "locale" => "en"}],
              "short_description" => [{"text" => "Before Section Short", "locale" => "en"}],
              "long_description" => [{"text" => "Before Section Long", "locale" => "en"}],
              "code" => "before_section_code"
            },
            metadata: metadata
          )
        end

        it "updates the section position and returns success" do
          specs = section_specifications.merge(position: "2")

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_success

          section = section_repo.where(section_id: section_id, survey_id: survey.id.to_s).first

          expect(result.value!).to eq({status: :created, data: {id: section.id.to_s, aggregate_id: section_id}})
          expect(section.order).to eq(2)
        end
      end

      context "when intended purpose is updated" do
        it "returns an error" do
          specs = section_specifications.merge(intended_purpose: "interview")

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to eq({status: :bad_request, message: "Not allowed to change intended purpose"})
          expect(logger).to have_received(:log).with(
            app: "murmur",
            message: "Not allowed to change intended purpose",
            module: "SurveyDesign::MultiAccountSurveying::Commands::UpdateSection",
            status: :bad_request,
            survey_aggregate_id: survey_aggregate_id
          )
        end
      end

      context "when name translations are updated" do
        it "updates the section and returns success" do
          specs = section_specifications.merge(name_translations: [
            {"text" => "Updated Test Name", "locale" => "en"},
            {"text" => "Updated Test Name FR", "locale" => "fr"}
          ])

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_success

          section = section_repo.where(section_id: section_id, survey_id: survey.id.to_s).first

          expect(result.value!).to eq({status: :created, data: {id: section.id.to_s, aggregate_id: section_id}})
          expect(section.name_translations).to include({"en" => "Updated Test Name", "fr" => "Updated Test Name FR"})
        end
      end

      context "when short description is updated" do
        it "updates the section and returns success" do
          specs = section_specifications.merge(short_description: [
            {"text" => "Updated Short Description", "locale" => "en"},
            {"text" => "Updated Short Description FR", "locale" => "fr"}
          ])

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_success

          section = section_repo.where(section_id: section_id, survey_id: survey.id.to_s).first

          expect(result.value!).to eq({status: :created, data: {id: section.id.to_s, aggregate_id: section_id}})
          expect(section.short_desc).to include("Updated Short Description")
        end
      end

      context "when long description is updated" do
        it "updates the section and returns success" do
          specs = section_specifications.merge(long_description: [
            {"text" => "Updated Long Description", "locale" => "en"},
            {"text" => "Updated Long Description FR", "locale" => "fr"}
          ])

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_success

          section = section_repo.where(section_id: section_id, survey_id: survey.id.to_s).first

          expect(result.value!).to eq({status: :created, data: {id: section.id.to_s, aggregate_id: section_id}})
          expect(section.long_desc).to include("Updated Long Description")
        end
      end
    end

    context "when parameters are invalid" do
      context "when ENPS protection is enabled" do
        before do
          allow(enps_protection_checker).to receive(:enps_11_section?).with(section_id).and_return(true)
        end

        it "returns an error when trying to update section" do
          specs = section_specifications.merge(name_translations: [
            {"text" => "Updated Test Name", "locale" => "en"},
            {"text" => "Updated Test Name FR", "locale" => "fr"}
          ])

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to eq({status: :bad_request, message: "Blocked attempt to delete ENPS 11 section"})
          expect(logger).to have_received(:log).with(
            app: "murmur",
            message: "Blocked attempt to delete ENPS 11 section",
            module: "SurveyDesign::MultiAccountSurveying::Commands::UpdateSection",
            status: :bad_request,
            survey_aggregate_id: survey_aggregate_id
          )
        end
      end

      context "when survey does not exist" do
        it "returns an error" do
          survey_aggregate_id = SecureRandom.uuid
          specs = section_specifications.merge(name_translations: [
            {"text" => "Updated Test Name", "locale" => "en"},
            {"text" => "Updated Test Name FR", "locale" => "fr"}
          ])

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to eq({status: :bad_request, message: "Survey not found"})
          expect(logger).to have_received(:log).with(
            app: "murmur",
            message: "Survey not found",
            module: "SurveyDesign::MultiAccountSurveying::Commands::UpdateSection",
            status: :bad_request,
            survey_aggregate_id: survey_aggregate_id
          )
        end
      end

      context "when survey is not an event sourced survey" do
        it "returns an error" do
          survey = FactoryBot.create(:survey)

          specs = section_specifications.merge(name_translations: [
            {"text" => "Updated Test Name", "locale" => "en"},
            {"text" => "Updated Test Name FR", "locale" => "fr"}
          ])

          result = subject.call(
            survey_aggregate_id: survey.aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to eq({status: :bad_request, message: "Survey is not event sourced"})
          expect(logger).to have_received(:log).with(
            app: "murmur",
            message: "Survey is not event sourced",
            module: "SurveyDesign::MultiAccountSurveying::Commands::UpdateSection",
            status: :bad_request,
            survey_aggregate_id: survey.aggregate_id
          )
        end
      end

      context "when section does not exist" do
        it "returns an error" do
          section_id = SecureRandom.uuid
          specs = section_specifications.merge(section_id: section_id)

          result = subject.call(
            survey_aggregate_id: survey_aggregate_id,
            section_specifications: specs,
            metadata: metadata
          )

          expect(result).to be_failure
          expect(result.failure).to eq({status: :not_found, message: "Section not found"})
          expect(logger).to have_received(:log).with(
            app: "murmur",
            message: "Section not found",
            module: "SurveyDesign::MultiAccountSurveying::Commands::UpdateSection",
            status: :not_found,
            survey_aggregate_id: survey_aggregate_id
          )
        end
      end

      context "when fail updating position" do
        context "when invalid parameters are provided" do
          let(:move_section_command) { double(:move_section_command) }

          before do
            allow(move_section_command).to receive(:validate).and_return(
              OpenStruct.new(
                failure?: true,
                errors: {name: ["Invalid positioned_after_section_id"]}
              )
            )
          end

          it "returns failure with bad_request status" do
            specs = section_specifications.merge(position: "0")

            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              section_specifications: specs,
              metadata: metadata
            )

            expect(result).to be_failure
            expect(result.failure).to include(status: :bad_request)
            expect(result.failure).to include(message: include("Invalid positioned_after_section_id"))
            expect(logger).to have_received(:log).with(
              app: "murmur",
              message: include("Invalid positioned_after_section_id"),
              module: described_class.name,
              status: :bad_request,
              survey_aggregate_id: survey_aggregate_id
            )
          end
        end

        context "when handler returns failure" do
          let(:move_section_command_handler) { double(:move_section_command_handler) }

          before do
            allow(move_section_command_handler).to receive(:call).and_return("failure")
          end

          it "returns failure with bad_request status" do
            specs = section_specifications.merge(position: "2")

            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              section_specifications: specs,
              metadata: metadata
            )

            expect(result).to be_failure
            expect(result.failure).to eq({status: :bad_request, message: "Failed to move section"})
            expect(logger).to have_received(:log).with(
              app: "murmur",
              message: "Failed to move section",
              module: described_class.name,
              status: :bad_request,
              survey_aggregate_id: survey_aggregate_id
            )
          end
        end
      end

      context "when fail renaming section" do
        context "when invalid parameters are provided" do
          let(:rename_section_command) { double(:rename_section_command) }

          before do
            allow(rename_section_command).to receive(:validate).and_return(
              OpenStruct.new(
                failure?: true,
                errors: {name: ["Invalid name"]}
              )
            )
          end

          it "returns failure with bad_request status" do
            specs = section_specifications.merge(name_translations: [
              {"text" => "", "locale" => "en"}
            ])

            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              section_specifications: specs,
              metadata: metadata
            )

            expect(result).to be_failure
            expect(result.failure).to include(status: :bad_request)
            expect(result.failure).to include(message: include("Invalid name"))
            expect(logger).to have_received(:log).with(
              app: "murmur",
              message: include("Invalid name"),
              module: described_class.name,
              status: :bad_request,
              survey_aggregate_id: survey_aggregate_id
            )
          end
        end

        context "when handler returns failure" do
          let(:rename_section_command_handler) { double(:rename_section_command_handler) }

          before do
            allow(rename_section_command_handler).to receive(:call).and_return("failure")
          end

          it "returns failure with bad_request status" do
            specs = section_specifications.merge(name_translations: [
              {"text" => "New Name", "locale" => "en"}
            ])

            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              section_specifications: specs,
              metadata: metadata
            )

            expect(result).to be_failure
            expect(result.failure).to eq({status: :bad_request, message: "Failed renaming section"})
            expect(logger).to have_received(:log).with(
              app: "murmur",
              message: "Failed renaming section",
              module: described_class.name,
              status: :bad_request,
              survey_aggregate_id: survey_aggregate_id
            )
          end
        end
      end

      context "when fail changing long description" do
        context "when invalid parameters are provided" do
          let(:change_section_long_description_command) { double(:change_section_long_description_command) }

          before do
            allow(change_section_long_description_command).to receive(:validate).and_return(
              OpenStruct.new(
                failure?: true,
                errors: {long_description: ["Invalid long description"]}
              )
            )
          end

          it "returns failure with bad_request status" do
            specs = section_specifications.merge(long_description: [
              {"text" => "", "locale" => "en"}
            ])

            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              section_specifications: specs,
              metadata: metadata
            )

            expect(result).to be_failure
            expect(result.failure).to include(status: :bad_request)
            expect(result.failure).to include(message: include("Invalid long description"))
            expect(logger).to have_received(:log).with(
              app: "murmur",
              message: include("Invalid long description"),
              module: described_class.name,
              status: :bad_request,
              survey_aggregate_id: survey_aggregate_id
            )
          end
        end

        context "when handler returns failure" do
          let(:change_section_long_description_command_handler) { double(:change_section_long_description_command_handler) }

          before do
            allow(change_section_long_description_command_handler).to receive(:call).and_return("failure")
          end

          it "returns failure with bad_request status" do
            specs = section_specifications.merge(long_description: [
              {"text" => "New Long Description", "locale" => "en"}
            ])

            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              section_specifications: specs,
              metadata: metadata
            )

            expect(result).to be_failure
            expect(result.failure).to eq({status: :bad_request, message: "Failed changing long description"})
            expect(logger).to have_received(:log).with(
              app: "murmur",
              message: "Failed changing long description",
              module: described_class.name,
              status: :bad_request,
              survey_aggregate_id: survey_aggregate_id
            )
          end
        end
      end

      context "when fail changing short description" do
        context "when invalid parameters are provided" do
          let(:change_section_short_description_command) { double(:change_section_short_description_command) }

          before do
            allow(change_section_short_description_command).to receive(:validate).and_return(
              OpenStruct.new(
                failure?: true,
                errors: {short_description: ["Invalid short description"]}
              )
            )
          end

          it "returns failure with bad_request status" do
            specs = section_specifications.merge(short_description: [
              {"text" => "", "locale" => "en"}
            ])

            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              section_specifications: specs,
              metadata: metadata
            )

            expect(result).to be_failure
            expect(result.failure).to include(status: :bad_request)
            expect(result.failure).to include(message: include("Invalid short description"))
            expect(logger).to have_received(:log).with(
              app: "murmur",
              message: include("Invalid short description"),
              module: described_class.name,
              status: :bad_request,
              survey_aggregate_id: survey_aggregate_id
            )
          end
        end

        context "when handler returns failure" do
          let(:change_section_short_description_command_handler) { double(:change_section_short_description_command_handler) }

          before do
            allow(change_section_short_description_command_handler).to receive(:call).and_return("failure")
          end

          it "returns failure with bad_request status" do
            specs = section_specifications.merge(short_description: [
              {"text" => "New Short Description", "locale" => "en"}
            ])

            result = subject.call(
              survey_aggregate_id: survey_aggregate_id,
              section_specifications: specs,
              metadata: metadata
            )

            expect(result).to be_failure
            expect(result.failure).to eq({status: :bad_request, message: "Failed changing short description"})
            expect(logger).to have_received(:log).with(
              app: "murmur",
              message: "Failed changing short description",
              module: described_class.name,
              status: :bad_request,
              survey_aggregate_id: survey_aggregate_id
            )
          end
        end
      end
    end
  end
end
