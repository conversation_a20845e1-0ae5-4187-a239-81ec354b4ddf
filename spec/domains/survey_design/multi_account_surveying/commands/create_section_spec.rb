require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Commands::CreateSection do
  let(:account) { FactoryBot.create(:account, :with_admin) }
  let(:add_section_command) { Domains::SurveyDesign::SurveyCaptureLayout::AddSectionCommand }
  let(:add_section_command_handler) { Domains::SurveyDesign::SurveyCaptureLayout::AddSectionCommandHandler.new }
  let(:account_aggregate_id) { account.aggregate_id }
  let(:survey_aggregate_id) { "ba619b05-d8ea-4a65-9d79-3094caefa2cf" }
  let(:section_id) { "********-1234-5678-1234-************" }
  let(:code) { "#{account.subdomain}.section.#{section_id.split("-")[0]}" }
  let(:name_translations) do
    [
      {"text" => "Test Name", "locale" => "en"},
      {"text" => "Test Name FR", "locale" => "fr"}
    ]
  end
  let(:intended_purpose) { Domains::Enums::SectionIntendedPurposes::STANDARD }
  let(:section_specifications) do
    {
      "section_id" => section_id,
      "position" => "1",
      "intended_purpose" => intended_purpose,
      "name_translations" => name_translations,
      "short_description" => [
        {"text" => "Short Description", "locale" => "en"},
        {"text" => "Short Description FR", "locale" => "fr"}
      ],
      "long_description" => [
        {"text" => "Long Description", "locale" => "en"},
        {"text" => "Long Description FR", "locale" => "fr"}
      ],
      "code" => code
    }
  end

  let(:metadata) do
    EventFramework::Event::Metadata.new(
      user_id: "e6419ccd-0bd6-4e14-bbd0-53ea8649fa78",
      account_id: account_aggregate_id,
      correlation_id: "6cdb1a76-5a5c-4cd8-8ce2-94da6cf97602"
    )
  end

  let(:logger) { double(:logger) }
  let(:command_handler_factory) { instance_double(SurveyDesign::Commands::Factory) }

  subject { described_class.new(add_section_command: add_section_command, command_handler_factory: command_handler_factory, logger: logger) }

  before do
    allow(logger).to receive(:log)

    survey_template = FactoryBot.create(:survey_template, :custom_survey)
    # Create an event sourced survey
    SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
      template: survey_template,
      user: account.administrators.first,
      account: account,
      aggregate_id_override: survey_aggregate_id,
      authorization: nil
    )
    # make command factory return the command handler set up on this test
    allow(command_handler_factory).to receive(:handler_for).with(survey_id: survey_aggregate_id, command_class: add_section_command).and_return(add_section_command_handler)
  end

  describe "#call" do
    context "when all parameters are valid" do
      it "creates a new section and returns success" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: section_specifications,
          metadata: metadata
        )

        section = Theme.where(section_id: section_id).first

        expect(result).to be_success
        expect(result.value!).to eq({status: :created, data: {id: section.id.to_s, aggregate_id: section_id}})
        expect(section).to have_attributes(
          name: "Test Name",
          code: code,
          long_desc: "Long Description",
          short_desc: "Short Description",
          order: section_specifications["position"].to_i
        )
      end

      it "positions the section correctly" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: section_specifications,
          metadata: metadata
        )
        section = Theme.where(section_id: section_id).first

        expect(result).to be_success
        expect(result.value!).to eq({status: :created, data: {id: section.id.to_s, aggregate_id: section_id}})

        expect(section.order).to eq(section_specifications["position"].to_i)
      end
    end

    context "when survey is not found" do
      it "returns failure with bad_request status" do
        survey_aggregate_id = SecureRandom.uuid

        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: section_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Survey not found")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Survey not found",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when section_id is missing" do
      it "returns failure with bad_request status" do
        invalid_specifications = section_specifications.except("section_id")
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: invalid_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
      end
    end

    context "when unsupported intended purpose is provided" do
      let(:intended_purpose) { Domains::Enums::SectionIntendedPurposes::INTERVIEW }

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: section_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Unsupported Intended Purpose")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Unsupported Intended Purpose",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when invalid parameters are provided" do
      let(:add_section_command) { double(:add_section_command) }

      before do
        allow(add_section_command).to receive(:validate).and_return(
          OpenStruct.new(
            failure?: true,
            errors: {name: ["Invalid name"]}
          )
        )
      end

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: section_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: include("Invalid name"))
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: include("Invalid name"),
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when handler returns failure" do
      let(:add_section_command_handler) { double(:add_section_command_handler) }

      before do
        allow(add_section_command_handler).to receive(:call).and_return("failure")
      end

      it "returns failure with bad_request status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: section_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :bad_request)
        expect(result.failure).to include(message: "Failed adding section")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Failed adding section",
          module: described_class.name,
          status: :bad_request,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end

    context "when an unexpected error occurs" do
      before do
        allow(add_section_command_handler).to receive(:call).and_raise(StandardError, "Unexpected error")
      end

      it "returns failure with internal_server_error status" do
        result = subject.call(
          survey_aggregate_id: survey_aggregate_id,
          section_specifications: section_specifications,
          metadata: metadata
        )

        expect(result).to be_failure
        expect(result.failure).to include(status: :internal_server_error)
        expect(result.failure).to include(message: "Unexpected error")
        expect(logger).to have_received(:log).with(
          app: "murmur",
          message: "Unexpected error",
          module: described_class.name,
          status: :internal_server_error,
          survey_aggregate_id: survey_aggregate_id
        )
      end
    end
  end
end
