require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Queries::GetSurveyData do
  include Dry::Monads[:result]

  let(:logger) { double("Splunk::Logger") }
  let(:extract_survey_data) { double(:extract_survey_data) }
  let(:extract_survey_data) { ->(survey_aggregate_id:) { Success({id: survey.aggregate_id, name: {"en" => "Test Survey"}}) } }
  let(:query) { described_class.new(logger: logger, extract_survey_data: extract_survey_data) }

  let(:is_org_survey) { true }
  let(:account) { FactoryBot.create(:account, subdomain: "test") }
  let(:survey) {
    FactoryBot.create(
      :survey,
      name: "Test Survey",
      account_id: account.id,
      description: "Test Description",
      type: :engagement,
      survey_period_type: :snapshot
    )
  }

  let(:survey_aggregate_id) { survey.aggregate_id }

  before do
    allow(logger).to receive(:log)
  end

  describe "#call" do
    context "when the survey is valid and active" do
      it "returns a success result" do
        result = query.call(survey_aggregate_id: survey_aggregate_id)

        expect(result).to be_success
        expect(result.value!).to eq(
          accountId: account.aggregate_id,
          accountMongoId: account.id.to_s,
          accountSubdomain: "test",
          region: "us",
          id: survey.aggregate_id,
          name: {"en" => "Test Survey"},
          questions: [],
          sections: [],
          factors: [],
          demographics: []
        )
      end
    end

    context "when the survey is not a snapshot engagement survey" do
      let(:exit_survey) { FactoryBot.create(:survey, type: :exit, account_id: account.id) }

      it "raises a NotSnapshotEngagementSurveyError" do
        result = query.call(survey_aggregate_id: exit_survey.aggregate_id)
        expect(result).to be_failure
        expect(result.failure[:status]).to eq(:bad_request)
        expect(result.failure[:message]).to eq("Survey with id '#{exit_survey.aggregate_id}' is not a snapshot engagement survey")
      end
    end

    context "when the survey is closed or archived" do
      let(:archived_survey) { FactoryBot.create(:survey, account_id: account.id, archived: true) }
      let(:closed_survey) { FactoryBot.create(:survey, account_id: account.id, status: :closed) }

      it "raises an InactiveSurveyError for a closed survey" do
        result = query.call(survey_aggregate_id: closed_survey.aggregate_id)
        expect(result).to be_failure
        expect(result.failure[:status]).to eq(:bad_request)
        expect(result.failure[:message]).to eq("Survey with id '#{closed_survey.aggregate_id}' is not active")
      end

      it "raises an InactiveSurveyError for an archived survey" do
        result = query.call(survey_aggregate_id: archived_survey.aggregate_id)
        expect(result).to be_failure
        expect(result.failure[:status]).to eq(:bad_request)
        expect(result.failure[:message]).to eq("Survey with id '#{archived_survey.aggregate_id}' is not active")
      end
    end

    context "when the survey is not found" do
      let(:wrong_survey_id) { SecureRandom.uuid }

      it "returns a failure result with not found status" do
        result = query.call(survey_aggregate_id: wrong_survey_id)

        expect(result).to be_failure
        expect(result.failure).to include(
          status: :not_found,
          message: "Could not find survey with id '#{wrong_survey_id}'"
        )
      end
    end

    context "when the account is not found" do
      let(:survey_with_wrong_account) { FactoryBot.create(:survey, account_id: BSON::ObjectId.new) }

      it "returns a failure result with not found status" do
        result = query.call(survey_aggregate_id: survey_with_wrong_account.aggregate_id)

        expect(result).to be_failure
        expect(result.failure).to include(
          status: :not_found,
          message: "Could not find account for survey with id '#{survey_with_wrong_account.aggregate_id}'"
        )
      end
    end

    context "when data extraction fails for a specific type" do
      before do
        allow(extract_survey_data).to receive(:call).with(survey_aggregate_id: survey.aggregate_id).and_return(Dry::Monads::Failure(:error))
      end

      it "returns a failure result with internal_server_error status and specifies the data type" do
        result = query.call(survey_aggregate_id: survey_aggregate_id)
        expect(result).to be_failure
        expect(result.failure[:status]).to eq(:internal_server_error)
        expect(result.failure[:message]).to eq("Failed to extract survey data for survey with id '#{survey_aggregate_id}'")
      end
    end
  end

  describe "#filter_stqs" do
    let!(:section) { FactoryBot.create(:theme, survey: survey) }
    let!(:active_culture_stq) { FactoryBot.create(:survey_to_question, survey: survey, status: :active, type: :culture) }
    let!(:inactive_culture_stq) { FactoryBot.create(:survey_to_question, survey: survey, status: :inactive, type: :culture) }
    let!(:active_demographic_stq) { FactoryBot.create(:survey_to_question, survey: survey, status: :active, type: :segment) } # Theme is not set, so self_report? would be false (i.e. not a self-select demographic)
    let!(:self_report_demographic_stq) { FactoryBot.create(:survey_to_question, survey: survey, status: :active, type: :segment, capture: true, theme_id: section.id) } # Capture is true and theme is set, so self_report? would be true (i.e. a self-select demographic)
    let!(:inactive_demographic_stq) { FactoryBot.create(:survey_to_question, survey: survey, status: :inactive, type: :segment) }

    subject(:get_survey_data) { described_class.new }

    fit "filters active STQs into filtered_stqs and demographic_stqs, and ignores inactive STQs" do
      filtered_stqs, demographic_stqs = get_survey_data.send(:filter_stqs, survey.stqs)

      expect(filtered_stqs).to contain_exactly(active_culture_stq, self_report_demographic_stq)
      expect(demographic_stqs).to contain_exactly(active_demographic_stq)
    end
  end
end
