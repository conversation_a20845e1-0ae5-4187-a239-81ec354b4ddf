require "rails_helper"

RSpec.describe Participants::Queries::Users::ByIds do
  subject { described_class.new.call(user_aggregate_ids: user_aggregate_ids) }

  let(:account) { FactoryBot.create(:account) }
  let(:aggregate_id) { question.aggregate_id }

  let(:person_1) { FactoryBot.create(:person, account: account) }
  let(:person_2) { FactoryBot.create(:person, account: account) }

  let(:user_aggregate_ids) { [person_1.aggregate_id, person_2.aggregate_id] }

  it "returns a struct with the users ids" do
    expect(subject).to match_array([
      described_class::UserStruct.new(
        aggregate_id: person_1.aggregate_id,
        mongo_id: person_1.id.to_s
      ),
      described_class::UserStruct.new(
        aggregate_id: person_2.aggregate_id,
        mongo_id: person_2.id.to_s
      )
    ])
  end

  context "when users don't exist" do
    let(:user_aggregate_ids) { [SecureRandom.uuid, SecureRandom.uuid] }

    it "returns empty array" do
      expect(subject).to eq []
    end
  end
end
