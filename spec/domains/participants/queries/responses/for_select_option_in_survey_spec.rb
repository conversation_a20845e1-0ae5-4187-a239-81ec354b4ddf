require "rails_helper"

RSpec.describe Participants::Queries::Responses::ForSelectOptionInSurvey do
  subject(:for_select_option_in_survey) do
    described_class.new(feature_flag_query: feature_flag_query).call(
      survey_aggregate_id: survey.aggregate_id,
      question_aggregate_id: demographic_stq.question.aggregate_id,
      select_option_id: select_option_id,
      exclude_user_aggregate_ids: exclude_user_aggregate_ids
    )
  end

  let(:exclude_user_aggregate_ids) { [] }
  let(:select_option_id) { demographic_select_option.id }

  let!(:account) { FactoryBot.create(:account) }
  let!(:survey) { FactoryBot.create(:rich_survey, account: account) }
  let!(:demographic_question) { FactoryBot.create(:demographic_question) }
  let!(:demographic_stq) { FactoryBot.create(:survey_to_question, survey: survey, question: demographic_question) }
  let!(:demographic_select_option) { FactoryBot.create(:select_option, select_question: demographic_question, value: "demographic_select_option") }
  let!(:demographic_select_option_2) { FactoryBot.create(:select_option, select_question: demographic_question, value: "demographic_select_option_2") }

  let(:feature_flag_query) { instance_double(FeatureFlags::Queries::ValueForAccount) }
  let(:experiment) { MongoToPostgresScientist::MtpExperiment }

  let!(:response_1) do
    FactoryBot.create(
      :response,
      user: person_1,
      survey: survey,
      status: :submitted
    )
  end

  let!(:response_2) do
    FactoryBot.create(
      :response,
      user: person_2,
      survey: survey,
      status: :submitted
    )
  end

  let!(:response_3) do
    FactoryBot.create(
      :response,
      user: person_3,
      survey: survey,
      status: :submitted
    )
  end

  let!(:response_4) do
    FactoryBot.create(
      :response,
      user: person_4,
      survey: survey,
      status: :submitted
    )
  end

  let!(:response_5) do
    FactoryBot.create(
      :response,
      user: person_5,
      survey: survey,
      status: :submitted
    )
  end

  let!(:answer_1) { Answer.create!(response: response_1, question: demographic_stq.question, select_option_ids: [demographic_select_option.id]) }
  let!(:answer_2) { Answer.create!(response: response_2, question: demographic_stq.question, select_option_ids: [demographic_select_option.id]) }
  let!(:answer_3) { Answer.create!(response: response_3, question: demographic_stq.question, select_option_ids: [demographic_select_option.id]) }
  let!(:answer_4) { Answer.create!(response: response_4, question: demographic_stq.question, select_option_ids: [demographic_select_option_2.id]) }
  let!(:answer_5) { Answer.create!(response: response_5, question: demographic_stq.question, select_option_ids: [demographic_select_option_2.id]) }

  let!(:person_1) { FactoryBot.create(:person, account: account) }
  let!(:person_2) { FactoryBot.create(:person, account: account) }
  let!(:person_3) { FactoryBot.create(:person, account: account) }
  let!(:person_4) { FactoryBot.create(:person, account: account) }
  let!(:person_5) { FactoryBot.create(:person, account: account) }

  let!(:pg_survey) { SurveyDesign::PgSurvey.create!(aggregate_id: survey.aggregate_id, account_aggregate_id: account.aggregate_id) }
  let!(:pg_response_1) do
    SurveyDesign::PgResponse.create!(
      aggregate_id: response_1.aggregate_id,
      survey_aggregate_id: survey.aggregate_id,
      user_aggregate_id: person_1.aggregate_id,
      status: "submitted"
    )
  end

  let!(:pg_response_2) do
    SurveyDesign::PgResponse.create!(
      aggregate_id: response_2.aggregate_id,
      survey_aggregate_id: survey.aggregate_id,
      user_aggregate_id: person_2.aggregate_id,
      status: "submitted"
    )
  end

  let!(:pg_response_3) do
    SurveyDesign::PgResponse.create!(
      aggregate_id: response_3.aggregate_id,
      survey_aggregate_id: survey.aggregate_id,
      user_aggregate_id: person_3.aggregate_id,
      status: "submitted"
    )
  end

  let!(:pg_response_4) do
    SurveyDesign::PgResponse.create!(
      aggregate_id: response_4.aggregate_id,
      survey_aggregate_id: survey.aggregate_id,
      user_aggregate_id: person_4.aggregate_id,
      status: "submitted"
    )
  end

  let!(:pg_response_5) do
    SurveyDesign::PgResponse.create!(
      aggregate_id: response_5.aggregate_id,
      survey_aggregate_id: survey.aggregate_id,
      user_aggregate_id: person_5.aggregate_id,
      status: "submitted"
    )
  end

  let!(:pg_answer_1) do
    SurveyDesign::PgAnswer.create!(
      aggregate_id: SecureRandom.uuid,
      response_aggregate_id: pg_response_1.aggregate_id,
      account_aggregate_id: account.aggregate_id,
      question_aggregate_id: demographic_stq.question.aggregate_id,
      survey_to_question_aggregate_id: SecureRandom.uuid,
      demographic_value_aggregate_ids: [demographic_select_option.demographic_value_id, SecureRandom.uuid]
    )
  end

  let!(:pg_answer_2) do
    SurveyDesign::PgAnswer.create!(
      aggregate_id: SecureRandom.uuid,
      response_aggregate_id: pg_response_2.aggregate_id,
      account_aggregate_id: account.aggregate_id,
      question_aggregate_id: demographic_stq.question.aggregate_id,
      survey_to_question_aggregate_id: SecureRandom.uuid,
      demographic_value_aggregate_ids: [demographic_select_option.demographic_value_id, SecureRandom.uuid]
    )
  end

  let!(:pg_answer_3) do
    SurveyDesign::PgAnswer.create!(
      aggregate_id: SecureRandom.uuid,
      response_aggregate_id: pg_response_3.aggregate_id,
      account_aggregate_id: account.aggregate_id,
      question_aggregate_id: demographic_stq.question.aggregate_id,
      survey_to_question_aggregate_id: SecureRandom.uuid,
      demographic_value_aggregate_ids: [demographic_select_option.demographic_value_id, SecureRandom.uuid]
    )
  end

  let!(:pg_answer_4) do
    SurveyDesign::PgAnswer.create!(
      aggregate_id: SecureRandom.uuid,
      response_aggregate_id: pg_response_4.aggregate_id,
      account_aggregate_id: account.aggregate_id,
      question_aggregate_id: demographic_stq.question.aggregate_id,
      survey_to_question_aggregate_id: SecureRandom.uuid,
      demographic_value_aggregate_ids: [demographic_select_option_2.demographic_value_id, SecureRandom.uuid]
    )
  end

  let!(:pg_answer_5) do
    SurveyDesign::PgAnswer.create!(
      aggregate_id: SecureRandom.uuid,
      response_aggregate_id: pg_response_5.aggregate_id,
      account_aggregate_id: account.aggregate_id,
      question_aggregate_id: demographic_stq.question.aggregate_id,
      survey_to_question_aggregate_id: SecureRandom.uuid,
      demographic_value_aggregate_ids: [demographic_select_option_2.demographic_value_id, SecureRandom.uuid]
    )
  end

  let!(:pg_demographic_question) do
    SurveyDesign::PgQuestion.create!(
      aggregate_id: demographic_question.aggregate_id,
      account_aggregate_id: account.aggregate_id
    )
  end

  let!(:pg_demographic_select_option) do
    SurveyDesign::PgSelectOption.create!(
      aggregate_id: demographic_select_option.demographic_value_id,
      account_aggregate_id: account.aggregate_id,
      question_aggregate_id: demographic_stq.question.aggregate_id
    )
  end

  let!(:pg_demographic_select_option_2) do
    SurveyDesign::PgSelectOption.create!(
      aggregate_id: demographic_select_option_2.demographic_value_id,
      account_aggregate_id: account.aggregate_id,
      question_aggregate_id: demographic_stq.question.aggregate_id
    )
  end

  before do
    Domains::MurmurGeneral.database(:general_db)[:select_option_id_mappings].insert(
      aggregate_id: pg_demographic_select_option.aggregate_id,
      mongo_id: demographic_select_option.id.to_s,
      question_aggregate_id: demographic_stq.question.aggregate_id,
      question_mongo_id: demographic_stq.question.id.to_s
    )

    Domains::MurmurGeneral.database(:general_db)[:select_option_id_mappings].insert(
      aggregate_id: pg_demographic_select_option_2.aggregate_id,
      mongo_id: demographic_select_option_2.id.to_s,
      question_aggregate_id: demographic_stq.question.aggregate_id,
      question_mongo_id: demographic_stq.question.id.to_s
    )
  end

  describe "#call" do
    context "given we haven't started an experiment" do
      before do
        allow(feature_flag_query).to receive(:call).with(
          account_aggregate_id: account.aggregate_id,
          flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_START,
          fallback_value: false
        ).and_return(false)

        allow(feature_flag_query).to receive(:call).with(
          account_aggregate_id: account.aggregate_id,
          flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_FINISH,
          fallback_value: false
        ).and_return(false)
      end

      it "doesn't call the experiment" do
        expect_any_instance_of(experiment).to_not receive(:use)
        expect_any_instance_of(experiment).to_not receive(:try)
        expect_any_instance_of(experiment).to_not receive(:run)
        for_select_option_in_survey
      end

      it "returns responses with matching demographics" do
        expect(for_select_option_in_survey).to match_array([
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_1.aggregate_id,
            user_aggregate_id: person_1.aggregate_id,
            user_mongo_id: person_1.id.to_s
          ),
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_2.aggregate_id,
            user_aggregate_id: person_2.aggregate_id,
            user_mongo_id: person_2.id.to_s
          ),
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_3.aggregate_id,
            user_aggregate_id: person_3.aggregate_id,
            user_mongo_id: person_3.id.to_s
          )
        ])
      end

      context "given no select_option_id provided" do
        let(:select_option_id) { nil }

        let!(:response_6) do
          FactoryBot.create(
            :response,
            user: person_5,
            survey: survey,
            status: :submitted
          )
        end

        it "returns the response with answers that don't have any of the select_options in the question" do
          expect(for_select_option_in_survey).to match_array([
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_6.aggregate_id,
              user_aggregate_id: person_5.aggregate_id,
              user_mongo_id: person_5.id.to_s
            )
          ])
        end
      end

      context "given we exclude user ids" do
        let(:exclude_user_aggregate_ids) { [person_1.aggregate_id] }

        it "excludes responses with those users" do
          expect(for_select_option_in_survey).to match_array([
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_2.aggregate_id,
              user_aggregate_id: person_2.aggregate_id,
              user_mongo_id: person_2.id.to_s
            ),
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_3.aggregate_id,
              user_aggregate_id: person_3.aggregate_id,
              user_mongo_id: person_3.id.to_s
            )
          ])
        end
      end
    end

    context "given we've started the experiment" do
      before do
        allow(feature_flag_query).to receive(:call).with(
          account_aggregate_id: account.aggregate_id,
          flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_START,
          fallback_value: false
        ).and_return(true)

        allow(feature_flag_query).to receive(:call).with(
          account_aggregate_id: account.aggregate_id,
          flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_FINISH,
          fallback_value: false
        ).and_return(false)
      end

      context "and experiment matched" do
        it "returns responses with matching demographics" do
          expect(for_select_option_in_survey).to match_array([
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_1.aggregate_id,
              user_aggregate_id: person_1.aggregate_id,
              user_mongo_id: person_1.id.to_s
            ),
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_2.aggregate_id,
              user_aggregate_id: person_2.aggregate_id,
              user_mongo_id: person_2.id.to_s
            ),
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_3.aggregate_id,
              user_aggregate_id: person_3.aggregate_id,
              user_mongo_id: person_3.id.to_s
            )
          ])
        end

        it "calls the experiment" do
          expect_any_instance_of(experiment).to receive(:use)
          expect_any_instance_of(experiment).to receive(:try)
          expect_any_instance_of(experiment).to receive(:run)
          for_select_option_in_survey
        end

        it "logs experiment matched and not mismatched" do
          expect(Rails.logger).to receive(:info).with("event=scientist.experiment.match name=mongo_to_postgres.experiment operation=Participants::Queries::Responses::ForSelectOptionInSurvey")
          expect(Rails.logger).not_to receive(:error)
          for_select_option_in_survey
        end

        context "given no select_option_id provided" do
          let(:select_option_id) { nil }

          let!(:response_6) do
            FactoryBot.create(
              :response,
              user: person_5,
              survey: survey,
              status: :submitted
            )
          end

          let(:pg_response_6) do
            SurveyDesign::PgResponse.create!(
              aggregate_id: response_6.aggregate_id,
              survey_aggregate_id: survey.aggregate_id,
              user_aggregate_id: person_5.aggregate_id,
              status: "submitted"
            )
          end

          let!(:pg_answer_6) do
            SurveyDesign::PgAnswer.create!(
              aggregate_id: SecureRandom.uuid,
              response_aggregate_id: pg_response_6.aggregate_id,
              account_aggregate_id: account.aggregate_id,
              question_aggregate_id: demographic_stq.question.aggregate_id,
              survey_to_question_aggregate_id: SecureRandom.uuid,
              demographic_value_aggregate_ids: []
            )
          end

          it "returns the response with answers that don't have any of the select_options in the question" do
            expect(for_select_option_in_survey).to match_array([
              Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
                aggregate_id: pg_response_6.aggregate_id,
                user_aggregate_id: person_5.aggregate_id,
                user_mongo_id: person_5.id.to_s
              )
            ])
          end

          it "logs experiment matched and not mismatched" do
            expect(Rails.logger).to receive(:info).with("event=scientist.experiment.match name=mongo_to_postgres.experiment operation=Participants::Queries::Responses::ForSelectOptionInSurvey")
            expect(Rails.logger).not_to receive(:error)
            for_select_option_in_survey
          end
        end

        context "given we exclude user ids" do
          let(:exclude_user_aggregate_ids) { [person_1.aggregate_id] }

          it "excludes responses with those users" do
            expect(for_select_option_in_survey).to match_array([
              Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
                aggregate_id: response_2.aggregate_id,
                user_aggregate_id: person_2.aggregate_id,
                user_mongo_id: person_2.id.to_s
              ),
              Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
                aggregate_id: response_3.aggregate_id,
                user_aggregate_id: person_3.aggregate_id,
                user_mongo_id: person_3.id.to_s
              )
            ])
          end

          it "logs experiment matched and not mismatched" do
            expect(Rails.logger).to receive(:info).with("event=scientist.experiment.match name=mongo_to_postgres.experiment operation=Participants::Queries::Responses::ForSelectOptionInSurvey")
            expect(Rails.logger).not_to receive(:error)
            for_select_option_in_survey
          end
        end
      end

      context "and experiment mismatched" do
        let(:person_6) { FactoryBot.create(:person, account: account) }

        before do
          pg_response_1.update!(user_aggregate_id: person_6.aggregate_id)
        end

        it "stills returns responses with matching demographics from mongo" do
          expect(for_select_option_in_survey).to match_array([
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_1.aggregate_id,
              user_aggregate_id: person_1.aggregate_id,
              user_mongo_id: person_1.id.to_s
            ),
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_2.aggregate_id,
              user_aggregate_id: person_2.aggregate_id,
              user_mongo_id: person_2.id.to_s
            ),
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_3.aggregate_id,
              user_aggregate_id: person_3.aggregate_id,
              user_mongo_id: person_3.id.to_s
            )
          ])
        end

        it "calls the experiment" do
          expect_any_instance_of(experiment).to receive(:use)
          expect_any_instance_of(experiment).to receive(:try)
          expect_any_instance_of(experiment).to receive(:run)
          for_select_option_in_survey
        end

        it "logs experiment mismatched" do
          expect(Rails.logger).to receive(:error).once
          expect(Rails.logger).to receive(:error).with("event=scientist.experiment.mismatch name=mongo_to_postgres.experiment operation=Participants::Queries::Responses::ForSelectOptionInSurvey")

          expect(Rails.logger).not_to receive(:info)
          for_select_option_in_survey
        end
      end
    end

    context "given we finished experiment" do
      before do
        allow(feature_flag_query).to receive(:call).with(
          account_aggregate_id: account.aggregate_id,
          flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_START,
          fallback_value: false
        ).and_return(true)

        allow(feature_flag_query).to receive(:call).with(
          account_aggregate_id: account.aggregate_id,
          flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_FINISH,
          fallback_value: false
        ).and_return(true)
      end

      it "doesn't call the experiment" do
        expect_any_instance_of(experiment).to_not receive(:use)
        expect_any_instance_of(experiment).to_not receive(:try)
        expect_any_instance_of(experiment).to_not receive(:run)
        for_select_option_in_survey
      end

      it "returns responses with matching demographics" do
        expect(for_select_option_in_survey).to match_array([
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_1.aggregate_id,
            user_aggregate_id: person_1.aggregate_id,
            user_mongo_id: person_1.id.to_s
          ),
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_2.aggregate_id,
            user_aggregate_id: person_2.aggregate_id,
            user_mongo_id: person_2.id.to_s
          ),
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_3.aggregate_id,
            user_aggregate_id: person_3.aggregate_id,
            user_mongo_id: person_3.id.to_s
          )
        ])
      end

      context "given no select_option_id provided" do
        let(:select_option_id) { nil }

        let(:pg_response_6) do
          SurveyDesign::PgResponse.create!(
            aggregate_id: SecureRandom.uuid,
            survey_aggregate_id: survey.aggregate_id,
            user_aggregate_id: person_5.aggregate_id,
            status: "submitted"
          )
        end

        let!(:pg_answer_6) do
          SurveyDesign::PgAnswer.create!(
            aggregate_id: SecureRandom.uuid,
            response_aggregate_id: pg_response_6.aggregate_id,
            account_aggregate_id: account.aggregate_id,
            question_aggregate_id: demographic_stq.question.aggregate_id,
            survey_to_question_aggregate_id: SecureRandom.uuid,
            demographic_value_aggregate_ids: []
          )
        end

        it "returns the response with answers that don't have any of the select_options in the question" do
          expect(for_select_option_in_survey).to match_array([
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: pg_response_6.aggregate_id,
              user_aggregate_id: person_5.aggregate_id,
              user_mongo_id: person_5.id.to_s
            )
          ])
        end
      end

      context "given we exclude user ids" do
        let(:exclude_user_aggregate_ids) { [person_1.aggregate_id] }

        it "excludes responses with those users" do
          expect(for_select_option_in_survey).to match_array([
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_2.aggregate_id,
              user_aggregate_id: person_2.aggregate_id,
              user_mongo_id: person_2.id.to_s
            ),
            Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
              aggregate_id: response_3.aggregate_id,
              user_aggregate_id: person_3.aggregate_id,
              user_mongo_id: person_3.id.to_s
            )
          ])
        end
      end
    end

    context "given experiment fails" do
      before do
        allow_any_instance_of(experiment).to receive(:run).and_raise("error")

        allow(feature_flag_query).to receive(:call).with(
          account_aggregate_id: account.aggregate_id,
          flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_START,
          fallback_value: false
        ).and_return(true)

        allow(feature_flag_query).to receive(:call).with(
          account_aggregate_id: account.aggregate_id,
          flag_name: FeatureFlags::Flags::MURMUR_DATA_MIGRATION_EXPERIMENT_SURVEY_PARTICIPANTS_READ_FROM_POSTGRES_FINISH,
          fallback_value: false
        ).and_return(false)
      end

      it "logs the error" do
        expect(Rails.logger).to receive(:error).with("operation=Participants::Queries::Responses::ForSelectOptionInSurvey error=error=error")
        for_select_option_in_survey
      end

      it "stills returns responses with matching demographics from mongo" do
        expect(for_select_option_in_survey).to match_array([
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_1.aggregate_id,
            user_aggregate_id: person_1.aggregate_id,
            user_mongo_id: person_1.id.to_s
          ),
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_2.aggregate_id,
            user_aggregate_id: person_2.aggregate_id,
            user_mongo_id: person_2.id.to_s
          ),
          Participants::Queries::Responses::ForSelectOptionInSurvey::ResponseStruct.new(
            aggregate_id: response_3.aggregate_id,
            user_aggregate_id: person_3.aggregate_id,
            user_mongo_id: person_3.id.to_s
          )
        ])
      end
    end
  end
end
