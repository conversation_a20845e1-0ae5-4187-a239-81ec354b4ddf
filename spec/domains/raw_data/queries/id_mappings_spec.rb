require "rails_helper"

RSpec.describe RawData::Queries::IdMappings do
  subject(:id_mappings) { described_class.new }

  let(:aggregate_id_1) { SecureRandom.uuid }
  let(:aggregate_id_2) { SecureRandom.uuid }
  let(:aggregate_id_3) { SecureRandom.uuid }
  let(:mongo_id_1) { BSON::ObjectId.new.to_s }
  let(:mongo_id_2) { BSON::ObjectId.new.to_s }
  let(:mongo_id_3) { BSON::ObjectId.new.to_s }

  let(:aggregate_ids) { [aggregate_id_1, aggregate_id_3] }
  let(:mongo_ids) { [mongo_id_1, mongo_id_3] }
  let(:mapping_collections) { [:survey, :response, :factor, :question, :section] }

  before do
    mapping_tables = [
      :survey_id_mappings,
      :response_id_mappings,
      :factor_id_mappings,
      :question_id_mappings,
      :section_id_mappings
    ]

    mapping_tables.each do |mapping_table|
      Domains::MurmurGeneral.database(:general_db)[mapping_table].multi_insert([
        {aggregate_id: aggregate_id_1, mongo_id: mongo_id_1},
        {aggregate_id: aggregate_id_2, mongo_id: mongo_id_2},
        {aggregate_id: aggregate_id_3, mongo_id: mongo_id_3}
      ])
    end
  end

  describe "#from_aggregate_ids" do
    it "returns the correct mongo_ids" do
      mapping_collections.each do |collection|
        result = id_mappings.from_aggregate_ids(collection: collection, aggregate_ids: aggregate_ids)
        expect(result).to eq({aggregate_id_1 => mongo_id_1, aggregate_id_3 => mongo_id_3})
      end
    end

    context "when the collection is invalid" do
      let(:collection) { :invalid_collection }

      it "raises an ArgumentError" do
        expect { id_mappings.from_aggregate_ids(collection: collection, aggregate_ids: aggregate_ids) }.to raise_error(ArgumentError, "Invalid collection: invalid_collection")
      end
    end

    context "when the collection is select_option" do
      let(:collection) { :select_option }

      it "raises an ArgumentError" do
        expect { id_mappings.from_aggregate_ids(collection: collection, aggregate_ids: aggregate_ids) }.to raise_error(ArgumentError, "Select options mapping currently not supported due to question scoping")
      end
    end
  end

  describe "#from_mongo_ids" do
    it "returns the correct aggregate_ids" do
      mapping_collections.each do |collection|
        result = id_mappings.from_mongo_ids(collection: collection, mongo_ids: mongo_ids)
        expect(result).to eq({mongo_id_1 => aggregate_id_1, mongo_id_3 => aggregate_id_3})
      end
    end

    context "when the collection is invalid" do
      let(:collection) { :invalid_collection }

      it "raises an ArgumentError" do
        expect { id_mappings.from_mongo_ids(collection: collection, mongo_ids: mongo_ids) }.to raise_error(ArgumentError, "Invalid collection: invalid_collection")
      end
    end

    context "when the collection is select_option" do
      let(:collection) { :select_option }

      it "raises an ArgumentError" do
        expect { id_mappings.from_mongo_ids(collection: collection, mongo_ids: mongo_ids) }.to raise_error(ArgumentError, "Select options mapping currently not supported due to question scoping")
      end
    end
  end
end
