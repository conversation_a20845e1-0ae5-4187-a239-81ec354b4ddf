require "rails_helper"
require "spec_access_helper"

RSpec.describe Admin::MasqueradesController do
  let(:fusion_auth_search_entity_type) { instance_double(Authentication::Commands::FusionAuthSearchEntityType) }
  let(:fusion_auth_create_entity) { instance_double(Authentication::Commands::FusionAuthCreateEntity) }
  let(:fusion_auth_delete_entity) { instance_double(Authentication::Commands::FusionAuthDeleteEntity) }
  let(:entity_type_id) { "type-123" }
  let(:jwt_sid) { "session-456" }
  let(:jwt_user_id) { "user-789" }
  let(:jwt_real_user_id) { "real-012" }
  let(:jwt_account_id) { "account-345" }

  let(:ca_account) { FactoryBot.create(:ca_account) }
  let(:a_ca_superuser) { FactoryBot.create(:superuser_with_regions, account: ca_account) }
  let(:another_ca_superuser) { FactoryBot.create(:superuser, account: ca_account) }
  let(:a_ca_standard_user) { FactoryBot.create(:user, account: ca_account) }
  let(:an_account) { FactoryBot.create(:account) }
  let(:a_privileged_account) { FactoryBot.create(:account, privilege: true) }
  let(:a_standard_user) { FactoryBot.create(:user, account: an_account) }
  let(:a_standard_user_on_privileged_account) { FactoryBot.create(:user, account: a_privileged_account) }
  let(:region) { Region.all.first }
  let(:other_region) { Region.all.last }

  before do
    # Generate standard masquerade tokens for these users to allow us to test against what's returned
    [another_ca_superuser, a_standard_user, a_standard_user_on_privileged_account].each do |u|
      u.generate_masquerade_token
      u.save!
    end
    sign_in logged_in_user if logged_in_user
  end

  describe "#new" do
    context "as an annonymous user" do
      let(:logged_in_user) { nil }
      it "blocks access to the masquerade page" do
        get :new
        expect(response).to redirect_to new_user_session_path(redirect: request.path)
        expect(session[:masquerade_user]).to be_nil

        post :create, params: {identifier: a_standard_user.email}
        expect(response).to redirect_to new_user_session_path(redirect: request.path)
        expect(session[:masquerade_user]).to be_nil
      end
    end

    context "as a standard user" do
      let(:logged_in_user) { a_standard_user }

      it "blocks access to the masquerade page" do
        expect(Rails.logger).to receive(:warn).with("User \"#{logged_in_user}\" was blocked trying to access a superuser function.")
        get :new
        expect(response).to be_forbidden
        expect(session[:masquerade_user]).to be_nil
      end

      it "detects someone without permissions attempting to hack the masquerading functions even with correct salts, and invalidate their session" do
        session[:masquerade_user] = [another_ca_superuser.id.to_s, another_ca_superuser.masquerade_token]
        expect(Rails.logger).to receive(:error).with("Resetting session because #{logged_in_user} invalidly attempted to masquerade as user [\"#{another_ca_superuser.id}\", \"#{another_ca_superuser.masquerade_token}\"]")
        get :new
        expect(response).to be_forbidden
        expect(session[:masquerade_user]).to be_nil
      end
    end

    context "as a Culture Amp employee without superuser access" do
      let(:logged_in_user) { a_ca_standard_user }

      it "blocks access to the masquerade page" do
        expect(Rails.logger).to receive(:warn).with("User \"#{a_ca_standard_user}\" was blocked trying to access a superuser function.")
        get :new
        expect(response).to be_forbidden
        expect(session[:masquerade_user]).to be_nil
      end
    end

    context "as a superuser" do
      let(:logged_in_user) { a_ca_superuser }

      it "detects someone attempting to hack the masquerading functions and invalidate their session" do
        session[:masquerade_user] = [a_standard_user.id.to_s, "A randomly attempted token"]
        expect(Rails.logger).to receive(:error).with("Resetting session because #{a_ca_superuser} invalidly attempted to masquerade as user [\"#{a_standard_user.id}\", \"A randomly attempted token\"]")
        get :new
        expect(response).to be_forbidden
        expect(session[:masquerade_user]).to be_nil
      end

      it "renders the masquerade screen" do
        get :new
        expect(response).to render_template("new")
        expect(session[:masquerade_user]).to be_nil
      end

      context "when masquerading as another user" do
        before do
          session[:masquerade_user] = [a_standard_user.id.to_s, a_standard_user.masquerade_token]
        end

        it "is forbidden to masquerade again without stop masquerading first" do
          get :new
          expect(response).to be_forbidden
        end
      end

      context "when superuser tries to masquerade as another superuser via hack" do
        before do
          session[:masquerade_user] = [another_ca_superuser.id.to_s, another_ca_superuser.masquerade_token]
        end

        it "is forbidden and resets the session" do
          expect(Rails.logger).to receive(:error).with("Resetting session because #{a_ca_superuser} invalidly attempted to masquerade as user [\"#{another_ca_superuser.id}\", \"#{another_ca_superuser.masquerade_token}\"]")
          get :new
          expect(response).to be_forbidden
          expect(session[:masquerade_user]).to be_nil
        end
      end
    end
  end

  describe "#create" do
    shared_examples "creates fusionauth entity" do
      context "when fusionauth_jwt_post_signin flag is enabled" do
        # Real JWT configuration using the test setup - use the actual logged-in user's aggregate_id
        let(:payload) { {"effectiveUserId" => logged_in_user.aggregate_id, "accountId" => logged_in_user.account.aggregate_id, "realUserId" => logged_in_user.aggregate_id, "sid" => jwt_sid} }
        let(:web_gateway_equivalent_key) { Rails.application.config.test_jwt_private_key }
        let(:token) { JWT.encode(payload, web_gateway_equivalent_key, "RS256") }
        let(:headers) { {"X-CA-FA-Authorization" => "Bearer #{token}"} }

        before do
          # Set JWT headers for authentication
          @request.headers.merge!(headers)

          allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
          allow(Authentication::Commands::FusionAuthSearchEntityType).to receive(:new).and_return(fusion_auth_search_entity_type)
          allow(Authentication::Commands::FusionAuthCreateEntity).to receive(:new).and_return(fusion_auth_create_entity)
        end

        context "when entity type search succeeds" do
          before do
            allow(fusion_auth_search_entity_type).to receive(:call)
              .with(name: "User Session")
              .and_return(Dry::Monads::Success(entity_type_id))
          end

          it "creates the entity" do
            expect(fusion_auth_create_entity).to receive(:call).with(
              entity_type_id: entity_type_id,
              session_id: jwt_sid,
              masquerade_effective_user_id: masquerade_user.aggregate_id,
              real_user_id: logged_in_user.aggregate_id,
              account_id: logged_in_user.account.aggregate_id,
              masquerade_account_id: masquerade_user.account.aggregate_id
            )

            subject
          end

          it "sets the JWT refresh header" do
            allow(fusion_auth_create_entity).to receive(:call).and_return(Dry::Monads::Success(true))
            subject
            expect(response.headers["X-CA-FA-JWT-Refresh"]).to be true
          end

          it "does not call masquerade_as_user!" do
            allow(fusion_auth_create_entity).to receive(:call).and_return(Dry::Monads::Success(true))
            expect_any_instance_of(Admin::MasqueradesController).not_to receive(:masquerade_as_user!)
            subject
          end

          it "does not set masquerade_end_time in session" do
            allow(fusion_auth_create_entity).to receive(:call).and_return(Dry::Monads::Success(true))
            subject
            expect(session[:masquerade_end_time]).to be_nil
          end
        end

        context "when entity type search fails" do
          before do
            allow(fusion_auth_search_entity_type).to receive(:call)
              .with(name: "User Session")
              .and_return(Dry::Monads::Failure("error"))
            allow(Rails.logger).to receive(:error)
          end

          it "logs the error" do
            expect(Rails.logger).to receive(:error).with("Failed to find User Session entity type")
            expect_any_instance_of(Admin::MasqueradesController).to receive(:masquerade_as_user!).never
            subject
            expect(response.code).to eq("500")
            expect(response).to render_template("site/500.html.haml")
          end

          it "does not create the entity" do
            expect(fusion_auth_create_entity).not_to receive(:call)
            expect_any_instance_of(Admin::MasqueradesController).to receive(:masquerade_as_user!).never
            subject
          end
        end

        context "when entity creation raises an error" do
          before do
            allow(fusion_auth_search_entity_type).to receive(:call)
              .with(name: "User Session")
              .and_return(Dry::Monads::Success(entity_type_id))
            allow(fusion_auth_create_entity).to receive(:call).and_raise("Error")
            allow(Rails.logger).to receive(:error)
            allow(Sentry).to receive(:capture_exception)
          end

          it "logs the error" do
            expect(Rails.logger).to receive(:error).with(/Failed to create FusionAuth entity for masquerade session/)
            expect(Sentry).to receive(:capture_exception)
            expect_any_instance_of(Admin::MasqueradesController).to receive(:masquerade_as_user!).never
            subject
            expect(response.code).to eq("500")
            expect(response).to render_template("site/500.html.haml")
          end
        end
      end

      context "when fusionauth_jwt_post_signin flag is disabled" do
        before do
          allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)
        end

        it "does not attempt to create entity" do
          expect(Authentication::Commands::FusionAuthSearchEntityType).not_to receive(:new)
          expect(Authentication::Commands::FusionAuthCreateEntity).not_to receive(:new)

          subject
        end

        it "calls masquerade_as_user! with the masquerade user" do
          expect(controller).to receive(:masquerade_as_user!).with(masquerade_user)
          subject
        end

        it "sets masquerade_end_time in session" do
          allow(Time).to receive(:now).and_return(Time.parse("2023-01-01 12:00:00"))
          subject
          expect(session[:masquerade_end_time]).to eq(12.hours.from_now)
        end

        it "logs the masquerade action" do
          expect(Rails.logger).to receive(:info).with("User \"#{logged_in_user}\" has started masquerading as \"#{masquerade_user}\".")
          subject
        end

        it "creates a RestrictedActionLog entry" do
          expect {
            subject
          }.to change(RestrictedActionLog, :count).by(1)
        end
      end
    end

    shared_examples "sets user-data cookies" do
      it "sets both regional and basic user-data cookies" do
        expected_user_info = {
          locale: masquerade_user.locale,
          employee_aggregate_id: masquerade_user.aggregate_id,
          account_aggregate_id: masquerade_user.account.aggregate_id
        }.to_json

        encoded_info = Base64.strict_encode64(expected_user_info)
        encoded_info_without_padding = ::Base64.urlsafe_encode64(expected_user_info, padding: false)

        subject
        expect(response.cookies["cultureamp.user-data"]).to eq(encoded_info)
        expect(response.cookies["cultureamp.#{controller.send(:env_name)}.user-data"]).to eq(encoded_info_without_padding)
      end
    end

    context "as a standard user" do
      let(:logged_in_user) { FactoryBot.create(:user, account: a_privileged_account) }

      it "blocks access to the masquerade function" do
        expect(Rails.logger).to receive(:warn).with("User \"#{logged_in_user}\" was blocked trying to access a superuser function.")
        post :create, params: {identifier: a_standard_user.email}
        expect(response).to be_forbidden
        expect(session[:masquerade_user]).to be_nil
      end
    end

    context "as a Culture Amp employee without superuser access" do
      let(:logged_in_user) { a_ca_standard_user }

      it "blocks access to the masquerade function" do
        expect(Rails.logger).to receive(:warn).with("User \"#{a_ca_standard_user}\" was blocked trying to access a superuser function.")
        post :create, params: {identifier: a_standard_user.email}
        expect(response).to be_forbidden
        expect(session[:masquerade_user]).to be_nil
      end
    end

    context "as a Culture Amp superuser" do
      let(:reason) { "Because I can!" }
      let(:logged_in_user) { a_ca_superuser }
      let(:masquerade_user) { a_standard_user_on_privileged_account }

      context "that has access to a privileged account" do
        let(:a_ca_superuser) { SpecAccessHelper.construct_superuser_for_privileged_account(a_privileged_account) }
        before do
          allow(Rails.logger).to receive(:info)
        end

        context "when masquerading by email" do
          subject { post :create, params: {reason: reason, identifier: masquerade_user.email} }

          include_examples "creates fusionauth entity"
          include_examples "sets user-data cookies"

          it "I can masquerade as a user found by email" do
            expect(Rails.logger).to receive(:info).with("User \"#{a_ca_superuser}\" has started masquerading as \"#{masquerade_user}\".")
            subject
            expect(response).to redirect_to surveys_path
            expect(session[:masquerade_user]).to eq([masquerade_user.id.to_s, masquerade_user.masquerade_token])
          end
        end

        context "when masquerading by ObjectId" do
          subject { post :create, params: {reason: reason, identifier: masquerade_user.id} }

          include_examples "creates fusionauth entity"
          include_examples "sets user-data cookies"

          it "I can masquerade as a user found by ObjectId" do
            expect(Rails.logger).to receive(:info).with("User \"#{a_ca_superuser}\" has started masquerading as \"#{masquerade_user}\".")
            subject
            expect(response).to redirect_to surveys_path
            expect(session[:masquerade_user]).to eq([masquerade_user.id.to_s, masquerade_user.masquerade_token])
          end
        end

        it "I can masquerade as a user found by closest name substring match (Feeling Lucky)" do
          expect(Rails.logger).to receive(:info).with("User \"#{a_ca_superuser}\" has started masquerading as \"#{masquerade_user}\".")
          name_substring = masquerade_user.name[1..-1] #  get all characters but the first to get a substring that will match. Eg: "ichael Jordan"
          post :create, params: {reason: reason, name: name_substring}
          expect(response).to redirect_to surveys_path
          expect(session[:masquerade_user]).to eq([masquerade_user.id.to_s, masquerade_user.masquerade_token])
        end

        it "I can masquerade as a user found by subdomain and employee_id" do
          expect(Rails.logger).to receive(:info).with("User \"#{a_ca_superuser}\" has started masquerading as \"#{masquerade_user}\".")
          post :create, params: {reason: reason, subdomain: a_privileged_account.subdomain, employee_id: masquerade_user.employee_id}
          expect(response).to redirect_to surveys_path
          expect(session[:masquerade_user]).to eq([masquerade_user.id.to_s, masquerade_user.masquerade_token])
        end

        it "I can masquerade as a user found by subdomain and email" do
          expect(Rails.logger).to receive(:info).with("User \"#{a_ca_superuser}\" has started masquerading as \"#{masquerade_user}\".")
          post :create, params: {reason: reason, subdomain: a_privileged_account.subdomain, identifier: masquerade_user.email}
          expect(response).to redirect_to surveys_path
          expect(session[:masquerade_user]).to eq([masquerade_user.id.to_s, masquerade_user.masquerade_token])
        end

        it "redirects to the masquerade page if the user is not found" do
          bad_identifier = "blah blah"
          post :create, params: {reason: reason, identifier: bad_identifier}
          expect(response).to render_template("new")
          expect(assigns(:identifier)).to eq(bad_identifier)
          expect(flash[:error]).to eq("Sorry, couldn't find a user with email or ObjectId '#{bad_identifier}'")
          expect(session[:masquerade_user]).to be_nil
        end

        it "redirects to the masquerade page if the multiple users are found with identifier" do
          non_unique_identifier = a_standard_user.email
          duplicate_user = FactoryBot.create(:user, account: ca_account)
          duplicate_user.email = non_unique_identifier
          duplicate_user.save!(validate: false)
          post :create, params: {reason: reason, identifier: non_unique_identifier}
          expect(response).to render_template("new")
          expect(assigns(:identifier)).to eq(non_unique_identifier)
          expect(flash[:error]).to eq("Sorry, there are multiple users with email '#{non_unique_identifier}'. Please provide a subdomain.")
          expect(session[:masquerade_user]).to be_nil
        end

        it "redirects to the masquerade page if no users are found using the Feeling Lucky search" do
          bad_name = "blah blah"
          post :create, params: {reason: reason, name: bad_name}
          expect(response).to render_template("new")
          expect(assigns(:name)).to eq(bad_name)
          expect(flash[:error]).to eq("Sorry, couldn't find a user with a name that contains '#{bad_name}'")
          expect(session[:masquerade_user]).to be_nil
        end

        it "redirects to the masquerade page if no users are found searching by subdomain and employee id" do
          bad_employee_id = "bad employee id"
          bad_subdomain = "a bad subdomain"
          post :create, params: {reason: reason, subdomain: bad_subdomain, employee_id: bad_employee_id}

          expect(response).to render_template("new")
          expect(assigns(:employee_id)).to eq(bad_employee_id)
          expect(assigns(:subdomain)).to eq(bad_subdomain)
          expect(flash[:error]).to eq("Sorry, couldn't find a user with subdomain '#{bad_subdomain}' and employee_id '#{bad_employee_id}'")
          expect(session[:masquerade_user]).to be_nil
        end

        it "redirects to the masquerade page if no users are found searching by subdomain and email or ObjectId" do
          bad_identifier = "bad email"
          bad_subdomain = "a bad subdomain"
          post :create, params: {reason: reason, subdomain: bad_subdomain, identifier: bad_identifier}

          expect(response).to render_template("new")
          expect(assigns(:identifier)).to eq(bad_identifier)
          expect(assigns(:subdomain)).to eq(bad_subdomain)
          expect(flash[:error]).to eq("Sorry, couldn't find a user with subdomain '#{bad_subdomain}' and email or ObjectId '#{bad_identifier}'")
          expect(session[:masquerade_user]).to be_nil
        end

        it "does not let a user masquerade as a deleted user" do
          user = FactoryBot.create(:user, account: a_privileged_account)
          user.archive!
          post :create, params: {reason: reason, identifier: user.id}
          expect(response).to render_template("new")
          expect(flash[:error]).to eq("Sorry, you cannot masquerade as a deleted user.")
          expect(session[:masquerade_user]).to be_nil
        end

        it "requires a reason for the masquerade action" do
          post :create, params: {identifier: masquerade_user.email}
          expect(response).to render_template("new")
          expect(flash[:error]).to eq("Sorry, you cannot masquerade without providing a reason.")
          expect(session[:masquerade_user]).to be_nil
        end

        it "persists reason to splunk" do
          post :create, params: {reason: reason, identifier: masquerade_user.email}
          expect(response).to redirect_to surveys_path
          expect(session[:masquerade_user]).to eq([masquerade_user.id.to_s, masquerade_user.masquerade_token])

          expect(RestrictedActionLog.where(camper_aggregate_id: a_ca_superuser.aggregate_id).count).to eq 1
        end

        context "when trying to masquerade as another cultureamp user without cultureamp restricted grant" do
          before do
            post :create, params: {reason: reason, identifier: a_ca_standard_user.id}
          end

          it "is forbidden and resets the session" do
            expect(flash[:error]).to eq("Sorry, you don't have the required privileges to masquerade as a user from this account.")
            expect(session[:masquerade_user]).to be_nil
          end
        end

        context "when trying to masquerade as another cultureamp user with cultureamp restricted grant" do
          let(:a_ca_superuser) { SpecAccessHelper.construct_superuser_for_privileged_account(ca_account) }
          before do
            post :create, params: {reason: reason, identifier: a_ca_standard_user.id}
          end

          it "is forbidden and resets the session" do
            expect(flash[:error]).to eq("Sorry, you cannot masquerade as another user in the same account.")
            expect(session[:masquerade_user]).to be_nil
          end
        end
      end

      context "when trying to masquerade as a cultureamp user with cultureamp account admin permission" do
        let(:reason) { "Because I can!" }
        let(:logged_in_user) { a_ca_superuser }

        before do
          another_ca_superuser.enable_account_admin_access!(ca_account, overall_admin: true)
          post :create, params: {reason: reason, identifier: a_ca_standard_user.id}
        end

        it "is forbidden and resets the session" do
          expect(flash[:error]).to eq("Sorry, you don't have the required privileges to masquerade as a user from this account.")
          expect(session[:masquerade_user]).to be_nil
        end
      end

      context "that is in same region as non-privileged account" do
        before do
          an_account.region = region
          an_account.save!
          logged_in_user.set_superuser_regions!([region])
          post :create, params: {reason: reason, identifier: a_standard_user.id}
        end

        it "I can masquerade as user" do
          expect(response).to redirect_to surveys_path
          expect(session[:masquerade_user]).to eq([a_standard_user.id.to_s, a_standard_user.masquerade_token])
        end
      end

      context "that is not in same region as non-privileged account" do
        before do
          an_account.region = region
          an_account.save!
          logged_in_user.set_superuser_regions!([other_region])
          post :create, params: {reason: reason, identifier: a_standard_user.id}
        end

        it "I can't masquerade as user" do
          expect(flash[:error]).to eq("Sorry, you don't have the required privileges to masquerade as a user from this account.")
          expect(session[:masquerade_user]).to be_nil
        end
      end
    end
  end

  describe "#destroy" do
    # Note, we do not need to test 'non-superuser' access as unmasquerade simply nils a session variable
    # and then redirects the user to the masquerade path where access is validated.

    shared_examples "stops masquerading as another user" do
      it "allows them to stop masquerading as another user" do
        session[:masquerade_user] = [a_standard_user.id.to_s, a_standard_user.masquerade_token]
        allow(Rails.logger).to receive(:info)
        expect(Rails.logger).to receive(:info).with("User \"#{a_ca_superuser}\" has stopped masquerading as \"#{a_standard_user}\".").at_least(:once)
        delete :destroy
        expect(response).to redirect_to new_masquerade_path
        expect(session[:masquerade_user]).to be_nil
      end
    end

    shared_examples "deletes fusionauth entity" do
      context "when fusionauth_jwt_post_signin flag is enabled" do
        # Real JWT configuration using the test setup - use the actual logged-in user's aggregate_id
        let(:payload) { {"effectiveUserId" => logged_in_user.aggregate_id, "accountId" => logged_in_user.account.aggregate_id, "realUserId" => logged_in_user.aggregate_id, "sid" => jwt_sid} }
        let(:web_gateway_equivalent_key) { Rails.application.config.test_jwt_private_key }
        let(:token) { JWT.encode(payload, web_gateway_equivalent_key, "RS256") }
        let(:headers) { {"X-CA-FA-Authorization" => "Bearer #{token}"} }

        before do
          # Set JWT headers for authentication
          @request.headers.merge!(headers)

          allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
          allow(Authentication::Commands::FusionAuthDeleteEntity).to receive(:new).and_return(fusion_auth_delete_entity)
        end

        it "deletes the entity" do
          expect(fusion_auth_delete_entity).to receive(:call).with(entity_id: jwt_sid, account_id: logged_in_user.account.aggregate_id)
          delete :destroy
        end

        it "sets the JWT refresh header" do
          allow(fusion_auth_delete_entity).to receive(:call).and_return(Dry::Monads::Success(true))
          delete :destroy
          expect(response.headers["X-CA-FA-JWT-Refresh"]).to be true
        end

        it "does not call stop_masquerading directly" do
          allow(fusion_auth_delete_entity).to receive(:call).and_return(Dry::Monads::Success(true))
          expect(controller).not_to receive(:stop_masquerading)
          delete :destroy
        end

        context "when entity deletion raises an error" do
          before do
            allow(fusion_auth_delete_entity).to receive(:call).and_raise("Error")
            allow(Rails.logger).to receive(:error)
            allow(Sentry).to receive(:capture_exception)
          end

          it "logs the error and continues" do
            expect(Rails.logger).to receive(:error).with(/Failed to delete FusionAuth entity for masquerade session/)
            expect(Sentry).to receive(:capture_exception)
            expect_any_instance_of(Admin::MasqueradesController).to receive(:stop_masquerading).never
            delete :destroy
            expect(response.code).to eq("500")
            expect(response).to render_template("site/500.html.haml")
          end
        end
      end

      context "when fusionauth_jwt_post_signin flag is disabled" do
        before do
          allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)
        end

        it "does not attempt to delete entity" do
          expect(Authentication::Commands::FusionAuthDeleteEntity).not_to receive(:new)
          delete :destroy
        end

        it "calls stop_masquerading" do
          expect(controller).to receive(:stop_masquerading)
          delete :destroy
        end

        it "does not set the JWT refresh header" do
          delete :destroy
          expect(response.headers["X-CA-FA-JWT-Refresh"]).to be_nil
        end
      end
    end

    shared_examples "restores original user-data cookies" do
      it "restores both regional and basic user-data cookies to original user" do
        original_user_info = {
          locale: logged_in_user.locale,
          employee_aggregate_id: logged_in_user.aggregate_id,
          account_aggregate_id: logged_in_user.account.aggregate_id
        }.to_json

        encoded_info = Base64.strict_encode64(original_user_info)
        encoded_info_without_padding = ::Base64.urlsafe_encode64(original_user_info, padding: false)

        delete :destroy
        expect(response.cookies["cultureamp.user-data"]).to eq(encoded_info)
        expect(response.cookies["cultureamp.#{controller.send(:env_name)}.user-data"]).to eq(encoded_info_without_padding)
      end

      it "deletes the original_user_info cookie" do
        delete :destroy
        expect(response.cookies["original_user_info"]).to be_nil
      end
    end

    context "as a superuser" do
      let(:logged_in_user) { a_ca_superuser }

      context "when original_user_info is set" do
        before do
          # Set an Origin header to satisfy the verify_same_origin concern.
          request.headers["Origin"] = "#{request.scheme}://#{request.host}/"
          original_user_info = {
            locale: logged_in_user.locale,
            employee_aggregate_id: logged_in_user.aggregate_id,
            account_aggregate_id: logged_in_user.account.aggregate_id
          }.to_json

          cookies[:original_user_info] = ::Base64.strict_encode64(original_user_info)
        end

        it_behaves_like "deletes fusionauth entity"
        it_behaves_like "stops masquerading as another user"
        it_behaves_like "restores original user-data cookies"
      end

      context "when original_user_info is not set" do
        it_behaves_like "deletes fusionauth entity"
        it_behaves_like "stops masquerading as another user"

        it "does not attempt to restore cookies" do
          delete :destroy
          expect(response.cookies["cultureamp.user-data"]).to be_nil
          expect(response.cookies["cultureamp.#{controller.send(:env_name)}.user-data"]).to be_nil
        end
      end
    end
  end
end
