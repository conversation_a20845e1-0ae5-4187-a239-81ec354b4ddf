require "rails_helper"

RSpec.describe MultiAccountSurveyingApi::V1::SubsidiarySurveysController, type: :controller do
  include Dry::Monads[:result]

  let(:account) { FactoryBot.create(:account, :with_admin) }
  let(:admin) { account.administrators.first }
  let(:account_aggregate_id) { account.aggregate_id }
  let(:upsert_subsidiary_survey_command) { instance_double(SurveyDesign::MultiAccountSurveying::Commands::UpsertSubsidiarySurvey, call: result) }
  let(:sub_survey_aggregate_id) { "2c998a19-7acd-44be-8cb3-b15d03f64aa1" }
  let(:correlation_id) { SecureRandom.uuid }
  let(:survey_template) { FactoryBot.create(:survey_template, :custom_survey) }
  let(:survey_specifications) do
    {
      "aggregateId" => sub_survey_aggregate_id,
      "orgSurveyAggregateId" => "5abbe2a0-1a2e-4a5b-8801-8732ec863a3f",
      "orgSurveyRegion" => "us",
      "nameTranslations" => {"en" => "Test Survey"},
      "type" => "engagement",
      "surveyPeriodType" => "snapshot",
      "flags" => {
        Flags::REPORT_SHARING.camelize(:lower) => Flags::DISABLED,
        Flags::SHOW_BENCHMARKABLE_QUESTIONS.camelize(:lower) => Flags::ENABLED
      },
      "configuration" => {
        Configs::CAPTURE_LIKERT_COLOR_SCHEMA.camelize(:lower) => Configs::CAPTURE_LIKERT_DEFAULT_COLOR_SCHEMA,
        Configs::SUPPORTED_LOCALES.camelize(:lower) => ["en"]
      }
    }
  end
  let(:event_source) { Domains::SurveyDesign.container.resolve("event_store.source") }

  before do
    sign_in(admin)

    # Need a survey template to create a survey
    survey_template

    # Adding required headers for the request
    request.headers["X-User-Id"] = admin.aggregate_id
    request.headers["X-Correlation-Id"] = correlation_id

    allow(controller).to receive(:authenticated?).and_return(true)
  end

  # post "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/:account_aggregate_id/upsert_survey"
  describe "POST #upsert_survey" do
    let(:params) do
      {
        account_aggregate_id: account_aggregate_id,
        survey_specifications: survey_specifications
      }
    end

    it "returns the correct path for the upsert_survey endpoint" do
      # OpenAPI endpoint doc/openapi/src/openapi.yaml
      # /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/upsert_survey
      expected_path = "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/#{account_aggregate_id}/upsert_survey"
      path = upsert_survey_multi_account_surveying_api_v1_subsidiary_surveys_path(account_aggregate_id)

      expect(path).to eq(expected_path)
    end

    context "when the survey does not exist" do
      it "returns a 201 status with the survey data" do
        post :upsert_survey, params: params
        expect(response).to have_http_status(:created)

        subsidiary_survey = Survey.find_by(aggregate_id: survey_specifications["aggregateId"])
        expect(subsidiary_survey).to have_attributes(
          aggregate_id: sub_survey_aggregate_id,
          org_survey_aggregate_id: survey_specifications["orgSurveyAggregateId"],
          org_survey_region: survey_specifications["orgSurveyRegion"].to_sym,
          name: survey_specifications["nameTranslations"]["en"],
          type: survey_specifications["type"].to_sym,
          survey_period_type: survey_specifications["surveyPeriodType"].to_sym,
          flags: include(survey_specifications["flags"].deep_transform_keys(&:underscore)),
          configuration: include(survey_specifications["configuration"].deep_transform_keys(&:underscore))
        )

        expect(JSON.parse(response.body)).to eq({"id" => subsidiary_survey.id.to_s, "aggregateId" => sub_survey_aggregate_id})
      end
    end

    context "when the survey already exists" do
      before do
        # Create a survey with the same aggregate ID
        SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
          template: survey_template,
          user: admin,
          account: account,
          aggregate_id_override: sub_survey_aggregate_id,
          authorization: nil
        )
      end

      it "updates the existing survey" do
        post :upsert_survey, params: params
        expect(response).to have_http_status(:created)
      end
    end

    context "when the upsert fails" do
      let(:result) { Failure({status: :bad_request, message: "Invalid data"}) }

      it "returns a 400 status with an error message" do
        allow(controller).to receive(:upsert_subsidiary_survey_command).and_return(upsert_subsidiary_survey_command)
        expect(upsert_subsidiary_survey_command).to receive(:call).with(
          account_aggregate_id: account_aggregate_id,
          survey_specifications: survey_specifications.deep_transform_keys(&:underscore),
          metadata: anything
        ).and_return(result)

        post :upsert_survey, params: params
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)).to eq({"error" => "Invalid data"})
      end
    end

    context "when an unexpected error occurs" do
      let(:result) { Failure({status: :internal_server_error, message: "An unexpected error occurred"}) }

      it "returns a 500 status with an error message" do
        allow(controller).to receive(:upsert_subsidiary_survey_command).and_return(upsert_subsidiary_survey_command)
        expect(upsert_subsidiary_survey_command).to receive(:call).with(
          account_aggregate_id: account_aggregate_id,
          survey_specifications: survey_specifications.deep_transform_keys(&:underscore),
          metadata: anything
        ).and_return(result)

        post :upsert_survey, params: params
        expect(response).to have_http_status(:internal_server_error)
        expect(JSON.parse(response.body)).to eq({"error" => "An unexpected error occurred"})
      end
    end

    context "when required headers are absent" do
      let(:correlation_id) { nil }

      it "returns a 403 status" do
        post :upsert_survey, params: params
        expect(response).to have_http_status(:forbidden)
      end
    end
  end

  # post "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/:account_aggregate_id/surveys/:survey_aggregate_id/upsert_section"
  describe "POST #upsert_section" do
    let(:survey_aggregate_id) { "2c998a19-7acd-44be-8cb3-b15d03f64aa1" }
    let(:section_id) { SecureRandom.uuid }
    let(:code) { "#{account.subdomain}.section.#{section_id.split("-")[0]}" }
    let(:name_translations) do
      [
        {"text" => "Test Name", "locale" => "en"},
        {"text" => "Test Name FR", "locale" => "fr"}
      ]
    end
    let(:section_specifications) do
      {
        "section_id" => section_id,
        "position" => "0",
        "intended_purpose" => "standard",
        "name_translations" => name_translations,
        "short_description" => [
          {"text" => "Short Description", "locale" => "en"},
          {"text" => "Short Description FR", "locale" => "fr"}
        ],
        "long_description" => [
          {"text" => "Long Description", "locale" => "en"},
          {"text" => "Long Description FR", "locale" => "fr"}
        ]
      }
    end

    let(:upsert_section_params) do
      {
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_aggregate_id,
        section_specifications: section_specifications
      }
    end

    before do
      # Create a survey with the same aggregate ID
      setup_survey
    end

    it "returns the correct path for the upsert_section endpoint" do
      # OpenAPI endpoint doc/openapi/src/openapi.yaml
      # /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/upsert_section
      expected_path = "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/#{account_aggregate_id}/surveys/#{survey_aggregate_id}/upsert_section"
      path = upsert_section_multi_account_surveying_api_v1_subsidiary_surveys_path(account_aggregate_id, survey_aggregate_id)

      expect(path).to eq(expected_path)
    end

    context "when the section does not exist" do
      it "creates a new section and returns success" do
        post :upsert_section, params: upsert_section_params
        expect(response).to have_http_status(:created)

        section = Theme.where(section_id: section_id).first
        expect(section).to have_attributes(
          name: "Test Name",
          code: code,
          long_desc: "Long Description",
          short_desc: "Short Description",
          order: section_specifications["position"].to_i
        )

        expect(response).to have_http_status(:created)
        expect(JSON.parse(response.body)).to eq({"id" => section.id.to_s, "aggregateId" => section_id})
      end
    end

    context "when the section already exists" do
      before do
        survey = Survey.where(aggregate_id: survey_aggregate_id).first
        setup_section(survey, section_specifications: section_specifications)
      end

      context "when valid section specifications are provided" do
        it "updates the existing section and returns success" do
          # Updated survey section specifications
          specs = section_specifications.merge(
            "position" => "1",
            "name_translations" => [
              {"text" => "Updated Test Name", "locale" => "en"},
              {"text" => "Updated Test Name FR", "locale" => "fr"}
            ],
            "short_description" => [
              {"text" => "Updated Short Description", "locale" => "en"},
              {"text" => "Updated Short Description FR", "locale" => "fr"}
            ],
            "long_description" => [
              {"text" => "Updated Long Description", "locale" => "en"},
              {"text" => "Updated Long Description FR", "locale" => "fr"}
            ]
          )
          post :upsert_section, params: upsert_section_params.merge(section_specifications: specs)

          expect(response).to have_http_status(:created)

          section = Theme.where(section_id: section_id).first
          expect(section).to have_attributes(
            name: "Updated Test Name",
            code: code,
            long_desc: "Updated Long Description",
            short_desc: "Updated Short Description",
            order: specs["position"].to_i
          )

          expect(JSON.parse(response.body)).to eq({"id" => section.id.to_s, "aggregateId" => section_id})
        end
      end

      context "when invalid section specifications are provided" do
        before do
          allow(controller).to receive(:upsert_section_command).and_return(
            instance_double(SurveyDesign::MultiAccountSurveying::Commands::UpsertSection, call: Failure({status: :bad_request, message: "Invalid section data"}))
          )
        end

        it "returns a 400 status with an error message" do
          post :upsert_section, params: upsert_section_params
          expect(response).to have_http_status(:bad_request)
          expect(JSON.parse(response.body)).to eq({"error" => "Invalid section data"})
        end
      end

      context "when an unexpected error occurs" do
        before do
          allow(controller).to receive(:upsert_section_command).and_return(
            instance_double(SurveyDesign::MultiAccountSurveying::Commands::UpsertSection, call: Failure({status: :internal_server_error, message: "An unexpected error occurred"}))
          )
        end

        it "returns a 500 status with an error message" do
          post :upsert_section, params: upsert_section_params
          expect(response).to have_http_status(:internal_server_error)
          expect(JSON.parse(response.body)).to eq({"error" => "An unexpected error occurred"})
        end
      end
    end
  end

  # post "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/:account_aggregate_id/surveys/:survey_aggregate_id/upsert_question"
  describe "POST #upsert_question" do
    let(:survey_aggregate_id) { "2c998a19-7acd-44be-8cb3-b15d03f64aa1" }

    let(:question_id) { "********-1234-5678-1234-5678********" }
    let(:rating_scale) { Domains::Enums::RatingSurveyQuestionScales::AGREEMENT }
    let(:question_type) { Domains::Enums::SurveyQuestionTypes::RATING }
    let(:section_id) { "38470b33-b57b-4122-bff1-411fe36c365e" }
    let(:factor_id) { "dc2b21cb-4b97-45b6-b8a4-b3715a70d622" }
    let(:description_translations) {
      [
        {"text" => "Test Text", "locale" => "en"},
        {"text" => "Test Text FR", "locale" => "fr"}
      ]
    }
    let(:selection_limit) { nil }
    let(:intended_purpose) { Domains::Enums::SurveyQuestionIntendedPurposes::FEEDBACK }
    let(:stq_order) { nil }
    let(:is_mandatory) { false }

    let(:question_specifications) do
      {
        "question_id" => question_id,
        "rating_scale" => rating_scale,
        "question_type" => question_type,
        "section_id" => section_id,
        "description_translations" => description_translations,
        "selection_limit" => selection_limit,
        "intended_purpose" => intended_purpose,
        "stq_order" => stq_order,
        "factor_ids" => [factor_id],
        "is_mandatory" => is_mandatory
      }
    end

    let(:upsert_question_params) do
      {
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_aggregate_id,
        correlation_id: SecureRandom.uuid,
        question_specifications: question_specifications
      }
    end

    before do
      # Create an event sourced survey
      survey = setup_survey
      # Create a section, factor and previous positioned question/stq
      section = setup_section(survey)
      setup_factor(survey)
      previous_question = setup_previous_positioned_question(survey, section)
      # Stub SurveyQuery and SurveyCaptureLayoutAggregate for command handler
      stub_survey_query(survey)
      stub_survey_capture_layout_aggregate(survey, section, previous_question)
    end

    context "when the question does not exist" do
      let(:expected_code) { "#{account.subdomain}.question.********" }

      it "creates a new question and stq, and returns success" do
        post :upsert_question, params: upsert_question_params
        expect(response).to have_http_status(:created)

        question = Question.where(aggregate_id: question_id).first
        expect(question).to have_attributes(
          aggregate_id: question_id,
          description: "Test Text",
          code: expected_code,
          scale: rating_scale,
          survey_aggregate_id: survey_aggregate_id,
          account_id: account.id
        )

        survey = Survey.where(aggregate_id: survey_aggregate_id).first
        stq = survey.stqs.where(question_id: question).first
        expect(stq).to have_attributes(
          question_id: question.id,
          order: 0,
          type: question.type,
          theme_id: Theme.where(section_id: section_id).first.id,
          factor_ids: [Factor.where(aggregate_id: factor_id).first.id]
        )

        expect(JSON.parse(response.body)).to eq({"id" => question.id.to_s, "aggregateId" => question.aggregate_id, "stqId" => stq.id.to_s})
      end
    end

    context "when the question includes select option specs" do
      let(:expected_code) { "#{account.subdomain}.question.********" }
      # Parameters for the question specifications
      let(:select_option_id_1) { "a0d2f305-7ba1-44e5-a698-d9ef9e357b55" }
      let(:select_option_id_2) { "a0d2f305-7ba1-44e5-a698-d9ef9e357b56" }
      let(:select_option_id_3) { "a0d2f305-7ba1-44e5-a698-d9ef9e357b57" }

      let(:select_option_order_1) { {"en" => "00000"} }
      let(:select_option_order_2) { {"en" => "00001"} }
      let(:select_option_order_3) { {"en" => "00002"} }

      let(:select_options_specifications) {
        [
          {
            "select_option_id" => select_option_id_1,
            "value_translations" => [{"text" => "Test Option 1", "locale" => "en"}],
            "sort_term" => select_option_order_1
          },
          {
            "select_option_id" => select_option_id_2,
            "value_translations" => [{"text" => "Test Option 2", "locale" => "en"}],
            "sort_term" => select_option_order_2
          },
          {
            "select_option_id" => select_option_id_3,
            "value_translations" => [{"text" => "Test Option 3", "locale" => "en"}],
            "sort_term" => select_option_order_3
          }
        ]
      }

      it "creates a new question, stq and select options, and returns success" do
        question_type = Domains::Enums::SurveyQuestionTypes::SELECT
        question_specifications[:question_type] = question_type
        upsert_question_params[:question_specifications][:select_options] = select_options_specifications

        post :upsert_question, params: upsert_question_params
        expect(response).to have_http_status(:created)

        question = Question.where(aggregate_id: question_id).first

        expect(question).to have_attributes(
          aggregate_id: question_id,
          description: "Test Text",
          code: expected_code,
          scale: rating_scale,
          survey_aggregate_id: survey_aggregate_id,
          account_id: account.id
        )

        expect(question.select_options.count).to eq(3)
        select_option_one = question.select_options.where(select_option_id: select_option_id_1).first
        select_option_two = question.select_options.where(select_option_id: select_option_id_2).first
        select_option_three = question.select_options.where(select_option_id: select_option_id_3).first
        expect(select_option_one).to have_attributes(
          select_option_id: select_option_id_1,
          value: "Test Option 1",
          sort_term: "00000"
        )
        expect(select_option_two).to have_attributes(
          select_option_id: select_option_id_2,
          value: "Test Option 2",
          sort_term: "00001"
        )
        expect(select_option_three).to have_attributes(
          select_option_id: select_option_id_3,
          value: "Test Option 3",
          sort_term: "00002"
        )

        survey = Survey.where(aggregate_id: survey_aggregate_id).first
        stq = survey.stqs.where(question_id: question).first
        expect(stq).to have_attributes(
          question_id: question.id,
          order: 0,
          type: question.type,
          theme_id: Theme.where(section_id: section_id).first.id,
          factor_ids: [Factor.where(aggregate_id: factor_id).first.id]
        )

        expected_select_option_ids = [
          {
            "id" => select_option_one.id.to_s,
            "aggregateId" => select_option_one.select_option_id
          },
          {
            "id" => select_option_two.id.to_s,
            "aggregateId" => select_option_two.select_option_id
          },
          {
            "id" => select_option_three.id.to_s,
            "aggregateId" => select_option_three.select_option_id
          }
        ]
        expect(JSON.parse(response.body)).to eq({"id" => question.id.to_s, "aggregateId" => question.aggregate_id, "stqId" => stq.id.to_s, "selectOptions" => expected_select_option_ids})
      end
    end

    context "when the question already exists" do
      let(:existing_question) { FactoryBot.create(:rating_question, account: account) }
      let(:question_id) { existing_question.aggregate_id }

      it "returns error" do
        post :upsert_question, params: upsert_question_params
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)).to eq({"error" => "Update is not implemented yet"})
      end
    end
  end

  private

  def setup_survey
    SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
      template: survey_template,
      user: admin,
      account: account,
      aggregate_id_override: survey_aggregate_id,
      authorization: nil
    )
  end

  def setup_section(survey, section_specifications: nil)
    return FactoryBot.create(:theme, survey: survey, section_id: section_id, name: [{locale: "en", text: "Overall"}]) unless section_specifications.present?

    metadata = EventFramework::Event::Metadata.new(
      user_id: SecureRandom.uuid,
      account_id: SecureRandom.uuid,
      correlation_id: SecureRandom.uuid
    )

    SurveyDesign::MultiAccountSurveying::Commands::CreateSection.new.call(
      survey_aggregate_id: survey.aggregate_id,
      section_specifications: section_specifications.merge(code: "#{account.subdomain}.section.#{section_specifications["section_id"].split("-")[0]}"),
      metadata: metadata
    )
  end

  def setup_factor(survey)
    FactoryBot.create(:factor, survey: survey, aggregate_id: factor_id)
  end

  def setup_previous_positioned_question(survey, section)
    previous_positioned_question = FactoryBot.create(:rating_question, account: account)
    FactoryBot.create(:survey_to_question, survey: survey, theme_id: section.id, question: previous_positioned_question, order: 0)

    previous_positioned_question
  end

  ##
  # Stub the SurveyQuery to return SurveyQuery::Result.
  # This is used to determine survey assignment for stq by add question command handler.
  def stub_survey_query(survey)
    allow(Domains::SurveyDesign::Queries::SurveyQuery).to receive(:call).and_return(
      Domains::SurveyDesign::Queries::SurveyQuery::Result.new(
        survey_id: survey_aggregate_id,
        account_id: account_aggregate_id,
        survey_capture_layout_id: survey.survey_capture_layout_id
      )
    )
  end

  ##
  # For #upsert_question - Stub the SurveyCaptureLayoutAggregate to return SurveyCaptureLayoutAggregate::Section.
  # This is used to determine section assignment and question positioning for stq by add question command handler.
  def stub_survey_capture_layout_aggregate(survey, section, previous_positioned_question)
    aggregate = Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate.build(survey.survey_capture_layout_id)
    # Create section aggregate
    section_intended_purpose = Domains::Enums::SectionIntendedPurposes::STANDARD
    aggregate_section = Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate::Section.new(
      id: section.section_id,
      name: section.name,
      short_description: section.name,
      long_description: section.name,
      code: section.code,
      intended_purpose: section_intended_purpose
    )
    # Set previous question to section aggregate
    aggregate_section.questions << previous_positioned_question.aggregate_id

    aggregate.send(:sections_by_purpose)[section_intended_purpose] << aggregate_section

    allow(Domains::SurveyDesign::SurveyCaptureLayout::SurveyCaptureLayoutAggregate).to receive(:build).with(survey.survey_capture_layout_id).and_return(aggregate)
  end

  # post "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/:account_aggregate_id/surveys/:survey_aggregate_id/upsert_factor"
  describe "POST #upsert_factor" do
    let(:survey) { FactoryBot.create(:survey, account: account) }
    let(:survey_aggregate_id) { survey.aggregate_id }
    let(:factor_id) { "********-1234-5678-1234-5678********" }
    let(:name_translations) do
      [
        {"text" => "Test Factor", "locale" => "en"},
        {"text" => "Test Factor FR", "locale" => "fr"}
      ]
    end
    let(:is_index_factor) { "true" }
    let(:is_key_factor) { "false" }

    let(:factor_specifications) do
      {
        "factorId" => factor_id,
        "nameTranslations" => name_translations,
        "isIndexFactor" => is_index_factor,
        "isKeyFactor" => is_key_factor
      }
    end

    let(:upsert_factor_params) do
      {
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_aggregate_id,
        factor_specifications: factor_specifications
      }
    end

    it "returns the correct path for the upsert_factor endpoint" do
      # OpenAPI endpoint doc/openapi/src/openapi.yaml
      # /multi_account_surveying_api/v1/subsidiary_surveys/accounts/#{accountAggregateId}/surveys/{surveyAggregateId}/upsert_factor
      expected_path = "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/#{account_aggregate_id}/surveys/#{survey_aggregate_id}/upsert_factor"
      path = upsert_factor_multi_account_surveying_api_v1_subsidiary_surveys_path(account_aggregate_id, survey_aggregate_id)

      expect(path).to eq(expected_path)
    end

    context "when the factor does not exist" do
      it "creates a new factor and returns success" do
        post :upsert_factor, params: upsert_factor_params
        expect(response).to have_http_status(:created)

        factor = Factor.where(aggregate_id: factor_id).first
        expect(JSON.parse(response.body)).to eq({"id" => factor.id.to_s, "aggregateId" => factor_id})

        factor_type = factor.is_key_factor? ? :output : :driver
        name_translations_map = name_translations.map { |t| [t["locale"], t["text"]] }.to_h

        expect(factor).to have_attributes(
          name_translations: name_translations_map,
          index_factor: !!is_index_factor,
          type: factor_type,
          survey_id: survey.id
        )
      end
    end

    context "when the factor already exists" do
      let(:factor) { FactoryBot.create(:factor, survey: survey, name: "Default Name") }
      let(:factor_id) { factor.aggregate_id }
      let(:name_translations) do
        [
          {"text" => "Test Factor", "locale" => "en"},
          {"text" => "Test Factor FR", "locale" => "fr"}
        ]
      end
      let(:is_index_factor) { "true" }
      let(:is_key_factor) { "false" }

      it "updates the factor" do
        post :upsert_factor, params: upsert_factor_params
        expect(response).to have_http_status(:created)
        expect(JSON.parse(response.body)).to eq({"id" => factor.id.to_s, "aggregateId" => factor_id})

        factor = Factor.where(aggregate_id: factor_id).first

        expect(factor).to have_attributes(
          name_translations: {
            "en" => "Test Factor",
            "fr" => "Test Factor FR"
          },
          index_factor: !!is_index_factor
        )
        expect(factor.is_key_factor?).to eq(!!is_key_factor)
      end
    end

    context "when the upsert fails" do
      let(:result) { Failure({status: :bad_request, message: "Invalid data"}) }
      let(:upsert_factor) { instance_double(SurveyDesign::MultiAccountSurveying::Commands::UpsertFactor, call: result) }

      it "returns a 400 status with an error message" do
        allow(controller).to receive(:upsert_factor_command).and_return(upsert_factor)
        expect(upsert_factor).to receive(:call).with(
          survey_aggregate_id: survey_aggregate_id,
          factor_specifications: factor_specifications.deep_transform_keys(&:underscore)
        ).and_return(result)

        post :upsert_factor, params: upsert_factor_params
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)).to eq({"error" => "Invalid data"})
      end
    end

    context "when an unexpected error occurs" do
      let(:result) { Failure({status: :internal_server_error, message: "An unexpected error occurred"}) }
      let(:upsert_factor) { instance_double(SurveyDesign::MultiAccountSurveying::Commands::UpsertFactor, call: result) }

      it "returns a 500 status with an error message" do
        allow(controller).to receive(:upsert_factor_command).and_return(upsert_factor)
        expect(upsert_factor).to receive(:call).with(
          survey_aggregate_id: survey_aggregate_id,
          factor_specifications: factor_specifications.deep_transform_keys(&:underscore)
        ).and_return(result)

        post :upsert_factor, params: upsert_factor_params
        expect(response).to have_http_status(:internal_server_error)
        expect(JSON.parse(response.body)).to eq({"error" => "An unexpected error occurred"})
      end
    end
  end

  # post "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/:account_aggregate_id/surveys/:survey_aggregate_id/delete_section"
  describe "POST #delete_section" do
    let(:create_section_command) { SurveyDesign::MultiAccountSurveying::Commands::CreateSection.new }
    let(:survey_aggregate_id) { "2c998a19-7acd-44be-8cb3-b15d03f64aa1" }
    let(:section_id) { "********-1234-5678-1234-5678********" }
    let(:section_specifications) do
      {
        "section_id" => section_id,
        "position" => "1",
        "intended_purpose" => "standard",
        "name_translations" => [
          {"text" => "Test Name", "locale" => "en"},
          {"text" => "Test Name FR", "locale" => "fr"}
        ],
        "short_description" => [
          {"text" => "Short Description", "locale" => "en"},
          {"text" => "Short Description FR", "locale" => "fr"}
        ],
        "long_description" => [
          {"text" => "Long Description", "locale" => "en"},
          {"text" => "Long Description FR", "locale" => "fr"}
        ],
        "code" => "#{account.subdomain}.section.#{section_id.split("-")[0]}"
      }
    end
    let(:metadata) do
      EventFramework::Event::Metadata.new(
        user_id: "e6419ccd-0bd6-4e14-bbd0-53ea8649fa78",
        account_id: account_aggregate_id,
        correlation_id: correlation_id
      )
    end
    let(:delete_section_params) do
      {
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_aggregate_id,
        section_id: section_id
      }
    end

    before do
      # Create a survey with the same aggregate ID
      SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
        template: survey_template,
        user: admin,
        account: account,
        aggregate_id_override: survey_aggregate_id,
        authorization: nil
      )

      # Create a section to delete
      create_section_command.call(
        survey_aggregate_id: survey_aggregate_id,
        section_specifications: section_specifications,
        metadata: metadata
      )
    end

    it "returns the correct path for the delete_section endpoint" do
      # OpenAPI endpoint doc/openapi/src/openapi.yaml
      # /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/delete_section
      expected_path = "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/#{account_aggregate_id}/surveys/#{survey_aggregate_id}/delete_section"
      path = delete_section_multi_account_surveying_api_v1_subsidiary_surveys_path(account_aggregate_id, survey_aggregate_id)

      expect(path).to eq(expected_path)
    end

    context "when the section exists" do
      it "deletes the section and returns status 202" do
        post :delete_section, params: delete_section_params
        expect(response).to have_http_status(202)

        section = Theme.where(section_id: section_id).first
        expect(section.status).to eq(:deleted)
      end
    end

    context "when the section does not exist" do
      it "returns http status 204" do
        delete_section_params = {
          account_aggregate_id: account_aggregate_id,
          survey_aggregate_id: survey_aggregate_id,
          section_id: SecureRandom.uuid
        }

        post :delete_section, params: delete_section_params
        expect(response).to have_http_status(204)

        section = Theme.where(section_id: delete_section_params[:section_id]).first
        expect(section).to be_nil
      end
    end

    context "when the delete fails" do
      let(:result) { Failure({status: :bad_request, message: "Invalid data"}) }
      let(:delete_section) { instance_double(SurveyDesign::MultiAccountSurveying::Commands::DeleteSection, call: result) }

      it "returns a 400 status with an error message" do
        allow(controller).to receive(:delete_section_command).and_return(delete_section)
        expect(delete_section).to receive(:call).with(
          survey_aggregate_id: survey_aggregate_id,
          section_id: section_id,
          metadata: anything
        ).and_return(result)
        post :delete_section, params: delete_section_params
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)).to eq({"error" => "Invalid data"})
      end
    end
  end

  # post "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/:account_aggregate_id/surveys/:survey_aggregate_id/delete_survey_question"
  describe "POST #delete_survey_question" do
    let(:create_section_command) { SurveyDesign::MultiAccountSurveying::Commands::CreateSection.new }

    # TODO replace with MAS add question command when ERV-2161 is merged
    let(:add_question_command) { Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommand }
    let(:add_question_command_handler) { Domains::SurveyDesign::SurveyQuestion::AddToSurveyCommandHandler.new }

    let(:survey_aggregate_id) { "2c998a19-7acd-44be-8cb3-b15d03f64aa1" }
    let(:question_id) { SecureRandom.uuid }

    let(:delete_survey_question_params) do
      {
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_aggregate_id,
        question_id: question_id
      }
    end

    let(:question_code) { SecureRandom.uuid }

    let(:section_id) { "********-1234-5678-1234-5678********" }
    let(:section_specifications) do
      {
        "section_id" => section_id,
        "position" => "1",
        "intended_purpose" => "standard",
        "name_translations" => [
          {"text" => "Test Name", "locale" => "en"},
          {"text" => "Test Name FR", "locale" => "fr"}
        ],
        "short_description" => [
          {"text" => "Short Description", "locale" => "en"},
          {"text" => "Short Description FR", "locale" => "fr"}
        ],
        "long_description" => [
          {"text" => "Long Description", "locale" => "en"},
          {"text" => "Long Description FR", "locale" => "fr"}
        ],
        "code" => "#{account.subdomain}.section.#{section_id.split("-")[0]}"
      }
    end
    let(:metadata) do
      EventFramework::Event::Metadata.new(
        user_id: "e6419ccd-0bd6-4e14-bbd0-53ea8649fa78",
        account_id: account_aggregate_id,
        correlation_id: correlation_id
      )
    end

    before do
      # Create a survey with the same aggregate ID
      SurveyTemplates::Commands::CreateSurveyFromTemplate.new.call(
        template: survey_template,
        user: admin,
        account: account,
        aggregate_id_override: survey_aggregate_id,
        authorization: nil
      )

      # Create a section to add the question to
      create_section_command.call(
        survey_aggregate_id: survey_aggregate_id,
        section_specifications: section_specifications,
        metadata: metadata
      )

      event_source.get_after(0).each do |event|
        Domains::SurveyDesign::Projectors::SurveyCommandProjector.new.handle_event(event)
      end

      # Create a question to delete
      # TODO replace this part with the MAS add question command (which encapsulates the command+handler) after ERV-2161 is merged
      command = add_question_command.new(
        aggregate_id: question_id,
        survey_id: survey_aggregate_id,
        code: question_code,
        question_type: "rating",
        rating_scale: "agreement",
        section_id: section_id,
        text: [{text: "Something", locale: "en"}],
        positioned_after_question_id: nil,
        cloned_from_curated_question_reference_code: nil,
        cloned_from_survey_question_id: nil,
        added_at: Time.now.utc,
        intended_purpose: Domains::Enums::SurveyQuestionIntendedPurposes::FEEDBACK
      )
      add_question_command_handler.call(command: command, metadata: metadata)
    end

    it "returns the correct path for the delete_survey_question endpoint" do
      # OpenAPI endpoint doc/openapi/src/openapi.yaml
      # /multi_account_surveying_api/v1/subsidiary_surveys/accounts/{accountAggregateId}/surveys/{surveyAggregateId}/delete_survey_question
      expected_path = "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/#{account_aggregate_id}/surveys/#{survey_aggregate_id}/delete_survey_question"
      path = delete_survey_question_multi_account_surveying_api_v1_subsidiary_surveys_path(account_aggregate_id, survey_aggregate_id)

      expect(path).to eq(expected_path)
    end

    context "when the question exists and is not already deleted" do
      it "deletes the question and returns 202 status code" do
        post :delete_survey_question, params: delete_survey_question_params
        expect(response).to have_http_status(:accepted)
      end
    end

    context "when the question has already been deleted" do
      before do
        delete_survey_question_command = SurveyDesign::MultiAccountSurveying::Commands::DeleteSurveyQuestion.new
        delete_survey_question_command.call(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          metadata: metadata
        )
      end

      it "returns 204 status code" do
        post :delete_survey_question, params: delete_survey_question_params
        expect(response).to have_http_status(:no_content)
      end
    end

    context "when the question is an ENPS11 question" do
      let(:enps11_question_aggregate_id) do
        create_enps11_question_command = Enps::Commands::CreateEnps11Question.new
        enps11_question_result = create_enps11_question_command.call(
          survey_aggregate_id: survey_aggregate_id,
          metadata: metadata
        )
        enps11_question_result.value!.aggregate_id
      end

      it "blocks the deletion" do
        enps11_delete_question_params = {
          account_aggregate_id: account_aggregate_id,
          survey_aggregate_id: survey_aggregate_id,
          question_id: enps11_question_aggregate_id
        }

        post :delete_survey_question, params: enps11_delete_question_params
        expect(response).to have_http_status(:bad_request)
      end
    end
  end

  # post "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/:account_aggregate_id/surveys/:survey_aggregate_id/upsert_question_display_condition"
  describe "POST #upsert_question_display_condition" do
    let(:upsert_question_display_condition) { SurveyDesign::MultiAccountSurveying::Commands::UpsertQuestionDisplayCondition.new }
    let(:account) { FactoryBot.create(:account, :with_admin) }
    let(:account_aggregate_id) { account.aggregate_id }
    let(:survey) { FactoryBot.create(:survey, account: account) }
    let(:survey_aggregate_id) { survey.aggregate_id }
    let(:parent_question) { FactoryBot.create(:single_select_question, survey_aggregate_id: survey_aggregate_id, select_options: %w[abc def xyz]) }
    let(:parent_question_id) { parent_question.aggregate_id }
    let(:select_options) { parent_question.select_options }
    let(:branching_select_option_ids) { select_options.map(&:select_option_id).take(2) } # Taking first two options for the test
    let(:question) { FactoryBot.create(:free_text_question, survey_aggregate_id: survey_aggregate_id) }
    let(:question_id) { question.aggregate_id }
    let(:display_condition_id) { SecureRandom.uuid }
    let(:display_condition_specifications) do
      {
        "questionId" => question_id,
        "answerBranchingRule" => {
          "id" => display_condition_id,
          "parentQuestionId" => parent_question_id,
          "selectOptionIds" => branching_select_option_ids
        }
      }
    end
    let(:upsert_question_display_condition_params) do
      {
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_aggregate_id,
        question_display_condition_specifications: display_condition_specifications
      }
    end

    before do
      # Mocking projection database inserts
      Domains::SurveyDesign.database(:projections)[:survey_command_projection].insert(
        account_id: account.aggregate_id,
        survey_id: survey_aggregate_id,
        survey_capture_layout_id: SecureRandom.uuid
      )

      Domains::SurveyDesign.database(:projections)[:question_command_projection_a].insert(
        question_id: parent_question.aggregate_id,
        survey_id: survey_aggregate_id,
        account_id: account.aggregate_id
      )

      Domains::SurveyDesign.database(:projections)[:question_command_projection_a].insert(
        question_id: question.aggregate_id,
        survey_id: survey_aggregate_id,
        account_id: account.aggregate_id
      )

      # Mocking the display condition select options query to return valid parent
      allow(Domains::SurveyDesign::Queries::DisplayConditionSelectOptionsQuery).to receive(:has_valid_parent?).and_return(true)
    end

    it "returns the correct path for the upsert_question_display_condition endpoint" do
      # OpenAPI endpoint doc/openapi/src/openapi.yaml
      # /multi_account_surveying_api/v1/subsidiary_surveys/accounts/#{accountAggregateId}/surveys/{surveyAggregateId}/upsert_question_display_condition
      expected_path = "/multi_account_surveying_api/v1/subsidiary_surveys/accounts/#{account_aggregate_id}/surveys/#{survey_aggregate_id}/upsert_question_display_condition"
      path = upsert_question_display_condition_multi_account_surveying_api_v1_subsidiary_surveys_path(account_aggregate_id, survey_aggregate_id)

      expect(path).to eq(expected_path)
    end

    it "should be idempotent" do
      # First call to create the display condition
      post :upsert_question_display_condition, params: upsert_question_display_condition_params
      expect(response).to have_http_status(:created)

      # Second call with the same parameters should return 201 again
      post :upsert_question_display_condition, params: upsert_question_display_condition_params
      expect(response).to have_http_status(:created)

      # Verify that the display condition was created only once
      display_conditions = DisplayCondition.where(
        question_id: question.id,
        parent_question_id: parent_question.id
      )
      expect(display_conditions.count).to eq(1)
    end

    context "when the display condition does not exist" do
      it "creates a new display condition and returns success" do
        post :upsert_question_display_condition, params: upsert_question_display_condition_params
        expect(response).to have_http_status(:created)

        display_condition = DisplayCondition.where(
          question_id: question.id,
          parent_question_id: parent_question.id
        ).first
        expect(display_condition).not_to be_nil
        expect(display_condition.select_option_aggregate_ids).to match_array(branching_select_option_ids)
      end
    end

    context "when error occurs during upsert" do
      let(:result) { Failure({status: :bad_request, message: "Invalid data"}) }
      let(:upsert_question_display_condition) { instance_double(SurveyDesign::MultiAccountSurveying::Commands::UpsertQuestionDisplayCondition, call: result) }

      before do
        allow(controller).to receive(:upsert_question_display_condition_command).and_return(upsert_question_display_condition)
      end

      it "returns a 400 status with an error message" do
        expect(upsert_question_display_condition).to receive(:call).with(
          survey_aggregate_id: survey_aggregate_id,
          question_id: question_id,
          display_condition_specifications: {
            answer_branching_rule: {
              id: display_condition_id,
              parent_question_id: parent_question_id,
              select_option_ids: branching_select_option_ids
            }
          },
          metadata: anything
        ).and_return(result)

        post :upsert_question_display_condition, params: upsert_question_display_condition_params
        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)).to eq({"error" => "Invalid data"})
      end
    end
  end
end
