require "rails_helper"
require "integration"

RSpec.describe SamlController do
  include Dry::Monads[:result]

  let(:saml_account) { FactoryBot.create(:account_without_master) }
  let(:fingerprint) { nil }
  let(:cert) { nil }
  let(:cert_multi) { nil }
  let(:cookie_env_name) { "development-#{Murmur::REGION}" }
  let!(:saml_integration) do
    FactoryBot.create(
      :saml_integration,
      account: saml_account,
      idp_sso_target_url: "http://sso-provider.example.com/callback/saml",
      idp_cert_fingerprint: fingerprint,
      idp_cert: cert,
      idp_cert_multi: cert_multi,
      authentication_field: "email"
    )
  end

  before do
    allow(Jobs::AnalyticsJob).to receive(:enqueue)

    expect(SAML::Authenticator)
      .to receive(:valid_saml_account?)
      .with(saml_account)
      .and_return(valid_saml_account)
  end
  let(:valid_saml_account) { :ok }

  def response_redirect_uri
    Addressable::URI.parse(response.get_header(ActionDispatch::Response::LOCATION))
  end

  describe "#show" do
    let(:settings) do
      SAML::Settings.new(saml_integration: saml_account.active_saml_integration)
    end

    context "account has SAML enabled" do
      before do
        allow(SAML::Settings).to receive(:new).and_return(settings)
      end

      context "simple redirect url" do
        let(:redirect) { "/steve" }

        subject do
          get :show, params: {sd: saml_account.subdomain, redirect: redirect}
        end

        it "generates a SAML redirect for SAML accounts" do
          subject

          expect(response.code).to eq("302")
          expect(response_redirect_uri.query_values).to include("RelayState" => redirect)
        end

        it "has the correct assertion_consumer_service_url set" do
          subject

          expect(SAML::Settings)
            .to have_received(:new)
            .with(
              saml_integration: saml_account.active_saml_integration,
              opts: {
                assertion_consumer_service_url: "http://#{saml_account.domain}/saml/callback/#{saml_account.subdomain}",
                issuer: saml_account.domain
              }
            )
        end

        # when doing the login for teams, the request host is identity.cultureamp.com.
        # The AzureAD has been configured with a value reply url that includes the domain
        # <subdomain>.cultureamp.com. This is a problem we had with Unilever trying out
        # our MS Teams bot. This should hopefully fix it for all customers by making the
        # domain consistent.
        it "does not switch identity.cultureamp.com to <subdomain>.cultureamp.com" do
          stub_const "Murmur::CULTURE_AMP_DOMAIN", "cultureamp.com"
          saml_account.disable!(Flags::SAML_REPLY_URL_USE_ACCOUNT_DOMAIN)
          subject

          expect(SAML::Settings)
            .to have_received(:new)
            .with(
              saml_integration: saml_account.active_saml_integration,
              opts: {
                assertion_consumer_service_url: "http://test.host/saml/callback/#{saml_account.subdomain}",
                issuer: "test.host"
              }
            )
        end

        it "sets an Amplitude Device ID in the session" do
          subject

          expect(session[:amplitude_device_id]).not_to be(nil)
        end

        it "sets an Amplitude Session ID in the session" do
          subject

          expect(session[:amplitude_session_id]).not_to be(nil)
        end

        it "tracks the SSO initiated event" do
          expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
            event_type: "SSO Sign-in Initiated"
          ))

          subject
        end
      end

      context "redirect is protocol relative url" do
        let(:redirect) { "//malacious.example.com/abc" }

        it "should set the session redirect to the extracted path if redirect param is protocol relative url" do
          get :show, params: {sd: saml_account.subdomain, redirect: redirect}

          expect(response_redirect_uri.query_values).to include("RelayState" => "/abc")
        end
      end
    end

    context "when saml login is not allowed" do
      let(:redirect) { "/" }
      let(:valid_saml_account) { :saml_not_allowed }
      let(:saml_allowed) { false }

      before { get :show, params: {sd: saml_account.subdomain, redirect: redirect} }

      it "redirects to signin page" do
        expect(flash[:alert]).to eq "SAML authentication has been disabled. Please check with your Culture Amp administrator or email #{Appamp::Settings.email_support}."
        expect(response).to redirect_to new_user_session_path
      end
    end

    context "account without SAML" do
      let(:redirect) { "" }
      let(:valid_saml_account) { :inactive_saml }

      before { get :show, params: {sd: saml_account.subdomain, redirect: redirect} }

      it "redirects to the sign in page" do
        expect(response).to redirect_to new_user_session_path
      end
    end
  end

  describe "#metadata" do
    before do
      get :metadata, params: {sd: saml_account.subdomain}
    end

    it_behaves_like "an ok response"
  end

  describe "#callback" do
    let(:user_from_saml_account) { FactoryBot.create(:user, account: saml_account, failed_attempts: 2) }

    context "when saml login is not allowed" do
      let(:valid_saml_account) { :saml_not_allowed }
      before do
        post :callback, params: {sd: saml_account.subdomain}
      end

      it "redirects to signin page" do
        expect(flash[:alert]).to eq "SAML authentication has been disabled. Please check with your Culture Amp administrator or email #{Appamp::Settings.email_support}."
        expect(response).to redirect_to new_user_session_path
      end
    end

    context "when account is a valid saml account" do
      let(:relay_state) { "" }

      subject do
        post :callback, params: {sd: saml_account.subdomain, SAMLResponse: saml_response_body, RelayState: relay_state}
      end

      before(:each) do
        Timecop.freeze(Time.parse(saml_expiry_time) - 1.second) # travel back to when the response was still valid
      end

      context "and the IdP supplies a single certificate" do
        let(:fingerprint) { "24:2E:D0:60:18:5D:69:CC:69:38:64:F6:31:10:DF:E3:E2:2C:FE:CC" }
        let(:idp_cert) { file_fixture(SAML::CERT_FILENAME).read }
        let!(:user_from_saml_account) { FactoryBot.create(:user, email: saml_name_id, account: saml_account) }

        let(:saml_name_id) { "<EMAIL>" }
        let(:saml_name_id_format) { "urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress" }
        let(:saml_expiry_time) { "2021-08-20T02:24:12.959Z" }
        let(:saml_response_body) {
          data = SAML::ResponseFixture.new(name_id: saml_name_id,
                                           name_id_format: saml_name_id_format,
                                           expiry_time: saml_expiry_time)
          ERB.new(file_fixture(SAML::RESPONSE_TEMPLATE_FILENAME).read).result(data.get_binding)
        }

        it "signs in the user and redirects" do
          subject

          expect(controller.current_user).to eq user_from_saml_account
          expect(response.status).to eq 302
          expect(response_redirect_uri.path).to eq "/"
        end

        it "sets created_at in the session after successful sign-in" do
          subject

          expect(session[:created_at]).to be_present
          expect(session[:created_at]).to be_within(1.second).of(Time.current.utc)
        end

        it "tracks the sign-in event" do
          expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
            user_aggregate_id: user_from_saml_account.aggregate_id,
            event_type: "User Signed In",
            event_properties: hash_including(
              "Authentication method" => "SAML",
              "Failed attempts" => 0,
              "Is deep link" => false
            )
          ))

          subject
        end

        it "sets an Amplitude Device ID in the session" do
          subject

          expect(session[:amplitude_device_id]).not_to be(nil)
        end

        context "when RelayState is provided" do
          let(:relay_state) { "/reports/insight/42" }

          it "redirects to the path provided via RelayState" do
            subject

            expect(response_redirect_uri.path).to eq relay_state
          end

          it "tracks the sign-in event with deep link true" do
            expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
              user_aggregate_id: user_from_saml_account.aggregate_id,
              event_type: "User Signed In",
              event_properties: hash_including(
                "Is deep link" => true
              )
            ))
            subject
          end
        end

        context "no response params" do
          let(:saml_response_body) { nil }

          it "redirects to the sign in page" do
            subject

            expect(response).to redirect_to(saml_signin_path(saml_account.subdomain))
          end
        end

        context "when the response is invalid" do
          before do
            subject
          end
          let(:saml_response_body) { "asdf" }

          it_behaves_like "an http unauthorized error"
        end

        context "when the response name id format is invalid" do
          before do
            subject
          end
          let(:saml_name_id_format) { "foobar" }

          it_behaves_like "an http unauthorized error"
        end

        context "when the user does not exist in the account" do
          before do
            subject
          end
          let!(:user_from_saml_account) { FactoryBot.create(:user, email: "<EMAIL>", account: saml_account) }

          it_behaves_like "an http forbidden error"
        end

        context "when the SAML response has expired" do
          before do
            Timecop.unfreeze
            subject
          end

          it_behaves_like "an http unauthorized error"
        end

        context "when authentication response fusion auth sign in failed" do
          let(:authenticate_response) { [:ok, user_from_saml_account, false] }
          before do
            expect(SAML::Authenticator).to receive(:authenticate).and_return(authenticate_response)
          end

          it "tracks the sign-in event as Murmur" do
            expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
              user_aggregate_id: user_from_saml_account.aggregate_id,
              event_type: "User Signed In",
              event_properties: hash_including(
                "Authentication method" => "SAML",
                "Failed attempts" => 0,
                "Authentication system" => "Murmur"
              )
            ))

            subject
          end

          it "signs the user in through Murmur's traditional flow" do
            expect(controller).not_to receive(:fusionauth_passwordless_login)
            subject
            expect(controller.current_user).to eq user_from_saml_account
          end

          it "does not set cookies for basic user info" do
            subject

            expect(response.cookies["cultureamp.user-data"]).to be_nil
          end

          it "does not set cookie for user data by region" do
            subject

            expect(response.cookies["cultureamp.#{cookie_env_name}.user-data"]).to be_nil
          end
        end

        context "when post_sign_in is enabled but fusion auth sign in returns invalid" do
          let(:authenticate_response) { [:ok, user_from_saml_account, false] }

          before do
            expect(SAML::Authenticator).to receive(:authenticate).and_return(authenticate_response)
            allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
          end

          it "should raise an exception" do
            expect {
              subject
            }.to raise_error("Failed to authenticate via FusionAuth for SAML user")
          end

          it "logs an error message" do
            expect(Rails.logger).to receive(:error).with("Failed to authenticate via FusionAuth for SAML user: #{user_from_saml_account.email}")

            begin
              subject
            rescue
              # Ignore the exception
            end
          end
        end

        context "when authentication response fusion auth sign in succeeded" do
          let(:authenticate_response) { [:ok, user_from_saml_account, true] }
          let(:passwordless_result) { Success(jwt: "fake-jwt", refresh_token: "fake-refresh-token") }
          let(:passwordless_login) { instance_double(Authentication::Commands::FusionAuthPasswordlessLogin) }

          let(:payload) { {"effectiveUserId" => user_from_saml_account.aggregate_id, "accountId" => user_from_saml_account.account.aggregate_id, "realUserId" => user_from_saml_account.aggregate_id} }
          let(:web_gateway_equivalent_key) { Rails.application.config.test_jwt_private_key }
          let(:token) { JWT.encode(payload, web_gateway_equivalent_key, "RS256") }
          let(:headers) { {"X-CA-FA-Authorization" => "Bearer #{token}"} }

          before do
            @request.host = "#{user_from_saml_account.account.subdomain}.#{Murmur::CULTURE_AMP_DOMAIN}"
            @request.headers.merge!(headers)

            expect(SAML::Authenticator).to receive(:authenticate).and_return(authenticate_response)
            allow(Authentication::Commands::FusionAuthPasswordlessLogin).to receive(:new).and_return(passwordless_login)
            allow(passwordless_login).to receive(:call).and_return(passwordless_result)
            allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
          end

          it "tracks the sign-in event as FusionAuth" do
            expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
              user_aggregate_id: user_from_saml_account.aggregate_id,
              event_type: "User Signed In",
              event_properties: hash_including(
                "Authentication method" => "SAML",
                "Failed attempts" => 0,
                "Authentication system" => "FusionAuth"
              )
            ))

            subject
          end

          it "attempts to generate JWT through passwordless login" do
            expect(passwordless_login).to receive(:call).with(
              account: user_from_saml_account.account,
              email: user_from_saml_account.email
            )
            subject
          end

          it "does not sign in using traditional flow" do
            expect(controller).not_to receive(:sign_in)
            subject
          end

          it "sets cookies for fa jwt and refresh token" do
            subject

            fa_jwt_cookie_name = "cultureamp.#{cookie_env_name}.token"
            fa_refresh_token_cookie_name = "cultureamp.#{cookie_env_name}.refresh-token"

            fa_jwt_cookie = response.cookies[fa_jwt_cookie_name]
            fa_refresh_token_cookie = response.cookies[fa_refresh_token_cookie_name]

            expect(fa_jwt_cookie).to eq("fake-jwt")
            expect(fa_refresh_token_cookie).to eq("fake-refresh-token")
          end

          it "sets cookies for basic user info" do
            user_from_saml_account_user_info = {
              locale: user_from_saml_account.locale,
              account_aggregate_id: user_from_saml_account.account.aggregate_id,
              employee_aggregate_id: user_from_saml_account.aggregate_id
            }.to_json

            encoded_info = ::Base64.strict_encode64(user_from_saml_account_user_info)

            subject

            basic_user_info_cookie = response.cookies["cultureamp.user-data"]
            expect(basic_user_info_cookie).not_to be_nil
            expect(basic_user_info_cookie).to eq(encoded_info)

            decoded_basic_user_info = ::Base64.decode64(basic_user_info_cookie)
            parsed_basic_user_info = JSON.parse(decoded_basic_user_info)

            expect(parsed_basic_user_info["locale"]).to eq(user_from_saml_account.locale)
            expect(parsed_basic_user_info["account_aggregate_id"]).to eq(user_from_saml_account.account.aggregate_id)
            expect(parsed_basic_user_info["employee_aggregate_id"]).to eq(user_from_saml_account.aggregate_id)
          end

          it "sets cookie for user info by region" do
            user_from_saml_account_user_info = {
              locale: user_from_saml_account.locale,
              employee_aggregate_id: user_from_saml_account.aggregate_id,
              account_aggregate_id: user_from_saml_account.account.aggregate_id
            }.to_json

            encoded_info = ::Base64.urlsafe_encode64(user_from_saml_account_user_info, padding: false)

            subject

            user_info_cookie_by_region = response.cookies["cultureamp.#{cookie_env_name}.user-data"]
            expect(user_info_cookie_by_region).not_to be_nil
            expect(user_info_cookie_by_region).to eq(encoded_info)

            decoded_user_info = ::Base64.decode64(user_info_cookie_by_region)
            parsed_user_info = JSON.parse(decoded_user_info)

            expect(parsed_user_info["locale"]).to eq(user_from_saml_account.locale)
            expect(parsed_user_info["account_aggregate_id"]).to eq(user_from_saml_account.account.aggregate_id)
            expect(parsed_user_info["employee_aggregate_id"]).to eq(user_from_saml_account.aggregate_id)
          end

          context "when jwt feature flag is off" do
            before do
              allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)
            end

            it "does not set JWT cookies" do
              subject

              expect(response.cookies["cultureamp.#{cookie_env_name}.token"]).to be_nil
              expect(response.cookies["cultureamp.#{cookie_env_name}.refresh-token"]).to be_nil
            end

            it "still sets basic user info cookie" do
              subject
              expect(response.cookies["cultureamp.user-data"]).not_to be_nil
            end

            it "still sets user info cookie by region" do
              subject
              expect(response.cookies["cultureamp.#{cookie_env_name}.user-data"]).not_to be_nil
            end
          end

          context "when passwordless login fails" do
            let(:passwordless_result) { Failure(:error) }

            it "should raise an exception" do
              expect {
                subject
              }.to raise_error("Failed to generate JWT for SAML user")
            end
          end
        end
      end

      context "and the IdP supplies multiple certificates" do
        let(:fingerprint) { nil }
        let(:cert) { nil }
        let(:cert_multi) do
          {
            signing: [
              file_fixture(SAML::CERTMULTI_A_FILENAME).read,
              file_fixture(SAML::CERTMULTI_B_FILENAME).read
            ]
          }
        end
        let!(:user_from_saml_account) { FactoryBot.create(:user, email: saml_name_id, account: saml_account) }

        let(:saml_name_id) { "<EMAIL>" }
        let(:saml_name_id_format) { "urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress" }
        let(:saml_expiry_time) { "2021-08-23T05:08:21.817Z" }
        let(:saml_response_body) {
          data = SAML::ResponseFixture.new(name_id: saml_name_id,
                                           name_id_format: saml_name_id_format,
                                           expiry_time: saml_expiry_time)
          ERB.new(file_fixture(SAML::RESPONSE_TEMPLATE_MULTICERT_FILENAME).read).result(data.get_binding)
        }

        it "signs in the user and redirects" do
          subject

          expect(controller.current_user).to eq user_from_saml_account
          expect(response.status).to eq 302
          expect(response_redirect_uri.path).to eq "/"
        end

        it "tracks the sign-in event" do
          expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
            user_aggregate_id: user_from_saml_account.aggregate_id,
            event_type: "User Signed In",
            event_properties: hash_including(
              "Authentication method" => "SAML",
              "Failed attempts" => 0,
              "Authentication system" => "Murmur"
            )
          ))

          subject
        end

        it "sets an Amplitude Device ID in the session" do
          subject

          expect(session[:amplitude_device_id]).not_to be(nil)
        end

        context "when RelayState is provided" do
          let(:relay_state) { "/reports/insight/42" }

          it "redirects to the path provided via RelayState" do
            subject

            expect(response_redirect_uri.path).to eq relay_state
          end
        end

        context "no response params" do
          let(:saml_response_body) { nil }

          it "redirects to the sign in page" do
            subject

            expect(response).to redirect_to(saml_signin_path(saml_account.subdomain))
          end
        end

        context "when the response is invalid" do
          before do
            subject
          end
          let(:saml_response_body) { "asdf" }

          it_behaves_like "an http unauthorized error"
        end

        context "when the response name id format is invalid" do
          before do
            subject
          end
          let(:saml_name_id_format) { "foobar" }

          it_behaves_like "an http unauthorized error"
        end

        context "when the user does not exist in the account" do
          before do
            subject
          end
          let!(:user_from_saml_account) { FactoryBot.create(:user, email: "<EMAIL>", account: saml_account) }

          it_behaves_like "an http forbidden error"
        end

        context "when the SAML response has expired" do
          before do
            Timecop.unfreeze
            subject
          end

          it_behaves_like "an http unauthorized error"
        end

        context "when authentication response fusion auth sign in failed" do
          let(:authenticate_response) { [:ok, user_from_saml_account, false] }
          before do
            expect(SAML::Authenticator).to receive(:authenticate).and_return(authenticate_response)
            allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
          end

          it "should raise an exception" do
            expect {
              subject
            }.to raise_error("Failed to authenticate via FusionAuth for SAML user")
          end

          it "logs an error message" do
            expect(Rails.logger).to receive(:error).with("Failed to authenticate via FusionAuth for SAML user: #{user_from_saml_account.email}")

            begin
              subject
            rescue
              # Ignore the exception
            end
          end
        end

        context "when authentication response fusion auth sign in succeeded" do
          let(:authenticate_response) { [:ok, user_from_saml_account, true] }
          let(:passwordless_result) { Success(jwt: "fake-jwt", refresh_token: "fake-refresh-token") }
          let(:passwordless_login) { instance_double(Authentication::Commands::FusionAuthPasswordlessLogin) }

          let(:payload) { {"effectiveUserId" => user_from_saml_account.aggregate_id, "accountId" => user_from_saml_account.account.aggregate_id, "realUserId" => user_from_saml_account.aggregate_id} }
          let(:web_gateway_equivalent_key) { Rails.application.config.test_jwt_private_key }
          let(:token) { JWT.encode(payload, web_gateway_equivalent_key, "RS256") }
          let(:headers) { {"X-CA-FA-Authorization" => "Bearer #{token}"} }

          before do
            @request.host = "#{user_from_saml_account.account.subdomain}.#{Murmur::CULTURE_AMP_DOMAIN}"
            @request.headers.merge!(headers)

            expect(SAML::Authenticator).to receive(:authenticate).and_return(authenticate_response)
            allow(Authentication::Commands::FusionAuthPasswordlessLogin).to receive(:new).and_return(passwordless_login)
            allow(passwordless_login).to receive(:call).and_return(passwordless_result)
            allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
          end

          it "tracks the sign-in event as FusionAuth" do
            expect(Jobs::AnalyticsJob).to receive(:enqueue).with(hash_including(
              user_aggregate_id: user_from_saml_account.aggregate_id,
              event_type: "User Signed In",
              event_properties: hash_including(
                "Authentication method" => "SAML",
                "Failed attempts" => 0,
                "Authentication system" => "FusionAuth"
              )
            ))

            subject
          end

          it "sets cookies for fa jwt and refresh token" do
            subject

            fa_jwt_cookie_name = "cultureamp.#{cookie_env_name}.token"
            fa_refresh_token_cookie_name = "cultureamp.#{cookie_env_name}.refresh-token"

            fa_jwt_cookie = response.cookies[fa_jwt_cookie_name]
            fa_refresh_token_cookie = response.cookies[fa_refresh_token_cookie_name]

            expect(fa_jwt_cookie).to eq("fake-jwt")
            expect(fa_refresh_token_cookie).to eq("fake-refresh-token")
          end

          it "sets cookies for basic user info" do
            user_from_saml_account_user_info = {
              locale: user_from_saml_account.locale,
              account_aggregate_id: user_from_saml_account.account.aggregate_id,
              employee_aggregate_id: user_from_saml_account.aggregate_id
            }.to_json

            encoded_info = ::Base64.strict_encode64(user_from_saml_account_user_info)

            subject

            basic_user_info_cookie = response.cookies["cultureamp.user-data"]
            expect(basic_user_info_cookie).not_to be_nil
            expect(basic_user_info_cookie).to eq(encoded_info)

            decoded_basic_user_info = ::Base64.decode64(basic_user_info_cookie)
            parsed_basic_user_info = JSON.parse(decoded_basic_user_info)

            expect(parsed_basic_user_info["locale"]).to eq(user_from_saml_account.locale)
            expect(parsed_basic_user_info["account_aggregate_id"]).to eq(user_from_saml_account.account.aggregate_id)
            expect(parsed_basic_user_info["employee_aggregate_id"]).to eq(user_from_saml_account.aggregate_id)
          end

          it "sets cookie for user info by region" do
            user_from_saml_account_user_info = {
              locale: user_from_saml_account.locale,
              employee_aggregate_id: user_from_saml_account.aggregate_id,
              account_aggregate_id: user_from_saml_account.account.aggregate_id
            }.to_json

            encoded_info = ::Base64.urlsafe_encode64(user_from_saml_account_user_info, padding: false)

            subject

            user_info_cookie_by_region = response.cookies["cultureamp.#{cookie_env_name}.user-data"]
            expect(user_info_cookie_by_region).not_to be_nil
            expect(user_info_cookie_by_region).to eq(encoded_info)

            decoded_user_info = ::Base64.decode64(user_info_cookie_by_region)
            parsed_user_info = JSON.parse(decoded_user_info)

            expect(parsed_user_info["locale"]).to eq(user_from_saml_account.locale)
            expect(parsed_user_info["account_aggregate_id"]).to eq(user_from_saml_account.account.aggregate_id)
            expect(parsed_user_info["employee_aggregate_id"]).to eq(user_from_saml_account.aggregate_id)
          end

          context "when jwt feature flag is off" do
            before do
              allow(controller).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)
            end

            it "does not set JWT cookies" do
              subject

              expect(response.cookies["cultureamp.#{cookie_env_name}.token"]).to be_nil
              expect(response.cookies["cultureamp.#{cookie_env_name}.refresh-token"]).to be_nil
            end

            it "still sets basic user info cookie" do
              subject
              expect(response.cookies["cultureamp.user-data"]).not_to be_nil
            end

            it "still sets user info cookie by region" do
              subject
              expect(response.cookies["cultureamp.#{cookie_env_name}.user-data"]).not_to be_nil
            end
          end

          context "when passwordless login fails" do
            let(:passwordless_result) { Failure(:error) }

            it "should raise an exception" do
              expect {
                subject
              }.to raise_error("Failed to generate JWT for SAML user")
            end

            it "logs an error message" do
              expect(Rails.logger).to receive(:error).with("Failed to generate JWT for SAML user: #{user_from_saml_account.email}")

              begin
                subject
              rescue
                # Ignore the exception
              end
            end
          end
        end
      end
    end
  end
end
