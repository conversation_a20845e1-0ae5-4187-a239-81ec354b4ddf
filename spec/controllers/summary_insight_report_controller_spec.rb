require "rails_helper"

RSpec.describe SummaryInsightReportController do
  let(:account) do
    FactoryBot.create(
      :account_with_master,
      flags:
        {
          Flags::DEFAULT_INSIGHT_REPORT => default_insight_report,
          Flags::SUMMARY_INSIGHT_REPORT => summary_insight_report
        }
    )
  end
  let(:survey) { FactoryBot.create(:rich_survey, account: account) }
  let(:default_insight_report) { Flags::DISABLED }
  let(:summary_insight_report) { Flags::ENABLED }
  let(:admin) { FactoryBot.create(:admin, account: account) }
  let!(:index_factor) { FactoryBot.create(:factor, survey: survey, index_factor: true) }

  before do
    survey.survey_to_questions.each do |stq|
      stq.update_attributes!(factors: [index_factor])
    end
  end

  describe "#show", vcr: true do
    describe "default behaviour" do
      before do
        sign_in admin
        get :show, params: {survey_id: survey.id, report_id: "admin"}
      end

      it_behaves_like "an ok response"
    end

    describe "json payload requested" do
      let(:comparisons_data) do
        {
          comparisons: {
            name: "My benchmark",
            indexFactorScore: 70,
            type: :benchmark
          }
        }
      end
      let(:index_factor_data) do
        {
          indexFactorScore: {
            favorable: 74,
            neutral: 9,
            unfavorable: 17,
            significant: true
          }
        }
      end
      let(:recommended_question_id) { "5ce08cd7-277c-4050-bbc3-c2389b350392" }
      let(:recommended_questions_data) do
        {
          recommendedQuestions: [
            {
              id: recommended_question_id,
              text: "The leaders at Hooli demonstrate that people are important to the company success",
              factorName: "Leadership",
              score: {
                favorable: 59,
                unfavorable: 16,
                neutral: 25
              },
              delta: -15
            }
          ]
        }
      end
      let(:highest_question_id) { "cbbb13c2-f150-412a-bb59-f4a79a67a8e0" }
      let(:highest_questions_data) do
        {
          highestQuestions: [
            {
              id: highest_question_id,
              text: "My manager (or someone in management) has shown a genuine interest in my career aspirations",
              factorName: "Learning & Development",
              score: {
                favorable: 91,
                unfavorable: 3,
                neutral: 6
              }
            }
          ]
        }
      end

      let(:action) do
        ActionFrameworks::Action.with(
          aggregate_id: 2,
          title: "title",
          description: "description",
          action_framework: "action_framework",
          source_inspiration: "source_inspiration",
          focus_areas: [
            ActionFrameworks::FocusArea.with(question_id: "cbbb13c2-f150-412a-bb59-f4a79a67a8e0")
          ]
        )
      end

      before do
        allow_any_instance_of(Reports::SummaryInsightReport::Comparisons).to receive(:data).and_return(comparisons_data)
        allow_any_instance_of(Reports::SummaryInsightReport::RecommendedQuestions).to receive(:data).and_return(recommended_questions_data)
        allow_any_instance_of(Reports::SummaryInsightReport::HighestRatingQuestions).to receive(:data).and_return(highest_questions_data)
        allow_any_instance_of(Reports::SummaryInsightReport::DisplayedFactorScore).to receive(:data).and_return(index_factor_data)
        allow_any_instance_of(ActionFrameworks::ReaderService).to receive(:actions).and_return(Monad::Result.ok([action]))

        request.headers["accept"] = "application/json"
        sign_in admin
        VCR.use_cassette("permissions_service/permit") do
          get :show, params: {survey_id: survey.id, report_id: "admin"}
        end
      end

      it_behaves_like "an ok response"

      it "sets default action data" do
        expect(sir_data["actionFrameworkEnabled"]).to eq(true)
        expect(sir_data["reportOwner"]).to eq(false)

        recommended_question = sir_data["recommendedQuestions"][0]
        expect(recommended_question["hasActions"]).to eq(false)
        expect(recommended_question["_links"]).to eq(
          "viewAction" => "/surveys/#{survey.id}/reports/admin/take_action?comparison=.overall&filters=&locale=en",
          "createAction" => "/surveys/#{survey.id}/reports/admin/take_action/questions/#{recommended_question_id}/create_action"
        )

        highest_question = sir_data["highestQuestions"][0]
        expect(highest_question["hasActions"]).to eq(true)
        expect(highest_question["_links"]).to eq(
          "viewAction" => "/surveys/#{survey.id}/reports/admin/take_action?comparison=.overall&filters=&locale=en",
          "createAction" => "/surveys/#{survey.id}/reports/admin/take_action/questions/#{highest_question_id}/create_action"
        )
      end
    end

    let(:sir_data) { JSON.parse(response.body)["reportData"]["summaryInsightReportData"] }

    describe "when the actionframework is disabled for the report id", vcr: true do
      let(:report) { FactoryBot.create(:report, survey: survey) }
      before do
        report.update_attributes(af_enabled: false)
        sign_in admin
        VCR.use_cassette("permissions_service/permit") do
          get :show, params: {survey_id: survey.id, report_id: report.id.to_s}, format: :json
        end
      end

      it "sets actionFrameworkEnabled to false" do
        expect(sir_data["actionFrameworkEnabled"]).to eq(false)
      end
    end

    describe "when the report is a multi demographic report", vcr: true do
      let(:multi_demographic_report) { FactoryBot.create(:multi_demographic_report, survey: survey) }
      let(:select_option_ids) { multi_demographic_report.report_scope.map { |_, v| v.first } }

      before do
        allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
          .to receive(:has_customer_access_to_multi_demographic_reports?)
          .and_return(true)
        allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
          .to receive(:has_access_to_view_multi_demographic_reports?)
          .and_return(true)

        FactoryBot.create(:report_access_grant, report: multi_demographic_report, report_consumer: admin, select_option_ids: select_option_ids)

        sign_in admin
        VCR.use_cassette("permissions_service/permit") do
          get :show, params: {survey_id: survey.id, report_id: multi_demographic_report.id.to_s}, format: :json
        end
      end

      it "returns false for actionFrameworkEnabled" do
        expect(sir_data["actionFrameworkEnabled"]).to eq(false)
      end
    end

    describe "handles the report type user preference" do
      let(:default_insight_report) { Flags::ENABLED }
      let(:overwrite_redirection) { false }
      let(:demo_account) { false }

      context "when no preference has been set by the user" do
        before do
          sign_in admin
          get :show, params: {survey_id: survey.id, report_id: "admin", overwrite_redirection: overwrite_redirection}
        end

        context "and default is summary insight report" do
          let(:default_insight_report) { Flags::DISABLED }
          it_behaves_like "an ok response"
        end
      end
    end
  end
end
