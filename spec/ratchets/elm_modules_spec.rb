RSpec.describe "elm modules ratchet" do
  NUMBER_OF_ELM_MODULES = 560

  it "number of Elm modules" do
    expect(current_elm_modules_count).to eq(NUMBER_OF_ELM_MODULES), explanation(current_elm_modules_count)
  end

  def current_elm_modules_count
    @current_elm_modules_count ||= begin
      `find app/client -type f -name '*.elm' -not -path '*/.elm*' | wc -l`.strip.to_i
    end
  end

  def explanation(current)
    if current > NUMBER_OF_ELM_MODULES
      <<~EXPLANATION
        Hello, it looks like you have added a new Elm module. We’re trying to limit the use
        of Elm in Murmur in favor of moving to React.

        Old count: #{NUMBER_OF_ELM_MODULES}
        New count: #{current}

        How about converting it to React? If conversion is difficult or impractical, you can
        update the constant above to match the new count.
      EXPLANATION
    else
      <<~EXPLANATION
        Hello, it looks like you've removed some of our Elm modules!

        Old count: #{NUMBER_OF_ELM_MODULES}
        New count: #{current}

        Great! Please decrement the constant above.
      EXPLANATION
    end
  end
end
