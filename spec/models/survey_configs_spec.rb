require "rails_helper"

# SurveyConfigs is included in Survey
RSpec.describe Survey do
  let(:account) { FactoryBot.build(:account_without_users) }
  subject(:survey) { Survey.create!(name: "Survey", account: account) }

  def mock_LD_flag_for_account(flag, value)
    allow_any_instance_of(FeatureFlags::Queries::ValueForAccount)
      .to receive(:call)
      .with(
        account_aggregate_id: anything,
        flag_name: flag,
        fallback_value: anything
      ).and_return(value)
  end

  def mock_LD_flag_for_account_and_survey(flag, value)
    allow_any_instance_of(FeatureFlags::Queries::ValueForAccount)
      .to receive(:call)
      .with(
        account_aggregate_id: anything,
        flag_name: flag,
        fallback_value: anything
      ).and_return(value)

    allow_any_instance_of(FeatureFlags::Queries::ValueForSurvey)
      .to receive(:call)
      .with(
        survey: anything,
        flag_name: flag,
        fallback_value: anything
      ).and_return(value)
  end

  def mock_hierarchy_reports_cutover(value)
    mock_LD_flag_for_account(FeatureFlags::Flags::HIERARCHY_REPORTS_TOGGLE_CUTOVER, value)
  end

  def mock_hierarchy_reports_toggle(value)
    mock_LD_flag_for_account_and_survey(FeatureFlags::Flags::HIERARCHY_REPORTS, value)
  end

  describe "#effectiveness_360_employee_driven?" do
    subject { survey.effectiveness_360_employee_driven? }

    it "returns false" do
      expect(subject).to eq(false)
    end

    context "for a lifecycle survey" do
      let(:survey) { Survey.new(name: "Survey", account: account, type: :onboard) }

      it "returns false" do
        expect(subject).to eq(false)
      end
    end

    context "for a three sixty survey" do
      let(:survey) { Survey.new(name: "Survey", account: account, type: :three_sixty) }

      it "returns false by default" do
        expect(subject).to eq(false)
      end

      context "with employee driven config flag set" do
        before do
          survey.assign_config(Configs::EFFECTIVENESS_360_EMPLOYEE_DRIVEN, Configs::ENABLED)
        end

        it "returns true" do
          expect(subject).to eq(true)
        end
      end
    end
  end

  describe "#likert_scale_color_schema" do
    it "returns 'classical' by default" do
      expect(subject.likert_scale_color_schema).to eq("classical")
    end

    it "returns the default" do
      expect(subject.likert_scale_color_schema).to eq(Configs::CAPTURE_LIKERT_DEFAULT_COLOR_SCHEMA)
    end

    context "when survey is configured to use 'blue'" do
      before do
        survey.assign_config(Configs::CAPTURE_LIKERT_COLOR_SCHEMA, "blue")
      end

      it "returns 'blue'" do
        expect(subject.likert_scale_color_schema).to eq("blue")
      end
    end

    context "when account is configured to use 'blue'" do
      before do
        survey.account.assign_config(Configs::CAPTURE_LIKERT_COLOR_SCHEMA, "blue")
      end

      it "returns 'blue'" do
        expect(subject.likert_scale_color_schema).to eq("blue")
      end
    end
  end

  describe "#full_workflow?" do
    subject { survey.full_workflow? }

    it "returns true by default" do
      expect(subject).to eq(true)
    end

    context "with lifecycle workflow config flag set to basic" do
      before do
        survey.assign_config(Configs::LIFECYCLE_WORKFLOW, Configs::LIFECYCLE_WORKFLOW_BASIC)
      end

      it "returns false" do
        expect(subject).to eq(false)
      end
    end
  end

  describe "#coached?" do
    subject { survey.coached? }

    it "returns true by default" do
      expect(subject).to eq(true)
    end

    context "with effectiveness 360 coached config flag disabled" do
      before do
        survey.assign_config(Configs::EFFECTIVENESS_360_COACHED, Configs::DISABLED)
      end

      it "returns false" do
        expect(subject).to eq(false)
      end
    end
  end

  describe "#valid_lifecycle_participant?" do
    subject { survey.valid_lifecycle_participant?(user) }
    let(:user) { User.new }

    context "for a lifecycle survey" do
      context "for a three sixty survey" do
        let(:survey) { FactoryBot.build(:three_sixty_survey) }

        context "when admin-driven" do
          before { expect(survey).to receive(:effectiveness_360_employee_driven?).and_return(false) }

          it "there is no participant filtering" do
            expect(subject).to eq true
          end
        end

        context "when employee-driven" do
          before { expect(survey).to receive(:effectiveness_360_employee_driven?).and_return(true) }

          it "there is participant filtering" do
            expect(subject).to eq false
          end
        end
      end

      context "for a different type of lifecycle survey" do
        let(:survey) { FactoryBot.build(:onboard_survey) }

        before do
          FactoryBot.create(:templates_survey, :with_onboard)
        end

        it "returns true - there is no participant filtering" do
          expect(subject).to eq true
        end
      end
    end

    context "for a non-lifecycle survey" do
      let(:survey) { FactoryBot.build(:survey) }

      it "returns false" do
        expect(subject).to eq false
      end
    end
  end

  describe "powerpoint_export_enabled?" do
    subject(:survey) { FactoryBot.create(:basic_survey) }

    context "when the feature flag is enabled" do
      before { survey.assign_flag!(Flags::POWERPOINT_EXPORT, Flags::ENABLED) }

      context "when the powerpoint template is set to none" do
        before { survey.set_powerpoint_template!(:none) }

        it { is_expected.not_to be_powerpoint_export_enabled }
      end

      context "when the powerpoint template is set to Engagement" do
        before { survey.set_powerpoint_template!(:engagement) }

        it { is_expected.to be_powerpoint_export_enabled }
      end
    end

    context "when the feature flag is disabled" do
      before { survey.assign_flag!(Flags::POWERPOINT_EXPORT, Flags::DISABLED) }

      context "when the powerpoint template is set to none" do
        before { survey.set_powerpoint_template!(:none) }

        it { is_expected.not_to be_powerpoint_export_enabled }
      end

      context "when the powerpoint template is set to Engagement" do
        before { survey.set_powerpoint_template!(:engagement) }

        it { is_expected.not_to be_powerpoint_export_enabled }
      end
    end

    context "when the feature flag is not set" do
      context "when the powerpoint template is set to none" do
        before { survey.set_powerpoint_template!(:none) }

        it { is_expected.not_to be_powerpoint_export_enabled }
      end

      context "when the powerpoint template is set to Engagement" do
        before { survey.set_powerpoint_template!(:engagement) }

        it { is_expected.to be_powerpoint_export_enabled }
      end
    end
  end

  describe "#show_question_benchmark_information?" do
    context "for surveys using :engagement_survey template" do
      it "returns false by default" do
        snapshot_survey = FactoryBot.create(:basic_survey, template_type: :engagement_survey)
        expect(snapshot_survey.show_question_benchmark_information?).to be_falsey
        snapshot_survey.assign_flag!(Flags::SHOW_BENCHMARKABLE_QUESTIONS, Flags::ENABLED)
        expect(snapshot_survey.show_question_benchmark_information?).to be_truthy
      end
    end

    context "for surveys using another template" do
      it "always return false" do
        snapshot_survey = FactoryBot.create(:basic_survey, template_type: :other_template)
        expect(snapshot_survey.show_question_benchmark_information?).to be_falsey
        snapshot_survey.assign_flag!(Flags::SHOW_BENCHMARKABLE_QUESTIONS, Flags::ENABLED)
        expect(snapshot_survey.show_question_benchmark_information?).to be_falsey
      end
    end
  end

  describe "#allow_emails?" do
    it "emails are allowed for 360 surveys by default" do
      survey = Survey.new(type: :three_sixty)
      expect(survey.allow_emails?).to eq(true)
    end

    it "emails are not allowed for 360 surveys if send_three_sixty_email is disabled" do
      survey = Survey.create!(type: :three_sixty, name: "no emails")
      survey.assign_config!(Configs::SEND_THREE_SIXTY_EMAILS, Flags::DISABLED)
      expect(survey.allow_emails?).to eq(false)
    end

    it "emails are allowed for engagement surveys by default" do
      survey = Survey.new(type: :engagement)
      expect(survey.allow_emails?).to eq(true)
    end

    it "emails are not allowed for engagement surveys if send_engagement_emails is disabled" do
      survey = Survey.create!(type: :engagement, name: "no emails")
      survey.assign_config!(Configs::SEND_ENGAGEMENT_EMAILS, Flags::DISABLED)
      expect(survey.allow_emails?).to eq(false)
    end
  end

  describe "#show_reviwer_names?" do
    context "when config is not set" do
      it "returns false by default" do
        expect(survey.show_reviewer_names?).to eq(false)
      end
    end

    context "when config is set on the survey" do
      it "returns true if survey config is true" do
        survey.assign_config!(Configs::SHOW_THREE_SIXTY_REVIEWER_NAMES, Configs::ENABLED)

        expect(survey.show_reviewer_names?).to eq(true)
      end

      it "returns false if survey config is false" do
        survey.assign_config!(Configs::SHOW_THREE_SIXTY_REVIEWER_NAMES, Configs::DISABLED)

        expect(survey.show_reviewer_names?).to eq(false)
      end
    end

    context "when config is set on the account" do
      it "returns true if account config is true" do
        account.assign_config!(Configs::SHOW_THREE_SIXTY_REVIEWER_NAMES, Configs::ENABLED)

        expect(survey.show_reviewer_names?).to eq(true)
      end

      it "returns false if account config is false" do
        account.assign_config!(Configs::SHOW_THREE_SIXTY_REVIEWER_NAMES, Configs::DISABLED)

        expect(survey.show_reviewer_names?).to eq(false)
      end
    end
  end

  describe "#allow_grouping_of_participants?" do
    it "returns true by default" do
      survey = Survey.new
      expect(survey.allow_grouping_of_participants?).to eq true
    end

    it "returns true if explicitly enabled for the survey" do
      survey = Survey.create!(name: "grouping enabled")
      survey.assign_config!(Configs::ALLOW_GROUP_PARTICIPANTS, Configs::ENABLED)
      expect(survey.allow_grouping_of_participants?).to eq true
    end

    it "returns false if explicitly disabled for the survey" do
      survey = Survey.create!(name: "grouping disabled")
      survey.assign_config!(Configs::ALLOW_GROUP_PARTICIPANTS, Configs::DISABLED)
      expect(survey.allow_grouping_of_participants?).to eq false
    end

    it "returns true if explicitly enabled for the account" do
      survey = Survey.create!(name: "account grouping enabled", account: account)
      account.assign_config!(Configs::ALLOW_GROUP_PARTICIPANTS, Configs::ENABLED)
      expect(survey.allow_grouping_of_participants?).to eq true
    end

    it "returns false if explicitly disabled for the account" do
      survey = Survey.create!(name: "account grouping disabled", account: account)
      account.assign_config!(Configs::ALLOW_GROUP_PARTICIPANTS, Configs::DISABLED)
      expect(survey.allow_grouping_of_participants?).to eq false
    end

    it "returns false if explicitly disabled for the survey, but enabled for the account" do
      survey = Survey.create!(name: "account grouping disabled", account: account)
      account.assign_config!(Configs::ALLOW_GROUP_PARTICIPANTS, Configs::ENABLED)
      survey.assign_config!(Configs::ALLOW_GROUP_PARTICIPANTS, Configs::DISABLED)
      expect(survey.allow_grouping_of_participants?).to eq false
    end

    it "returns true if the account has a people count below the limit" do
      expect(account).to receive_message_chain(:people, :associated, :count).and_return(1_000)
      survey = Survey.create!(name: "account people limit below", account: account)
      expect(survey.allow_grouping_of_participants?).to eq true
    end

    it "returns false if the account has a people count above the limit" do
      expect(account).to receive_message_chain(:people, :associated, :count).and_return(8_000)
      survey = Survey.create!(name: "account people limit above", account: account)
      expect(survey.allow_grouping_of_participants?).to eq false
    end
  end

  describe "#report_owner_enabled?" do
    let(:survey) do
      Survey.new(
        name: "Survey",
        account: account,
        flags:
          {
            Flags::ACTION_DRIVER => action_driver_enabled
          }
      )
    end

    subject { survey.report_owner_enabled? }

    context "when action driver is disabled" do
      let(:action_driver_enabled) { Flags::DISABLED }

      it "returns false" do
        expect(subject).to eq(false)
      end
    end

    context "when action driver is enabled" do
      let(:action_driver_enabled) { Flags::ENABLED }

      it "returns true" do
        expect(subject).to eq(true)
      end
    end
  end

  describe "#action_framework_flag_enabled?" do
    before do
      expect_any_instance_of(FeatureFlags::Queries::ValueForSurvey)
        .to receive(:call)
        .with(
          survey: survey,
          flag_name: FeatureFlags::Flags::MODULE_TOGGLES_ACTION_FRAMEWORK,
          fallback_value: true
        )
        .and_return(action_framework_flag)
    end

    context "action framework LD flag is ON" do
      let(:action_framework_flag) { true }
      it "returns true" do
        expect(survey.action_framework_flag_enabled?).to eq(true)
      end
    end

    context "action framework LD flag is OFF" do
      let(:action_framework_flag) { false }
      it "returns true" do
        expect(survey.action_framework_flag_enabled?).to eq(false)
      end
    end
  end

  describe "#action_framework_enabled?" do
    let(:mocked_action_framework_feature_flag) { true }
    let(:report_has_af_enabled) { true }

    let(:report) { FactoryBot.create(:report, survey: survey, af_enabled: report_has_af_enabled) }
    before do
      allow_any_instance_of(FeatureFlags::Queries::ValueForSurvey)
        .to receive(:call)
        .with(
          survey: survey,
          flag_name: FeatureFlags::Flags::MODULE_TOGGLES_ACTION_FRAMEWORK,
          fallback_value: true
        )
        .and_return(mocked_action_framework_feature_flag)
    end

    context "when the report is a multi-demographic report" do
      let(:report) { FactoryBot.create(:multi_demographic_report, survey: survey) }

      it "returns false" do
        expect(survey.action_framework_enabled?(report)).to eq(false)
      end
    end

    context "when survey period type is neither snapshot nor adhoc" do
      before do
        allow(survey).to receive(:survey_period_type).and_return(:other_type)
      end

      it "returns false" do
        expect(survey.action_framework_enabled?).to eq(false)
      end
    end

    context "when survey period type is snapshot" do
      before do
        allow(survey).to receive(:survey_period_type).and_return(:snapshot)
      end

      context "when action_framework_flag is enabled" do
        it "returns true when no report is provided" do
          expect(survey.action_framework_enabled?).to eq(true)
        end

        it "returns true when report.af_enabled is true" do
          expect(survey.action_framework_enabled?(report)).to eq(true)
        end
      end
      context "when action_framework_flag is disabled" do
        let(:mocked_action_framework_feature_flag) { false }
        let(:report_has_af_enabled) { false }
        it "returns false" do
          expect(survey.action_framework_enabled?).to eq(false)
        end

        it "returns false when report.af_enabled is false" do
          expect(survey.action_framework_enabled?(report)).to eq(false)
        end
      end
    end

    context "when survey period type is adhoc" do
      before do
        allow(survey).to receive(:survey_period_type).and_return(:adhoc)
      end

      context "when action_framework_flag is enabled" do
        it "returns true when action_framework_flag is enabled and no report is provided" do
          expect(survey.action_framework_enabled?).to eq(true)
        end
        it "returns true when action_framework_flag is enabled and report.af_enabled is true" do
          expect(survey.action_framework_enabled?(report)).to eq(true)
        end
      end
      context "when action_framework_flag is disabled" do
        let(:mocked_action_framework_feature_flag) { false }
        let(:report_has_af_enabled) { false }

        it "returns false when action_framework_flag is disabled" do
          expect(survey.action_framework_enabled?).to eq(false)
        end
        it "returns false when report.af_enabled is false" do
          expect(survey.action_framework_enabled?(report)).to eq(false)
        end
      end
    end
  end

  describe "#hierarchy_reports_enabled?" do
    subject(:survey) { FactoryBot.create(:basic_survey) }

    context "cutover flag is off" do
      before do
        mock_hierarchy_reports_cutover(false)
      end
      context "hierarchy reports - murmur flag is on, LD flag is off" do
        before do
          survey.enable!(Flags::HIERARCHY_REPORTS)
          mock_hierarchy_reports_toggle(false)
        end
        it "returns true" do
          expect(survey.hierarchy_reports_enabled?).to eq true
        end
      end
    end
    context "cutover flag is on" do
      before do
        mock_hierarchy_reports_cutover(true)
      end
      context "hierarchy reports - murmur flag is off, LD flag is on" do
        before do
          survey.enable!(Flags::HIERARCHY_REPORTS)
          mock_hierarchy_reports_toggle(true)
        end
        it "returns true" do
          expect(survey.hierarchy_reports_enabled?).to eq true
        end
      end
    end
  end

  describe "#survey_admin_overview_page_enabled?" do
    subject { survey.survey_admin_overview_page_enabled? }

    it "returns true by default" do
      expect(subject).to eq(true)
    end

    context "with survey overview config flag enabled" do
      before do
        survey.assign_flag!(Flags::SURVEY_ADMIN_OVERVIEW_PAGE, Flags::ENABLED)
      end

      it "returns true" do
        expect(subject).to eq(true)
      end
    end
  end

  describe "#show_results_to_action_guide_text_ms_teams?" do
    context "when config is not set" do
      it "returns true by default" do
        expect(survey.show_results_to_action_guide_text_ms_teams?).to eq(true)
      end
    end

    context "when config is set on the survey" do
      it "returns true if survey config is Enabled" do
        survey.assign_config!(Configs::SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS, Configs::ENABLED)

        expect(survey.show_results_to_action_guide_text_ms_teams?).to eq(true)
      end

      it "returns false if survey config is Disabled" do
        survey.assign_config!(Configs::SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS, Configs::DISABLED)

        expect(survey.show_results_to_action_guide_text_ms_teams?).to eq(false)
      end
    end

    context "when config is set on the account" do
      it "returns true if account config is Enabled" do
        account.assign_config!(Configs::SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS, Configs::ENABLED)

        expect(survey.show_results_to_action_guide_text_ms_teams?).to eq(true)
      end

      it "returns false if account config is disabled" do
        account.assign_config!(Configs::SHOW_RESULT_TO_ACTION_GUIDE_TEXT_MS_TEAMS, Configs::DISABLED)

        expect(survey.show_results_to_action_guide_text_ms_teams?).to eq(false)
      end
    end
  end
end
