require "rails_helper"

RSpec.describe Props::ReportAccessProps do
  let(:account) { FactoryBot.create(:account) }
  let(:report_type) { ReportDataTypes::INSIGHT }
  let(:survey) do
    FactoryBot.create(
      :survey,
      account: account,
      flags: {
        Flags::REPORT_SHARING => Flags::ENABLED,
        Flags::ACTION_DASHBOARD_SHARING => action_dashboard_sharing_enabled
      }
    )
  end
  let(:action_dashboard_sharing_enabled) { Flags::ENABLED }
  let(:user) { FactoryBot.create(:user, account: account) }
  let(:survey_admin) { FactoryBot.create(:survey_admin, survey: survey, account: account) }
  let(:hr_business_partner) { FactoryBot.create(:hr_business_partner, account: account) }

  let(:report_1) { FactoryBot.create(:report, name: "Report 1", survey: survey) }
  let(:report_2) { FactoryBot.create(:report, name: "Report 2", survey: survey) }

  let(:standard_report_path) { "standard report path" }
  let(:action_plan_path) { "action plan path" }
  let(:action_dashboard_path) { "action dashboard path" }
  let(:participation_path) { "participation path" }

  let(:anchor) { AllResultsReportFilter::VALUE }

  let(:paths) do
    instance_double(
      ReportPaths::ReportSharing,
      polymorphic_path: standard_report_path,
      survey_action_dashboard_application_path: action_dashboard_path,
      survey_report_new_action_framework_report_path: action_plan_path
    )
  end

  describe "#available_reports" do
    context "when I am a report owner" do
      let!(:report_access_grant_1) { FactoryBot.create(:report_access_grant, report: report_1, report_consumer: user, action_framework_role: ReportAccessGrant::REPORT_OWNER) }
      let!(:report_access_grant_2) { FactoryBot.create(:report_access_grant, report: report_2, report_consumer: user, action_framework_role: ReportAccessGrant::VIEWER) }
      let(:report_access_grant_finder) { ReportAccessGrantFinder.new(user, survey.id, report_1.id, anchor) }
      subject { described_class.new(paths, user, report_access_grant_finder, report_type).to_hash }

      it "outputs the current report" do
        expect(subject[:current]).to eq "Report 1"
      end

      context "if Action Dashboard sharing FF is enabled" do
        it "includes report owner action dashboards" do
          expect(subject[:available]).to eq(
            [
              {url: standard_report_path, current: true, name: "Report 1"},
              {url: standard_report_path, current: false, name: "Report 2"},
              {url: action_dashboard_path, current: true, name: "Action Dashboard Report 1"}
            ]
          )
        end
        context "when there are multi demographic reports" do
          let(:report_1) { FactoryBot.create(:multi_demographic_report, name: "Report 1", survey: survey) }
          let(:report_2) { FactoryBot.create(:multi_demographic_report, name: "Report 2", survey: survey) }

          let!(:report_access_grant_1) {
            FactoryBot.create(
              :report_access_grant,
              report: report_1,
              report_consumer: user,
              action_framework_role: ReportAccessGrant::REPORT_OWNER,
              select_option_ids: report_1.report_scope.map { |_, v| v.first }
            )
          }
          let!(:report_access_grant_2) {
  FactoryBot.create(
    :report_access_grant,
    report: report_2,
    report_consumer: user,
    action_framework_role: ReportAccessGrant::VIEWER,
    select_option_ids: report_2.report_scope.map { |_, v| v.first }
  )
}

          before do
            allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
              .to receive(:has_customer_access_to_multi_demographic_reports?)
              .and_return(true)
          end

          it "does not include multi demographic report owner action dashboards" do
            expect(subject[:available]).to eq(
              [
                {url: standard_report_path, current: true, name: "Report 1: #{report_access_grant_1.label}"},
                {url: standard_report_path, current: false, name: "Report 2: #{report_access_grant_2.label}"}
              ]
            )
          end
        end
      end

      context "if Action Dashboard sharing FF is disabled" do
        let(:action_dashboard_sharing_enabled) { Flags::DISABLED }

        it "does not include report owner action dashboards" do
          expect(subject[:available]).to eq(
            [
              {url: standard_report_path, current: true, name: "Report 1"},
              {url: standard_report_path, current: false, name: "Report 2"}
            ]
          )
        end
      end
    end

    context "when I am a survey admin" do
      let(:user) { survey_admin }
      let!(:report_access_grant_1) { FactoryBot.create(:report_access_grant, report: report_1, report_consumer: user, action_framework_role: ReportAccessGrant::VIEWER) }
      let(:report_access_grant_finder) { ReportAccessGrantFinder.new(user, survey.id, AdministratorReport::ID.to_s, anchor) }
      subject { described_class.new(paths, user, report_access_grant_finder, report_type).to_hash }

      it "includes the admin report, the action dashboard report and normal report" do
        expect(subject[:available]).to eq(
          [
            {url: standard_report_path, current: true, name: "Administrator Report"},
            {url: action_dashboard_path, current: false, name: "Action Dashboard"},
            {url: standard_report_path, current: false, name: "Report 1"}
          ]
        )
      end

      context "when there is a demo report" do
        let(:demo_report) { FactoryBot.create(:report, name: "Demo report", survey: survey, base_demographic_stq_id: nil) }
        let!(:report_access_grant_1) { FactoryBot.create(:report_access_grant, report: report_1, report_consumer: user, action_framework_role: ReportAccessGrant::VIEWER) }
        let(:report_access_grant_finder) { ReportAccessGrantFinder.new(user, survey.id, demo_report.id, anchor) }

        it "returns the demo report" do
          expect(subject[:available]).to eq(
            [
              {url: standard_report_path, current: true, name: "Demo report"}
            ]
          )
        end
      end
    end

    context "when I am a HR business partner" do
      let(:user) { hr_business_partner }
      let!(:report_access_grant_1) { FactoryBot.create(:report_access_grant, report: report_1, report_consumer: user, action_framework_role: ReportAccessGrant::VIEWER) }
      let(:report_access_grant_finder) { ReportAccessGrantFinder.new(user, survey.id, ActionDashboardReport::ID.to_s, anchor) }
      subject { described_class.new(paths, user, report_access_grant_finder, report_type).to_hash }

      it "includes the action dashboard grant and the normal grant" do
        expect(subject[:available]).to eq(
          [
            {url: action_dashboard_path, current: true, name: "Action Dashboard"},
            {url: standard_report_path, current: false, name: "Report 1"}
          ]
        )
      end
    end

    context "for action framework report" do
      let(:report_type) { ReportDataTypes::NEW_ACTION_FRAMEWORK }
      let(:report_1) { FactoryBot.create(:report, name: "Report 1", survey: survey, report_view: :participation) }
      let(:report_2) { FactoryBot.create(:report, name: "Report 2", survey: survey) }
      let!(:report_access_grant_1) { FactoryBot.create(:report_access_grant, report: report_1, report_consumer: user, action_framework_role: ReportAccessGrant::VIEWER) }
      let!(:report_access_grant_2) { FactoryBot.create(:report_access_grant, report: report_2, report_consumer: user, action_framework_role: ReportAccessGrant::VIEWER) }
      let(:report_access_grant_finder) { ReportAccessGrantFinder.new(user, survey.id, report_2.id, anchor) }
      subject { described_class.new(paths, user, report_access_grant_finder, report_type).to_hash }

      it "removes participation reports from the output" do
        expect(subject[:available]).to eq([{url: action_plan_path, current: true, name: "Report 2"}])
      end
    end

    context "for action framework report when report grant finder is nil" do
      let(:report_type) { ReportDataTypes::NEW_ACTION_FRAMEWORK }
      let(:report_access_grant_finder) { nil }
      subject { described_class.new(paths, user, report_access_grant_finder, report_type).to_hash }

      it "returns empty" do
        expect(subject[:available]).to eq([])
      end
    end

    context "for multi-demographic reports" do
      let(:report) {
        FactoryBot.create(
          :multi_demographic_report,
          name: "MDR",
          survey: survey,
          sharing_status: "published"
        )
      }
      let(:select_option_ids) { report.report_scope.map { |_, v| v.first } }
      let!(:grant) {
        FactoryBot.create(
          :multi_demographic_report_access_grant,
          report_consumer: user,
          report: report,
          select_option_ids: select_option_ids
        )
      }
      let(:report_access_grant_finder) { ReportAccessGrantFinder.new(user, survey.id, report.id, anchor) }

      context "when I have access to multi-demographic reports" do
        before do
          allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
            .to receive(:has_customer_access_to_multi_demographic_reports?)
            .and_return(true)
        end

        subject { described_class.new(paths, user, report_access_grant_finder, report_type).to_hash }

        it "includes multi demographic report grant" do
          expect(subject[:available]).to eq(
            [
              {url: standard_report_path, current: true, name: "MDR: here + here"}
            ]
          )
        end
      end

      context "when I do not have access to multi-demographic reports" do
        before do
          allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
            .to receive(:has_customer_access_to_multi_demographic_reports?)
            .and_return(false)
        end

        subject { described_class.new(paths, user, report_access_grant_finder, report_type).to_hash }

        it "includes multi demographic report grant" do
          expect(subject[:available]).not_to include({url: standard_report_path, current: false, name: "MDR: here + here"})
        end
      end
    end
  end

  describe "#action_framework_grants" do
    let(:report_type) { ReportDataTypes::NEW_ACTION_FRAMEWORK }
    let(:summary_report) { FactoryBot.build(:report, survey: survey, report_view: :summary) }
    let(:participation_report) { FactoryBot.build(:report, survey: survey, report_view: :participation) }
    let!(:activity_grant) { ActivityReportAccessGrant.new(survey: FactoryBot.build(:survey)) }
    let!(:normal_grant) { FactoryBot.create(:report_access_grant, report: report_1, report_consumer: user) }
    let!(:summary_grant) { FactoryBot.create(:report_access_grant, report: summary_report, report_consumer: user) }
    let!(:participation_grant) { FactoryBot.create(:report_access_grant, report: participation_report, report_consumer: user) }
    let(:available_reports) { [activity_grant, normal_grant, summary_grant, participation_grant] }
    let(:report_access_grant_finder) { ReportAccessGrantFinder.new(user, survey.id, survey.id, anchor) }

    subject { described_class.new(paths, user, report_access_grant_finder, report_type).send(:action_framework_grants, available_reports) }

    it "filters out summary and participation reports with action_framework_enabled incorrectly set to true" do
      expect(subject).to contain_exactly(activity_grant, normal_grant)
    end
  end
end
