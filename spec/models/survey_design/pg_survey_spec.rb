require "rails_helper"

RSpec.describe SurveyDesign::<PERSON><PERSON><PERSON><PERSON><PERSON>, type: :model do
  let(:surveys_table) { Domains::MurmurGeneral.database(:general_db)[:surveys] }
  let(:responses_table) { Domains::MurmurGeneral.database(:general_db)[:responses] }
  let(:account_aggregate_id) { SecureRandom.uuid }

  let(:survey_1_aggregate_id) { SecureRandom.uuid }
  let(:survey_2_aggregate_id) { SecureRandom.uuid }
  let(:survey_3_aggregate_id) { SecureRandom.uuid }

  before do
    surveys_table.multi_insert([
      {
        aggregate_id: survey_1_aggregate_id
      },
      {
        aggregate_id: survey_2_aggregate_id
      },
      {
        aggregate_id: survey_3_aggregate_id
      }
    ])

    responses_table.multi_insert([
      {
        aggregate_id: SecureRandom.uuid,
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_1_aggregate_id
      },
      {
        aggregate_id: SecureRandom.uuid,
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_1_aggregate_id
      },
      {
        aggregate_id: SecureRandom.uuid,
        account_aggregate_id: account_aggregate_id,
        survey_aggregate_id: survey_2_aggregate_id
      }
    ])
  end

  it "has the correct table name" do
    expect(described_class.table_name).to eq("surveys")
  end

  it "counts the right numbers of table records" do
    expect(described_class.count).to eq surveys_table.count
  end

  it "has the correct number of responses" do
    survey_1 = described_class.find_by(aggregate_id: survey_1_aggregate_id)
    survey_2 = described_class.find_by(aggregate_id: survey_2_aggregate_id)

    expect(survey_1.responses.count).to eq 2
    expect(survey_2.responses.count).to eq 1
  end
end
