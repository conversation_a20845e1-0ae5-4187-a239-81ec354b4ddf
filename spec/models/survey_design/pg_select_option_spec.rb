require "rails_helper"

RSpec.describe SurveyDesign::PgSelectOption, type: :model do
  let(:questions_table) { Domains::MurmurGeneral.database(:general_db)[:questions] }
  let(:select_options_table) { Domains::MurmurGeneral.database(:general_db)[:select_options] }
  let(:account_aggregate_id) { SecureRandom.uuid }

  let(:question_aggregate_id) { SecureRandom.uuid }

  let(:select_option_1_aggregate_id) { SecureRandom.uuid }
  let(:select_option_2_aggregate_id) { SecureRandom.uuid }
  let(:select_option_3_aggregate_id) { SecureRandom.uuid }

  before do
    questions_table.insert(
      {
        aggregate_id: question_aggregate_id,
        account_aggregate_id: account_aggregate_id
      }
    )

    select_options_table.multi_insert([
      {
        aggregate_id: select_option_1_aggregate_id,
        question_aggregate_id: question_aggregate_id,
        account_aggregate_id: account_aggregate_id
      },
      {
        aggregate_id: select_option_2_aggregate_id,
        question_aggregate_id: question_aggregate_id,
        account_aggregate_id: account_aggregate_id
      },
      {
        aggregate_id: select_option_3_aggregate_id,
        question_aggregate_id: question_aggregate_id,
        account_aggregate_id: account_aggregate_id
      }
    ])
  end

  it "has the correct table name" do
    expect(described_class.table_name).to eq("select_options")
  end

  it "counts the right numbers of table records" do
    expect(described_class.count).to eq select_options_table.count
  end

  it "belongs to a question" do
    expect(described_class.first.question.aggregate_id).to eq question_aggregate_id
  end
end
