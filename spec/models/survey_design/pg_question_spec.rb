require "rails_helper"

RSpec.describe SurveyDesign::PgQuestion, type: :model do
  let(:questions_table) { Domains::MurmurGeneral.database(:general_db)[:questions] }
  let(:select_options_table) { Domains::MurmurGeneral.database(:general_db)[:select_options] }

  let(:account_aggregate_id) { SecureRandom.uuid }

  let(:question_1_aggregate_id) { SecureRandom.uuid }
  let(:question_2_aggregate_id) { SecureRandom.uuid }
  let(:question_3_aggregate_id) { SecureRandom.uuid }

  before do
    questions_table.multi_insert([
      {
        aggregate_id: question_1_aggregate_id,
        account_aggregate_id: account_aggregate_id
      },
      {
        aggregate_id: question_2_aggregate_id,
        account_aggregate_id: account_aggregate_id
      },
      {
        aggregate_id: question_3_aggregate_id,
        account_aggregate_id: account_aggregate_id
      }
    ])

    select_options_table.multi_insert([
      {
        aggregate_id: SecureRandom.uuid,
        question_aggregate_id: question_1_aggregate_id,
        account_aggregate_id: account_aggregate_id
      },
      {
        aggregate_id: SecureRandom.uuid,
        question_aggregate_id: question_1_aggregate_id,
        account_aggregate_id: account_aggregate_id
      },
      {
        aggregate_id: SecureRandom.uuid,
        question_aggregate_id: question_2_aggregate_id,
        account_aggregate_id: account_aggregate_id
      }
    ])
  end

  it "has the correct table name" do
    expect(described_class.table_name).to eq("questions")
  end

  it "counts the right numbers of table records" do
    expect(described_class.count).to eq questions_table.count
  end

  it "has the correct number of select options" do
    question_1 = described_class.find_by(aggregate_id: question_1_aggregate_id)
    question_2 = described_class.find_by(aggregate_id: question_2_aggregate_id)

    expect(question_1.select_options.count).to eq 2
    expect(question_2.select_options.count).to eq 1
  end
end
