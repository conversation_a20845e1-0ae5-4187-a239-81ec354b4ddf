# frozen_string_literal: true

require "ca/authentication/http_signatures"

::CA::Authentication::HttpSignatures.configure do |config|
  config.signing_key_id = "murmur_http_signing_key"

  def key(env_key)
    key = ENV[env_key] || ""
    key.gsub(/\\n/, "\n")
  end

  config.keys = {
    "murmur_http_signing_key" => {
      private_key: ENV["MURMUR_HTTP_SIGNING_PRIVKEY"] && OpenSSL::PKey::RSA.new(ENV["MURMUR_HTTP_SIGNING_PRIVKEY"].gsub('\n', "\n"))
    },
    "slack-communications-service" => {
      public_key: key("AUTH_HTTP_SIG_SLACK_COMMUNICATIONS_SERVICE_PUBKEY")
    },
    "survey-conversations-service" => {
      public_key: key("AUTH_HTTP_SIG_SURVEY_CONVERSATIONS_SERVICE_PUBKEY")
    },
    "survey-template-library" => {
      public_key: key("AUTH_HTTP_SIG_SURVEY_TEMPLATE_LIBRARY_PUBKEY")
    },
    "manager-lab-api" => {
      public_key: key("AUTH_HTTP_SIG_MANAGER_LAB_API_PUBKEY")
    },
    "develop-module-api" => {
      public_key: key("AUTH_HTTP_SIG_DEVELOP_MODULE_API_PUBKEY")
    },
    "career-pathways-api" => {
      public_key: key("AUTH_HTTP_SIG_CAREER_PATHWAYS_API_PUBKEY")
    },
    "ms-teams-bot" => {
      public_key: key("AUTH_HTTP_SIG_MS_TEAMS_BOT_PUBKEY")
    },
    "ms-teams-conv-migration-task" => {
      public_key: key("AUTH_HTTP_SIG_MS_TEAMS_CONV_MIGRATION_TASK_PUBKEY")
    },
    "recognition-service" => {
      public_key: key("AUTH_HTTP_SIG_RECOGNITION_SERVICE_API_PUBKEY")
    },
    "survey-scores" => {
      public_key: key("AUTH_HTTP_SIG_SURVEY_SCORES_PUBKEY")
    },
    "survey-summary" => {
      public_key: key("AUTH_HTTP_SIG_SURVEY_SUMMARY_PUBKEY")
    },
    "comment-publisher" => {
      public_key: key("AUTH_HTTP_SIG_COMMENT_PUBLISHER_PUBKEY")
    },
    "survey-design" => {
      public_key: key("AUTH_HTTP_SIG_SURVEY_DESIGN_PUBKEY")
    }
  }
end
