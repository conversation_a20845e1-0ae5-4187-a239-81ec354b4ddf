Murmur::Application.routes.draw do
  # Note, across all routes we always need a way of determining what 'account' the user is attempting
  # to access. We can't just assume it's the account of the current_user, because superusers (Culture
  # Amp users) can access any account. (We'll also eventually likely allow consultants to be given
  # access to multiple accounts).
  #
  # We can often derive the 'account' from the resource being accessed - eg via the survey, user,
  # access_grant, upload_employe_file, etc. However, routes such as :index, :new, and :create don't
  # have an already associated account we can leverage. We handle this issue by nesting the routes
  # for these actions (only) under the account resource routes - meaning we can rely on an account_id
  # in their URLs to explitly tell us the account. We've chosen not to nest all routes under the account
  # resource in an attempt to keep the URLs cleaner, and to avoid the possibility for ambiguity in the
  # URLs (eg, how would we interpret a survey id for an account that didn't match a provided account_id)
  #
  # In all cases, the controllers are still responsible for ensuring the current_user has appropriate
  # authorisation to access the routes based on the account that's been derived or explicitly provided.

  mount CepaHealth::Engine, at: "/healthy(.:format)"

  namespace :http_signature_api do
    # This is an endpoint to check if we can send http signed requests to murmur
    get "/check" => "/http_signature_api#check"
  end

  get "/s/:short_code", controller: :short_survey_links, action: :response_for_short_code, as: :short_survey_link

  # AppAmp Module
  scope module: :appamp do
    # Site Pages -
    # Add content to "./view/site" to make it available
    get "/core/site/:page", controller: :site, action: :page
  end

  get "/error/:page", controller: "appamp/site", action: :error
  get "/launch_plans" => "launch_plans#index"

  get "/go", controller: :inbound_redirection, action: :bounce

  # For new account setup workflow
  get "/private_api/account_setup" => "account_setup#index"
  post "/private_api/account_setup" => "account_setup#update"

  get "/terms-agreement" => "terms_and_conditions#index", :as => "terms_and_conditions"
  post "/terms-agreement" => "terms_and_conditions#create"

  resources :campers, only: [:index, :show, :update]
  get "/msteams/send_test_message" => "microsoft_teams#send_test_message"

  namespace :managerlab do
    resources :user, only: [:show] do
      member do
        get "communication_platforms"
      end
    end
  end

  namespace :develop_module do
    resources :user, only: [:show]
  end

  namespace :career_pathways do
    resources :user, only: [:show]
  end

  post "/managerlab/send_slack_message" => "managerlab/slack_mailer#send_slack_message"

  # Administration
  resources :admin, only: [:index, :show] do
    get "search", on: :collection
    get "survey_results_download"

    collection do
      get "error"
      get "person" => "admin#summary_person"
      get "survey_period_templates"
      get "flags/search" => "admin/flags#search"
      get "surveys/:survey_id/responses/:response_id/resend_invite" => "admin#resend_invite", :as => "resend_invite"
      get "surveys/:survey_id/responses/:response_id/reset_response" => "admin#reset_response", :as => "reset_response"

      resources :influx_migrations, controller: "admin/influx_migrations", only: [:index, :create]

      resources :employee_event_reader, controller: "admin/employee_event_reader", only: [:index] do
        collection do
          get :view_data
        end
      end

      resource :masquerade, controller: "admin/masquerades", only: [:new, :create, :destroy]
      resources :hierarchies, controller: "admin/hierarchies", only: [:index, :show]
      resources :benchmarks, controller: "admin/benchmarks", only: [:index, :create, :destroy] do
        collection { post :bulk_upload }
      end
      resources :privileged_users, controller: "admin/privileged_users", only: [:index, :create, :destroy] do
        collection do
          get :accounts
          post :toggle_account
        end
      end
      resources :accounts_summary, controller: "admin/accounts_summary", only: [:index, :show]
      resources :restricted_actions, controller: "admin/restricted_actions", only: [:index]
      resources :gdpr_accounts, controller: "admin/gdpr_accounts", only: [:index, :destroy]
      resources :permissions_migrations, controller: "admin/permissions_migrations", only: [:index, :create]
      resources :fusion_auth_status, controller: "admin/fusion_auth_status", only: [:show, :create]
    end
  end

  put "features/:id/update_configuration" => "features#update_configuration", :as => "update_configs"
  resources :features, only: [:index, :show, :update]
  resources :test_data_generator, only: [:new, :create]

  # "Ad-hoc" Surveys
  resources :adhoc, only: [:show], controller: :adhoc_surveys do
    post "anonymous_register", action: "anonymous_register"
  end

  resources :background_jobs, only: [:create, :destroy, :index, :show, :update] do
    post "run_now", action: "run_now"
  end

  resources :communications, only: [:show]
  resources :bulk_account_update, only: [:new, :create]

  # Kiosk Access
  resources :kiosk, only: [:show] do
    post "register", action: "register"
  end

  namespace :effectiveness do
    resources :internal_dashboard, only: [:index], controller: "internal_dashboard"
    resources :surveys, only: [], controller: "legacy_surveys" do
      member do
        get "app/insight_report", to: "app#insight_report", as: :application_insight_report_index
        get "app/insight_report/*front_end_route", to: "app#insight_report", as: :application_insight_report
        get "app/*front_end_route", to: "app#index", as: :application
      end

      put "three_sixty_settings", to: "three_sixty_settings#update", as: :three_sixty_settings

      resources :bulk_actions, only: [:show]
      resources :bulk_create_processes, only: [:index]
      resources :participants, only: [:index]
      resources :themes, only: [:index]
      resources :factors, only: [:index]
      resources :stats, only: [:index]
      resources :responses, only: [:show]

      post "bulk_actions/complete", to: "bulk_actions#complete", as: :bulk_complete
      post "bulk_actions/share", to: "bulk_actions#share", as: :bulk_share
      post "bulk_actions/request_nomination", to: "bulk_actions#request_nomination", as: :bulk_request_nomination
      post "bulk_actions/launch", to: "bulk_actions#launch", as: :bulk_launch
      post "bulk_actions/create", to: "bulk_actions#create", as: :bulk_create
      post "bulk_actions/delete", to: "bulk_actions#delete", as: :bulk_delete
      post "bulk_actions/update_dates", to: "bulk_actions#update_dates", as: :bulk_update_dates
      post "bulk_actions/send_reminders", to: "bulk_actions#send_reminders", as: :bulk_remind
      post "bulk_actions/validate_creation", to: "bulk_actions#validate_creation", as: :bulk_validate

      resources :processes, only: [:index, :show] do
        resource :observers, only: [:update]
        resources :answers, only: [:index]
        resources :focuses, only: [:index, :create, :destroy]

        member do
          get "feedback_review/feedback", controller: "feedback_review", action: "show", as: :feedback_report
          get "feedback_review/take_action", controller: "feedback_review", action: "show", as: :take_action
          get "feedback_review/new_take_action", controller: "action_framework", action: "show", as: :new_take_action
          get "feedback_review/new_take_action/questions/:question_aggregate_id/create_action", controller: "action_framework", action: "create", as: :effectiveness_create_action
          get "feedback_review/api/app_data", controller: "feedback_review", action: "app_data"
          get "feedback_review/api/take_action", controller: "feedback_review", action: "get_take_action_data"
        end
      end

      resource :insight_report, only: [:show], controller: :insight_report do
        resources :free_text_questions, only: [:show]
        resources :rating_questions, only: [:show]
        resources :select_questions do
          resources :select_options, only: [:show]
        end
      end

      resources :insight_report_powerpoint_export, only: [:create, :show]

      resource :participation, only: [], controller: :participation do
        collection do
          get "employee_workload"
          get "reviewer_workload"
        end
      end

      resources :action_record, only: [] do
        resources :export_bulk_create_errors, only: [:index]
      end
    end

    resources :responses, only: [:show, :update] do
      resources :answers, only: [:update] do
        resources :select_options, only: [] do
          resource :comments, only: [:update, :destroy]
        end
      end
    end
  end

  # Reporting 2 - Legacy Permissions
  resources :insight_report, only: [:show] do
    resources :questions, controller: :question_insight_report, only: [:index, :show]
    resources :factors, controller: :factor_insight_report, only: [:show]
    resources :participation_report, only: [:index, :show] do
      get "show_spreadsheet", to: "participation_report#show_spreadsheet", on: :collection, format: true, constraints: {format: /(csv|xls|xlsx)/}
    end
    resources :select_question_report, only: [:index]
    resources :powerpoint_report, only: [:new, :create, :show]
  end

  resources :new_comparison_report, only: [:show] do
    get "count"
  end

  resources :lifecycle_insight_report, only: [:show]

  # Old Manager Insight

  resources :old_manager_insight, as: "old_manager_insight", path: "manager_insight", only: [] do
    get "insight_spreadsheet", to: "insight_spreadsheet#show_spreadsheet", as: "insight_spreadsheet", format: true, constraints: {format: /(csv|xls)/}

    get "filters" => "filters#data"
  end

  resources :demographic_report, only: [:show] do
    get "filters" => "filters#data"
    get "select_demographic"
  end

  # -- Capture
  resources :responses, only: [:show, :update] do
    resources :answers, only: [:update]
    post "start"
    get "rate" => "responses#rate"
    get "thanks" => "responses#thanks"
    post "reset_response"
  end

  get "responses/:id/content/:template_id" => "responses#confidentiality_template_content", :as => "response_content"

  get "online" => "online#index"

  resources :inspiration_reports, only: [] do
    collection do
      get "exclusions" => "action_frameworks/datalab_reports#exclusions"
      get "custom_inspirations" => "action_frameworks/datalab_reports#custom_inspirations"
      get "custom_inspiration/:inspiration_uuid" => "action_frameworks/datalab_reports#custom_inspiration"
      get "actions" => "action_frameworks/datalab_reports#actions"
      get "core_inspirations" => "action_frameworks/datalab_reports#core_inspirations"
    end
  end

  get "kiosk_summary/export/start", to: "action_frameworks/kiosk_summary_export#start", as: :kiosk_summary_export_start
  get "kiosk_summary/export/:filename", to: "action_frameworks/kiosk_summary_export#show", as: :kiosk_summary_export

  resources :surveys, only: [:show, :update, :destroy], controller: "legacy_surveys" do
    post "upload_survey_logo", controller: "legacy_surveys", to: "legacy_surveys#upload_survey_logo"
    post "delete_survey_logo", controller: "legacy_surveys", to: "legacy_surveys#delete_survey_logo"
    post "responses_count", controller: "legacy_surveys", to: "legacy_surveys#responses_count"
    post "remove_inactive_managers_from_hierarchy", controller: "legacy_surveys", to: "legacy_surveys#remove_inactive_managers_from_hierarchy"

    get :configuration, to: "admin#show", on: :member

    # NOTE this is not to become a permanent URL namespace; this should only exist
    # here until the new survey designer becomes EAP
    namespace :survey_designer, path: "/survey_designer" do
      root to: "bootstrap#index"

      get "survey_details", to: "queries#survey_details"
      get "v1/survey_metadata", to: "queries#survey_metadata"
      get "demographic_question_select_options", to: "queries#demographic_question_select_options"
      get "recommended_questions", to: "queries#recommended_questions"
      post "add_section", controller: "commands", action: "add_section"
      post "make_question_mandatory", controller: "commands", action: "make_question_mandatory"
      post "make_question_optional", controller: "commands", action: "make_question_optional"
      post "remove_section", controller: "commands", action: "remove_section"
      post "change_section_description", controller: "commands", action: "change_section_description"
      post "rename_section", controller: "commands", action: "rename_section"
      post "add_question", controller: "commands", action: "add_question"
      post "add_demographic_question", controller: "commands", action: "add_demographic_question"
      post "delete_question", controller: "commands", action: "delete_question"
      post "change_question_type", controller: "commands", action: "change_question_type"
      post "add_select_option", controller: "commands", action: "add_select_option"
      post "add_select_options", controller: "commands", action: "add_select_options"
      post "change_question_text", controller: "commands", action: "change_question_text"
      post "change_selection_limit", controller: "commands", action: "change_selection_limit"
      post "change_rating_scale", controller: "commands", action: "change_rating_scale"
      post "rename_select_option", controller: "commands", action: "rename_select_option"
      post "delete_select_option", controller: "commands", action: "delete_select_option"
      post "enable_other_select_option", controller: "commands", action: "enable_other_select_option"
      post "disable_other_select_option", controller: "commands", action: "disable_other_select_option"
      post "change_other_select_option_label", controller: "commands", action: "change_other_select_option_label"
      post "move_section", controller: "commands", action: "move_section"
      post "position_question", controller: "commands", action: "position_question"
      post "position_demographic_sections", controller: "commands", action: "position_demographic_sections"
      post "move_select_option", controller: "commands", action: "move_select_option"
      post "restore_section", controller: "commands", action: "restore_section"
      post "restore_question", controller: "commands", action: "restore_question"
      post "hide_question_from_capture", controller: "commands", action: "hide_question_from_capture"
      post "change_section_long_description", controller: "commands", action: "change_section_long_description"
      post "change_section_short_description", controller: "commands", action: "change_section_short_description"
      post "remove_section_descriptions", controller: "commands", action: "remove_section_descriptions"
      post "delete_survey", controller: "commands", action: "delete_survey"
      post "restore_survey", controller: "commands", action: "restore_survey"
      post "add_answer_condition_to_question", controller: "commands", action: "add_answer_condition_to_question"
      post "choose_parent_question", controller: "commands", action: "choose_parent_question"
      post "add_select_option_from_parent_question", controller: "commands", action: "add_select_option_from_parent_question"
      post "remove_select_option_from_parent_question", controller: "commands", action: "remove_select_option_from_parent_question"
      post "remove_answer_condition_from_question", controller: "commands", action: "remove_answer_condition_from_question"
      post "change_question_short_text", controller: "commands", action: "change_question_short_text"

      # legacy commands
      post "add_demographic_condition", controller: "commands", action: "add_demographic_condition"
      post "add_demographic_condition_value", controller: "commands", action: "add_demographic_condition_value"
      post "remove_demographic_condition", controller: "commands", action: "remove_demographic_condition"
      post "remove_demographic_condition_value", controller: "commands", action: "remove_demographic_condition_value"
      post "remove_all_demographic_conditions", controller: "commands", action: "remove_all_demographic_conditions"
      post "select_demographic_condition_question", controller: "commands", action: "select_demographic_condition_question"
      post "set_condition_matcher_type", controller: "commands", action: "set_condition_matcher_type"

      get "question_factors", to: "queries#question_factors"
    end

    namespace :survey_scheduler, path: "/survey_scheduler" do
      post "schedule", controller: "scheduler", action: "schedule"
      post "cancel_launch", controller: "scheduler", action: "cancel_launch"
      post "close", controller: "scheduler", action: "close"
      post "reopen", controller: "scheduler", action: "reopen"
      post "resend_reminder", controller: "scheduler", action: "resend_reminder"
    end

    resources :sample_reports, only: [:show], controller: "shared_reports/sample_report"

    controller "account_branching_questions" do
      get "/branching_questions", action: "index"
      post "branching_questions/validate", action: "validate"
    end

    controller "hierarchy_browser" do
      get "/hierarchy_browser", action: "index"
      get "/hierarchy_browser/:leader_id", action: "index"
    end

    resources :inspirations, only: [:index, :create, :destroy, :update], controller: "action_frameworks/inspirations" do
      collection do
        get "mappings" => "action_frameworks/inspirations#mappings"
        get "import_export" => "action_frameworks/inspirations_import_export#index"
        get "export" => "action_frameworks/inspirations_import_export#export"
        post "changes" => "action_frameworks/inspirations_import_export#changes"
        post "import" => "action_frameworks/inspirations_import_export#import"
        get "translations" => "action_frameworks/inspiration_translations#index"
        get "translations/export" => "action_frameworks/inspiration_translations#export"
        post "translations/import" => "action_frameworks/inspiration_translations#import"
      end
      member do
        post "exclude" => "action_frameworks/inspirations#create_exclusion"
        delete "exclude" => "action_frameworks/inspirations#remove_exclusion"
        post "include" => "action_frameworks/inspirations#create_inclusion"
        delete "include" => "action_frameworks/inspirations#remove_inclusion"
        post "create_account_inspiration_from_core_inspiration" => "action_frameworks/inspirations#create_account_inspiration_from_core_inspiration"
      end
    end

    get "focus_area_modal" => "action_frameworks/focus_area_modal#index"
    get "focus_agent_scores" => "focus_engine#index"
    get "respond" => "legacy_surveys#respond"
    get "confidentiality" => "legacy_surveys#confidentiality"
    post "reset_cache" => "legacy_surveys#reset_cache"
    post "generate_feedback_digest" => "legacy_surveys#generate_feedback_digest"
    get "operations"
    get "audit_logs"
    patch "kiosk_on", to: "legacy_surveys#kiosk_on"
    patch "kiosk_off", to: "legacy_surveys#kiosk_off"
    get "kiosk_state", to: "legacy_surveys#kiosk_state"
    put "kiosk_settings", to: "legacy_surveys#kiosk_settings"
    put "administer", to: "legacy_surveys#administer"
    get "survey_launch_settings", to: "snapshot_launch#survey_launch_settings"

    resources :summaries, only: [:update, :destroy]

    resources :admins, controller: :survey_admins, except: [:show, :destroy]
    resource :design, controller: :survey_design, except: [:create, :new, :destroy] do
      get "demonstration" => "survey_demonstration#capture"
      get "confidentiality" => "survey_demonstration#confidentiality"
      get "questions_export/:filename/:job_id" => "survey_questions_api/v1/questions_export#show", :as => "questions_export"
      get "questions_export/template" => "survey_questions_api/v1/questions_export#template", :as => "questions_export_template"
      get "questions_import/:import_questions_process_aggregate_id" => "survey_questions_api/v1/questions_import#show", :as => "questions_import"
      post "questions_export" => "survey_questions_api/v1/questions_export#create", :as => "start_questions_export"
      post "questions_import" => "survey_questions_api/v1/questions_import#create", :as => "start_questions_import"
      post "setup" => "survey_demonstration#setup"
      put "set_demographic_grouping"
    end

    resources :launch, only: [] do
      get "confirm", on: :collection
    end

    resources :snapshot_launch, only: [:index, :update]

    resources :continuous_launch, only: [:index, :update] do
      get "confirm_update"
    end

    resources :lifecycle_launch, only: [:index, :update] do
      get "confirm"
      get "confirm_update"
    end

    resources :adhoc_launch, only: [:index, :update] do
      get "confirm", on: :collection
    end

    resources :participant do
      collection do
        put "update_exclude_after"
        put "select_all"
        put "deselect_all"
        get "view_by_index"
        get "invited_participants_count"
      end
    end

    post "participant_import" => "participant_import#analyze", :as => "analyze_participant_import"
    post "participant_import_v2" => "participant_import#analyze_v2", :as => "analyze_v2_participant_import"
    put "participant_import/:participant_import_id" => "participant_import#apply", :as => "apply_participant_import"
    get "participant_import/:participant_import_id" => "participant_import#status", :as => "participant_import_status"
    get "participant_import_latest_participant_import_id" => "participant_import#latest_participant_import_id"
    get "participant_import_latest_apply_step/:participant_import_id" => "participant_import#latest_apply_step_import"

    resources :kiosk_codes_exports, only: [:new, :create, :show]

    resources :participants_exports, only: [:create, :show] do
      get "download", to: "participants_exports#download"
    end

    resources :raw_data_exports, only: [:new, :create, :show]

    resource :feedback_digest, only: [:create] do
      post "create"
    end

    resources :import_survey_responses, only: [:new, :create]

    get "/import_survey_responses/master_batch/:id",
      to: "survey_importer/master_batches#show",
      as: "importer_master_batch"

    scope module: "import_export_report" do
      namespace :v1 do
        resources :import_report_sharing_permissions, only: [:new, :create, :show]
      end

      resources :import_report_sharing_permissions_file, only: [:index, :create, :show, :update]
      get "import_report_sharing_permissions_file/:id/errors_download" => "import_report_sharing_permissions_file#errors_download"

      resources :validate_report_sharing_permissions_file, only: [:new, :create, :show]

      get "validate_report_sharing_permissions_file/:id/errors_download" => "validate_report_sharing_permissions_file#errors_download"

      resources :import_reports, only: [:new, :create, :show]

      resources :reports_import_results, only: [:show] do
        get :export, on: :member
      end

      resources :report_sharing_permissions_import_results, only: [:show] do
        get :export, on: :member
      end

      resources :export_report_sharing_permissions, only: [:new, :create, :show]
      resources :export_effectiveness_participation, only: [:new, :create, :show]
      resources :export_reports, only: [:new, :create, :show]
    end

    resources :communication_designs, only: [:index, :show, :update] do
      get :preview, on: :member
      collection do
        put :enable_slack
        put :disable_slack
        get :show_slack_screenshot
      end
    end

    resources :slack_communication_designs, only: [:show, :create, :update]

    resources :survey_to_questions, controller: :survey_to_questions, except: [:index] do
      get "answer_details"
      put "toggle_inclusion"
    end

    put "select_all_demographics", controller: "survey_to_questions", to: "survey_to_questions#select_all_demographics"
    put "deselect_all_demographics", controller: "survey_to_questions", to: "survey_to_questions#deselect_all_demographics"

    resources :sections, controller: :sections, except: [:index] do
      get "recommended_questions"
      put "save_recommended_question_experiment"
    end

    resource :report_settings, only: [:edit, :update]

    resource :survey_comments_settings, only: [:update]

    get "available_consumers" => "report_sharing#available_consumers"
    post "reset_action_framework" => "action_frameworks/survey#reset"
    get "reset_action_framework" => "action_frameworks/survey#new"
    resources :hierarchy_reports, controller: "reports/hierarchy_reports", only: [:create]

    namespace "shared_reports" do
      resources "demographics", only: [:index, :show]
      resources "employee_hierarchies", only: [:show]
    end

    get "manager_hierarchies" => "reports/manager_hierarchies#hierarchy_for_admin_report", :as => "hierarchy_for_admin_report"
    get "leader_name" => "reports/leader_name#show", :as => "hierarchy_leader_name"

    get "manager_hierarchy_browser" => "reports/manager_hierarchy_browser#hierarchy_for_leader", :as => "hierarchy_for_leader"

    get "bulk_remove_grants_progress/:bulk_remove_grants_aggregate_id" => "report_sharing#bulk_remove_grants_progress", :as => "bulk_remove_grants_progress"
    put "bulk_remove_grants" => "report_sharing#bulk_remove_grants"

    resources :reports, controller: :report_sharing, except: [:new, :edit] do
      get "filters" => "filters#data"
      get "report_consumers_count" => "report_sharing#report_consumers_count"
      post "export_participants" => "reports/export_participants#create"
      get "export_participants/:id" => "reports/export_participants#show", :as => "show_export_participants"
      post "export_bulk" => "reports/export_bulk#create"
      get "export_bulk/:id" => "reports/export_bulk#show", :as => "show_export_bulk"

      get "simplified_report_configuration" => "report_sharing#simplified_report_configuration_index"
      get "simplified_report_configuration" => "report_sharing#simplified_report_configuration_index", :on => :collection

      collection do
        resources :overall_reports, only: [:create, :update], controller: "shared_reports/overall_reports" do
          get "new" => "report_sharing#index", :as => "new", :on => :collection
          get "/:report_id" => "report_sharing#index", :as => "edit", :on => :collection
        end
      end

      get "employee_hierarchies/:front_end_route" => "report_sharing#index", :on => :collection

      post "publish_report" => "shared_reports#publish_report"
      post "unpublish_report" => "shared_reports#unpublish_report"

      get "/:report_id/metadata" => "report_metadata#index", :on => :collection
      get "/:report_id/user_configuration" => "report_metadata#metadata_user_configuration", :on => :collection

      resources :participation_report, only: [:index, :show] do
        get "show_spreadsheet", to: "participation_report#show_spreadsheet", on: :collection, format: true, constraints: {format: /(csv|xls|xlsx)/}
      end

      get "summary_insight_report" => "summary_insight_report#show"

      get "participants" => "shared_reports/participants#index"

      get "take_action" => "action_frameworks/new_action_framework_report#show", :as => "new_action_framework_report"
      get "take_action/questions/:question_aggregate_id/create_action" => "action_frameworks/new_action_framework_report#create_action", :as => "create_action"
      get "create_action" => "action_frameworks/new_action_framework_report#create_action", :as => "create_action_without_focus"
      get "take_action_pdf" => "action_frameworks/new_action_framework_report#show_pdf"

      # Reporting 2 - Report Sharing reports
      get "insight_report" => "insight_report#show"

      get "new_comparison_report" => "new_comparison_report#show"
      get "new_comparison_report/count" => "new_comparison_report#count"

      resources :powerpoint_report, only: [:new, :create, :show]

      resources :survey_to_questions, path: :question_report, controller: :question_insight_report, as: "question_insight_report", only: [:index, :show]
      get "coach_ai_props", to: "coach_ai_props#coach_ai_props", constraints: {format: /json/}
      get "question_scores", to: "coach_ai_props#question_scores", constraints: {format: /json/}
      get "demographic_filters", to: "coach_ai_props#demographic_filters", constraints: {format: /json/}
      get "focus_area_modal" => "action_frameworks/focus_area_modal#index"
      resources :factors, path: :factor_report, controller: :factor_insight_report, as: "factor_insight_report", only: [:show]
      resources :select_question_report, only: [:index]

      get "insight_spreadsheet" => "insight_spreadsheet#show_spreadsheet", :as => "insight_spreadsheet", :format => true, :constraints => {format: /(csv|xls|xlsx)/}

      get "lifecycle_insight_report" => "lifecycle_insight_report#show"

      # Needs to be called :survey_to_questions to work when passing an STQ to the polymorphic path helpers
      get "demographics" => "demographic_report#show"

      resources :free_text_questions_report, only: [:index]

      put "update_grants"
      resources :notify_people, controller: "report_sharing_notifications", only: [:create, :show] do
        collection do
          get "preview"
        end
      end
      get "notify_counts"

      resources :comments, controller: "text_analytics/reports", only: [:index] do
        collection do
          post "graphql"
          get "themes", to: "text_analytics/reports#index"
          get "saved", to: "text_analytics/reports#index"
        end
      end

      namespace :trend do
        namespace :api, controller: "/trend/api" do
          post :graphql
        end
      end

      get "lifecycle_survey_trend" => "lifecycle_survey_trend/scores#show"

      resources :comments_export, controller: "text_analytics/exports", only: [:new, :create, :show]

      get "manager_hierarchies" => "reports/manager_hierarchies#hierarchy_for_shared_report", :as => "hierarchy_for_shared_report"
    end

    get "summary_insight_report" => "summary_insight_report#show"
    get "take_action" => "action_frameworks/new_action_framework_report#show", :as => "new_action_framework_report"
    get "take_action/questions/:question_aggregate_id/create_action" => "action_frameworks/new_action_framework_report#create_action", :as => "create_action"
    get "create_action" => "action_frameworks/new_action_framework_report#create_action", :as => "create_action_without_focus"
    get "focus_area_modal" => "action_frameworks/focus_area_modal#index"
    get "take_action_pdf" => "action_frameworks/new_action_framework_report#show_pdf"

    resources :free_text_questions_report, only: [:index]

    resources :comments, controller: "text_analytics/reports", only: [:index] do
      collection do
        post "graphql"
        get "themes", to: "text_analytics/reports#index"
        get "saved", to: "text_analytics/reports#index"
      end
    end

    namespace :survey_templates do
      resource :admin, controller: "survey_admin", only: [:show, :update]
      get "export", to: "export#single", as: :export
    end

    namespace :trend do
      namespace :api, controller: "/trend/api" do
        post :graphql
      end
    end
    get "lifecycle_survey_trend" => "lifecycle_survey_trend/scores#show"

    resources :comments_export, controller: "text_analytics/exports", only: [:new, :create, :show]

    resources :exit_responses, only: [:index, :new, :create], path: "activity" do
      get "count" => "exit_responses#count", :on => :collection, :constraints => {format: /json/}
    end
    get "exit_responses", to: redirect("legacy_surveys/%{survey_id}/activity")
    resources :periods, controller: :survey_periods, only: [:index, :create]
    resource :text_analytics_admin, controller: "text_analytics/admin", path: "text_analytics/admin", only: [:show, :update]
    resource :trend_import, controller: "trend/import", path: "trend/import", only: [:show, :update]
    resource :trend_reset, controller: "trend/reset", path: "trend/reset", only: [:show, :update]
    resource :trend_diagnosis, controller: "trend/diagnosis", path: "trend/diagnosis/:category_id", only: [:show, :update]

    namespace :trend do
      resource :survey_configuration, controller: "survey_configuration", only: [:show, :update]
    end

    resources :demographic_option_summary_imports, only: [:new, :create] do
      get :check_import
    end

    resources :hierarchical_summary_imports, only: [:new, :create] do
      get :check_import
    end

    get "summary_imports/comparison_import_props" => "survey_summary_imports#comparison_import_props"
    get "summary_imports/export_historical_survey_demographic_mappings" => "survey_summary_imports#export_historical_survey_demographic_mappings"
    put "summary_imports/reorder_comparisons" => "survey_summary_imports#reorder_comparisons"
    put "summary_imports/set_default_comparison" => "survey_summary_imports#set_default_comparison"
    post "summary_imports/demographic_comparison_support_ticket" => "survey_summary_imports#demographic_comparison_support_ticket"
    put "summary_imports/update_company_overall_comparison_name" => "survey_summary_imports#update_company_overall_comparison_name"
    resources :summary_imports, controller: :survey_summary_imports, except: [:new] do
      get :check_import
      get :question_matching_props
      get :factors_with_matching_questions, constraints: {summary_import_id: /[0-z.]+/}
    end
    get "summary_imports/*front_end_route" => "survey_summary_imports#index"

    namespace :external_comparison_import do
      resources :dry_run, only: [:create, :show], controller: "dry_run"
      resources :import, only: [:create, :show], controller: "import"
      resources :generate_csv, only: [:create, :show], controller: "generate_csv"
    end

    resources :three_sixty_survey_topics do
      member do
        post "request_nomination"
        post "share_feedback"
        post "unshare_feedback"
        post "complete"
        post "launch"
        post "reopen"
        post "send_nomination_reminder"
        post "send_reminders"
      end

      collection do
        get "all_activities"
        get "history"
        get "start_your_process"
      end

      resources :responses, controller: :three_sixty_responses, only: [:index, :show] do
        member do
          get "reset"
          get "resend"
        end
      end
    end

    resources :survey_topics, only: [] do
      post "reminder"
      post "reset"
    end

    resources :action_framework, only: [:show] do
      get "/", to: "action_frameworks/action_framework#show"

      resources :focus_areas, only: [:create, :destroy], controller: "action_frameworks/focus_area"
      resources :action, only: [:create, :update, :destroy], controller: "action_frameworks/action" do
        put "complete", to: "action_frameworks/action#complete"
        delete "complete", to: "action_frameworks/action#undo_complete"

        resources :comment, controller: "action_frameworks/comment", only: [:create, :update, :destroy]

        post "feedback/start", to: "action_frameworks/action_feedback#start"
        post "feedback/:job_id/cancel", to: "action_frameworks/action_feedback#cancel"
        get "feedback_preview" => "action_frameworks/action_feedback_response#preview"
        resources :feedback, controller: "action_frameworks/action_feedback_response", only: [:show] do
          resources :response, controller: "action_frameworks/action_feedback_response", only: [:update]
        end
      end
      get "feedback/respondents", to: "action_frameworks/action_feedback#respondents"
      get "focus_area_options" => "action_frameworks/action_framework#focus_area_options"
    end

    get "questions/:question_aggregate_id/inspirations" => "action_frameworks/action_framework#inspirations", :as => :question_inspirations

    get "action_dashboard/:action_framework_id/report", to: "action_frameworks/action_dashboard#report_summary"
    get "action_dashboard/:action_framework_id/report_viewers", to: "action_frameworks/action_dashboard#report_viewers"
    get "action_dashboard/:action_framework_id/report_details", to: "action_frameworks/action_dashboard#report_details"

    get "action_dashboard/overview", to: "action_frameworks/action_dashboard#overview"
    get "action_dashboard/progress", to: "action_frameworks/action_dashboard#progress"
    get "action_dashboard/inspirations", to: "action_frameworks/action_dashboard#inspirations"
    get "action_dashboard/focus_areas", to: "action_frameworks/action_dashboard#focus_areas"
    get "action_dashboard/app/*front_end_route", to: "action_frameworks/action_dashboard#index", as: :action_dashboard_application

    post "action_dashboard/export", to: "action_frameworks/action_dashboard_export#create", as: :action_dashboard_export_create
    get "action_dashboard/export/:id", to: "action_frameworks/action_dashboard_export#show", as: :action_dashboard_export_show
    get "action_dashboard/export/:id/download_file", to: "action_frameworks/action_dashboard_export#download_file", as: :action_dashboard_export_download_file

    post "upgrade", to: "effectiveness/upgrade_survey#update"
    post "upgrade_communications", to: "effectiveness/upgrade_communications#update"

    resources :export_import_questions_translation, only: [:new]
    post "export_import_questions_translation/import", to: "export_import_questions_translation#import"
    get "export_import_questions_translation/export", to: "export_import_questions_translation#export"
    post "export_import_comms_translation/import", to: "export_import_comms_translation#import"
    get "export_import_comms_translation/export", to: "export_import_comms_translation#export"

    resources :response_stats, only: [:index]

    get "notifications/:type", to: "survey_notifications#show", as: :notification
  end

  scope :action_plans do
    get :created_actions, to: "action_frameworks/action_plans#created_actions"
    get :accessible_actions, to: "action_frameworks/action_plans#accessible_actions"
  end

  controller "action_frameworks/personal/growth_plan" do
    get :personal_growth_plan, action: :show
    get "personal_growth_plan/:focus_area_id/create_action", action: :create_action, as: :create_personal_action
    get "personal_growth_plan/:focus_area_id/suggested_actions", action: :suggested_actions, as: :suggested_actions
  end

  scope :personal_growth_plan do
    resources :action, only: [:create, :update, :destroy], controller: "action_frameworks/personal/action" do
      member do
        put :complete
        delete :complete, action: :undo_complete
      end

      resources :comment, controller: "action_frameworks/comment", only: [:create, :update, :destroy]
    end
  end

  resources :periods, controller: :survey_periods, only: [:create]

  get "exit_responses/:id", to: redirect("activity/%{id}")
  resources :exit_responses, only: [:destroy, :show, :update], path: "activity" do
    resources :answers, only: [:update]
    get ":id/demo" => "exit_responses#demo_mode", :on => :collection, :as => :demo
    post "share_feedback" => "exit_responses#share_feedback"
    post "reminder" => "exit_responses#reminder"

    collection do
      get "user_search"
    end
  end

  namespace :comment_replies do
    get "/" => "/comment_reply_configuration#index"
  end

  namespace :comment_view do
    post "/" => "/comment_view#authorized"
    post "/replies_enabled" => "/comment_view#authorized_and_replies_enabled"
  end

  namespace :manager_dashboard do
    get "/:manager_id" => "/manager_dashboard#index"
    get "/" => "/manager_dashboard#index"
  end

  resources :support_tasks, only: [:index]
  namespace :support_tasks_api do
    get "/init" => "/support_tasks#init"
    get "/get" => "/support_tasks#get", :as => "get"
    post "/execute_task" => "/support_tasks#execute_task", :as => "execute_task"
  end

  namespace :trend_api do
    get "/program/:program_id" => "/trend_api/program#get", :as => "trend_for_program"
    get "/program/:program_id/factor_sets" => "/trend_api/program#factor_sets", :as => "factor_sets"
  end

  namespace :survey_programs_api do
    get "/recent_surveys/:account_aggregate_id" => "/recent_surveys#get", :as => "recent_surveys"
    get "/account/:account_aggregate_id/programs/:aggregate_id/surveys" => "/surveys_for_program#get", :as => "surveys_for_program"
    get "/account/:account_aggregate_id" => "/survey_programs#survey_programs_for_account", :as => "survey_programs_for_account"
    get "/account/:account_aggregate_id/recommendations" => "/survey_programs#recommendations", :as => "program_recommendations_for_account"
    post "/account/:account_aggregate_id/create" => "/survey_programs#create", :as => "create_survey_program"
    get "/:aggregate_id" => "/survey_programs#survey_program", :as => "survey_program"
    patch "/:aggregate_id" => "/survey_programs#update_program", :as => "update_program"
    delete "/:aggregate_id" => "/survey_programs#destroy"
    get "/:aggregate_id/surveys" => "/survey_programs#surveys", :as => "surveys"
    get "/:aggregate_id/surveys_to_add" => "/survey_programs#surveys_to_add", :as => "surveys_to_add"
    post "/:aggregate_id/add_survey" => "/survey_programs#add_survey", :as => "add_survey"
    post "/:aggregate_id/add_surveys" => "/survey_programs#add_surveys", :as => "add_surveys"
    post "/:aggregate_id/duplicate_survey" => "/survey_programs#duplicate_survey", :as => "duplicate_survey"
    post "/:aggregate_id/rename" => "/survey_programs#rename", :as => "rename"
    post "/:aggregate_id/remove_survey" => "/survey_programs#remove_survey", :as => "remove_survey"
  end

  namespace :surveys_api do
    put "/v1/:survey_aggregate_id/confidentiality_configuration/reporting_rules" => "/surveys_api/v1/confidentiality_configuration#set_reporting_rules"
    put "/v1/:survey_aggregate_id/confidentiality_configuration/raw_data_extract" => "/surveys_api/v1/confidentiality_configuration#set_raw_data_extract"
    get "/v1/:survey_aggregate_id/confidentiality_configuration" => "/surveys_api/v1/confidentiality_configuration#get_confidentiality_configuration"

    get "/:survey_aggregate_id" => "/surveys_api/surveys#show"
    get "/:survey_aggregate_id/configuration_actions" => "/surveys_api/configuration_actions#show"
    get "/v1/:survey_aggregate_id/index_factor" => "/surveys_api/v1/index_factor#survey_index_factor"
    get "/v1/:survey_aggregate_id/driver_analysis_metadata" => "/surveys_api/v1/driver_analysis_metadata#driver_analysis_metadata"
    get "/v1/:survey_aggregate_id/reporting_group_minimum" => "/surveys_api/v1/reporting_group_minimum#reporting_group_minimum"
    get "/v1/:survey_aggregate_id/metadata/title_block" => "/surveys_api/v1/metadata#title_block"
    get "/v1/:survey_aggregate_id/auto_comparisons_hidden_in_report_count" => "/surveys_api/v1/auto_comparisons#hidden_in_report_count"
    get "/v1/:survey_aggregate_id/locale_info" => "/surveys_api/v1/locale_info#index"
    get "/v1/:survey_aggregate_id/survey_mongo_id" => "/surveys_api/v1/mongo_mapper#survey_mongo_id"
  end

  namespace :comment_summary_service_api do
    get "/v1/survey/:survey_id/metadata" => "/comment_summary_service_api/v1/metadata#fetch_survey_metadata"
    get "/v2/survey/:survey_id/metadata" => "/comment_summary_service_api/v1/metadata#fetch_survey_metadata_v2"
    get "/v1/survey/:survey_id/report/:report_mongo_id/metadata" => "/comment_summary_service_api/v1/metadata#fetch_report_metadata"
    get "/v1/survey/:survey_id/comparison_metadata" => "/comment_summary_service_api/v1/metadata#fetch_comparison_metadata"
  end

  namespace :survey_reporting_api do
    get "/v1/surveys/:survey_id/reports/:report_id/report-metadata" => "/survey_reporting_api#report_metadata"
    get "/v1/surveys/:survey_id/reports/:report_id/report-configuration" => "/survey_reporting_api#report_configuration"
    get "/v1/surveys/:survey_id/reports/:report_id/available-reports" => "/survey_reporting_api#available_reports"
    get "/v1/surveys/:survey_id/reports/:report_id/user-permissions" => "/survey_reporting_api#user_permissions"
    get "/v1/surveys/:survey_id/reports/:report_id/report-data/participation" => "/participation_report#survey_reporting_api_participation"
    get "/v1/surveys/:survey_id/reports/:report_id/report-data/participation/:id" => "/participation_report#survey_reporting_api_participation"
    get "/v1/surveys/:survey_id/reports/:report_id/report-data/heatmap" => "/demographic_report#survey_reporting_api_heatmap"
    get "/v1/surveys/:survey_id/reports/:report_id/report-data/factor-insight/:id" => "/factor_insight_report#survey_reporting_api_factor_insight"
    get "/v1/surveys/:survey_id/reports/:report_id/report-data/question-insight/:id" => "/question_insight_report#survey_reporting_api_question_insight"
  end

  namespace :factors_api do
    get "/v1/surveys/:survey_aggregate_id/factors" => "/factors_api/v1/factors#index"
    get "/v1/surveys/:survey_aggregate_id/questions_with_factors_info" => "/factors_api/v1/questions_with_factors#get_questions_with_factors_info"
    put "/v1/surveys/:survey_aggregate_id/index_factor" => "/factors_api/v1/factors#set_index_factor"
    put "/v1/surveys/:survey_aggregate_id/add_question_to_factors" => "/factors_api/v1/questions_with_factors#add_question_to_factors"
    put "/v1/surveys/:survey_aggregate_id/survey_factor/:factor_aggregate_id/update" => "/factors_api/v1/factors#update"
    delete "/v1/surveys/:survey_aggregate_id/survey_factor/:factor_aggregate_id" => "/factors_api/v1/factors#destroy"
    delete "/v1/surveys/:survey_aggregate_id/remove_questions_from_factor" => "/factors_api/v1/questions_with_factors#remove_questions_from_factor"
    post "/v1/surveys/:survey_aggregate_id/survey_factor" => "/factors_api/v1/factors#create"
    get "/v1/:factor_aggregate_id/factor_mongo_id" => "/factors_api/v1/mongo_mapper#factor_mongo_id"
  end

  namespace :survey_questions_api do
    get "/v1/:survey_aggregate_id/stq_mongo_ids" => "/survey_questions_api/v1/mongo_mapper#stq_mongo_ids"
    get "/v1/:demographic_aggregate_id/select_option_mongo_ids" => "/survey_questions_api/v1/mongo_mapper#demographic_select_option_mongo_ids"
    get "/v1/:survey_aggregate_id/reportable_rating_questions" => "/survey_questions_api/v1/reportable_rating_questions#rating_questions_data"
  end

  namespace :enps_api do
    get "/surveys/:survey_aggregate_id/questions_with_enps_info" => "/enps_api/enps#get_questions_with_enps_info"
    get "/surveys/:survey_aggregate_id/get_enps_question" => "/enps_api/enps#get_enps_5_point_question"
    get "/surveys/:survey_aggregate_id/get_enps_question_v2" => "/enps_api/enps#get_enps_question"
    put "/surveys/:survey_aggregate_id/set_enps_question" => "/enps_api/enps#set_enps_question"
    put "/surveys/:survey_aggregate_id/set_enps_11_question" => "/enps_api/enps#set_enps_11_question"
    put "/surveys/:survey_aggregate_id/disable_enps" => "/enps_api/enps#disable_enps"
  end

  namespace :survey_settings_api do
    put "/v1/surveys/:survey_aggregate_id/set_likert_scale_color" => "/survey_settings_api/v1/survey_colors#set_likert_scale_color"
  end

  namespace :survey_summary_api do
    namespace :v1 do
      get "/response/:response_aggregate_id" => "/survey_summary_api/v1/survey_summary#response_data"
      get "/user/:user_aggregate_id" => "/survey_summary_api/v1/survey_summary#user_data"
      get "/survey/:survey_aggregate_id" => "/survey_summary_api/v1/survey_summary#survey_data"
      get "/survey/:survey_aggregate_id/recipient_count" => "/survey_summary_api/v1/survey_summary#survey_recipient_count"
      post "/survey/:survey_aggregate_id/share_survey_summary" => "/survey_summary_api/v1/survey_summary#share_survey_summary"
    end
  end

  namespace :continuous_reporting_api do
    namespace :v1 do
      get "/benchmarks" => "/continuous_reporting_api/v1/benchmarks#index"
      get "/account" => "/continuous_reporting_api/v1/account#index"
      get "/demographics/:survey_id" => "/continuous_reporting_api/v1/demographics#index"
    end

    namespace :v2 do
      get "/account" => "/continuous_reporting_api/v2/account#index"
      get "/account-demographics" => "/continuous_reporting_api/v2/account_demographics#index"
      get "/account-demographic-values/:demographic_id" => "/continuous_reporting_api/v2/account_demographics#values"
      get "/account-demographic-labels" => "/continuous_reporting_api/v2/account_demographics#labels"
      get "/turnover-and-headcount" => "/continuous_reporting_api/v2/employee_data#turnover_and_headcount"
      get "/retention" => "/continuous_reporting_api/v2/employee_data#retention_rate"
      get "/sentiment" => "/continuous_reporting_api/v2/sentiment#index"
      get "/survey-questions" => "/continuous_reporting_api/v2/survey_questions#index"
      get "/metadata" => "/continuous_reporting_api/v2/metadata#index"
      get "/correlations" => "/continuous_reporting_api/v2/correlations#index"
      get "/current-head-count" => "/continuous_reporting_api/v2/employee_data#current_head_count"
      get "/employee-data" => "/continuous_reporting_api/v2/employee_data#employee_data"
      get "/turnover-groups" => "/continuous_reporting_api/v2/turnover_groups#index"
      get "/trend-configuration" => "/continuous_reporting_api/v2/trend_configuration#index"
      get "/selectable-surveys" => "/continuous_reporting_api/v2/selectable_surveys#index"
      get "/engage-report-link" => "/continuous_reporting_api/v2/engage_report_link#index"
      get "/account-has-module" => "/continuous_reporting_api/v2/account_has_module#index"
      get "/question-scores" => "/continuous_reporting_api/v2/question_scores#index"
    end
  end

  namespace :sent_email do
    get "/translations/:response_id/:template_type" => "/sent_email/translations#show", :as => "translations"
  end

  namespace :communications_api do
    resources :surveys do
      resources :email_templates, only: [:show, :update] do
        get "subject"
        get "raw_content"
        put "send_test"
      end

      get "sms_templates/:template_type", to: "sms_templates#content"
      post "sms_templates/:template_type", to: "sms_templates#send_sms"

      namespace :raw_data_extract_statement do
        get "/" => "/communications_api/statements#raw_data_extract_statement", :as => "raw_data_extract_statement"
        put "/update" => "/communications_api/statements#update_raw_data_extract_statement", :as => "update_raw_data_extract_statement"
        put "/unset" => "/communications_api/statements#unset_raw_data_extract_statement", :as => "unset_raw_data_extract_statement"
      end

      namespace :privacy_statement do
        get "/" => "/communications_api/statements#privacy_statement", :as => "privacy_statement"
        put "/update" => "/communications_api/statements#update_privacy_statement", :as => "update_privacy_statement"
        put "/unset" => "/communications_api/statements#unset_privacy_statement", :as => "unset_privacy_statement"
      end

      resource :welcome_page, only: [:show, :update]
      resource :confidentiality_page, only: [:show, :update]

      resource :metadata do
        get "sidebar_navigation_menu"
        get "title_block"
        get "supported_locales"
        get "admin_emails"
      end
    end
  end

  resources :account, only: [:show, :update, :new, :create] do
    get "logo"
    get "small_logo"
    get "settings" => "account_settings#edit"
    patch "settings" => "account_settings#update"
    delete "remove_logo" => "account_settings#remove_logo"
    put "update_authentication" => "account_authentications#update"
    resources :users, only: [:index, :new, :create, :edit, :update] do
      collection do
        get "search"
        get "check"
        get "hierarchy_modal_skip"
      end
      post "demographics" => "users#update_demographics"
    end
    get "action_frameworks/activity_feed" => "action_frameworks/activity_feed#index"

    namespace :survey_programs do
      post "/create" => "/survey_programs#create"
    end

    namespace :account_dashboard do
      resources :admin, controller: "admin", only: [:index]
      get "/admin/*front_end_route", to: "admin#index"
      resources :users, controller: "users", only: [:index, :create, :destroy]
    end

    resources :demographics do
      collection do
        get :active_account_demographics
      end
    end

    resources :surveys, only: [:index, :create], controller: "legacy_surveys"

    post "/email_bounced/:user_aggregate_id/clear" => "email_bounced#clear"
    post "/email_bounced/bulk_clear" => "email_bounced#bulk_clear"

    resources :upload_employees_files, only: [:index]

    namespace :employee_admin_api do
      resources :employee_statuses, only: [:index]
      resources :super_users, only: [:index]
      resources :account_demographics_admin_codes, only: [:show]
    end

    # This is meant to replace "users#send_invite" after we release the new user list UI
    resources :send_email_invites, only: [:create] do
      collection do
        post :invite_multiple
      end
    end
    # This new endpoint is meant to replace the the 'user_downloads' endpoint after we release the new user list UI
    resources :export_employees, only: [:create, :show]
    resources :user_downloads, only: [:create, :show]
    resources :permission_sets, only: [:show, :update, :destroy, :create, :index]

    namespace :employee_import, path: "/users/import" do
      resource :import_notification, only: [:show] do
        get "status"
      end
      resource :migration_notification, only: [:show]

      resource :historical_summary, only: [:show], controller: :historical_summary do
        get "bulk_import_log", to: "historical_summary#bulk_import_log", as: :bulk_import_log
        get "summary", to: "historical_summary#summary", as: :summary
      end
    end

    namespace :employee_imports_api do
      namespace :import_workflow, controller: :import_workflow do
        get :initial_page_data
        get :upload_file_page_data
        post :upload_file
        get :review_page_data
      end
    end

    resource :integrations, only: [:show, :update] do
      get :edit_bamboo, on: :collection
      get :edit_namely, on: :collection
      get :edit_workday, on: :collection
      get :edit_successfactors, on: :collection
      get :edit_securefile, on: :collection
      get :successfactors_definition, to: "integrations#successfactors_definition"

      get :support, to: "integrations#support"
      get :fetch_hris_report, to: "integrations#fetch_hris_report"

      post :turn_all_integrations_off, on: :collection
    end

    resource :integration_sync, only: [:create, :show]

    resources :saml_integrations, only: [:index, :update] do
      collection do
        post :create
      end
      get :activate
      get :deactivate
    end

    get "performance_configuration" => "performance_configuration#index"
    post "performance_configuration" => "performance_configuration#update"
    post "performance_configuration/migrate_perform_mappings" => "performance_configuration#migrate_perform_mappings"

    get "gdpr_requests" => "admin/gdpr_requests#index"

    resource :gdpr_requests, controller: "admin/gdpr_requests", only: [:index] do
      post "create_request" => "admin/gdpr_requests#create_request"
      post "approve_request" => "admin/gdpr_requests#approve_request"

      get "validate_request" => "admin/gdpr_requests#validate_request"
      post "validate_employees" => "admin/gdpr_requests#validate_employees"

      get "*path" => "admin/gdpr_requests#index"
    end

    get "notifications" => "notifications#index"

    resource :notifications, only: [:index] do
      post "slack/start_sync" => "notifications#slack_start_sync"
      get "slack/check_sync" => "notifications#slack_check_sync"
      post "slack/send_test_message" => "notifications#send_slack_test_message"
      get "msteams/send_test_message" => "notifications#send_msteams_test_message"
      get "msteams/send_legacy_test_message" => "notifications#send_msteams_legacy_test_message"
      get "msteams/send_flow_test_message" => "notifications#send_msteams_flow_test_message"
      put "slack/toggle_performance_notifications" => "notifications#toggle_slack_performance_notifications"
      delete "slack" => "notifications#remove_slack"

      get "*path" => "notifications#index"
    end

    namespace :report_settings, module: "account_report_settings" do
      resource :admin, only: [:show], controller: "admin"
      get "/admin/*front_end_route", to: "admin#show"
      resource :comments, only: [:show, :update]
    end

    get "program_index" => "survey_browser/index#index"

    resources :survey_index, controller: "survey_browser/index" do
      collection do
        post "duplicate"
        post "archive"
        post "unarchive"
      end
    end

    post "surveys_stats" => "account/surveys_stats#create"
    get "surveys_stats/:id" => "account/surveys_stats#show", :as => "show_surveys_stats"

    get :personal_growth_plan, to: "action_frameworks/personal/growth_plan#overview"
    get "personal_growth_plan/:focus_area_id/create_action", to: "action_frameworks/personal/growth_plan#create_action"

    namespace :home do
      post "/tasks", to: "tasks#index"
      get "/available-reports", to: "available_reports#index"
      get "/tasks", to: redirect("/app/home")
      get "/completed_tasks", to: redirect("/app/home")
      get "/activity", to: "app#activity"
      get "/reports_index", to: "reports#index"
      get "/performance", to: "performance#index"
      get "/action_plans_index", to: redirect("account/%{account_id}/home/<USER>"), as: "legacy_action_plans_index"
      get "/action_plans", to: "action_plans#index", as: "action_plans_index"
      get "/action_plans/*front_end_route", to: "action_plans#index", as: "action_plans_frontend"
      get "/account_dashboard", to: redirect("/app/home")
      get "/*front_end_route", to: redirect("/app/home")
    end

    # TODO: deprecated employee_homepage urls, kept to keep backwards compatibility
    namespace :task_dashboard do
      get "/your_tasks", to: "app#tasks"
      get "/reports", to: "app#legacy_reports"
      get "/completed_tasks", to: "app#completed_tasks"
    end

    get "survey_templates", to: "survey_browser/templates#index"

    get "survey_templates/admin", to: "survey_templates/account_admin#show"
    get "survey_templates/export", to: "survey_templates/export#bulk"

    get "template_library", to: redirect("account/%{account_id}/survey_templates")
  end

  namespace :backend_metadata_api do
    resources :accounts, only: [:show]
    resources :surveys, only: [:show]
    get "/comments/aggregate_ids", to: "/backend_metadata_api/comments#aggregate_ids"
  end

  namespace :comment_replies do
    post "/email/generate", to: "/comment_replies/comment_reply_email#generate"
  end

  namespace :reporting_api do
    get "/survey/:survey_id/responses", to: "/reporting_api/responses#get"
    get "/surveys", to: "/reporting_api/surveys#get"
    post "/token/generate", to: "/reporting_api/token#generate"
  end

  namespace :survey_insights, path: "/survey-insights-api/v1" do
    get "survey/:survey_id/report/:report_id/question/:stq_id", to: "/survey_insights/question_insights#get"
  end

  namespace :survey_templates do
    get "/accounts/:account_id/templates/:template_id/preview", to: "/survey_templates/template_preview#show", as: :preview_template
    put "/accounts/:account_id/templates/:template_id/preview", to: "/survey_templates/template_preview#no_op", as: :preview_no_op
    delete "/accounts/:account_id/templates/:template_id/preview", to: "/survey_templates/template_preview#no_op"
    get "/accounts/:account_id/templates/:template_id/preview/rate", to: "/survey_templates/template_preview#rate", as: :preview_rate
    get "/accounts/:account_id/templates/:template_id/preview/thanks", to: "/survey_templates/template_preview#thanks", as: :preview_thanks
    post "/accounts/:account_id/templates/:template_id/preview/start", to: "/survey_templates/template_preview#start", as: :preview_start
    get "/accounts/:account_id/templates/:template_id/preview/confidentiality", to: "/survey_templates/template_preview#confidentiality_template_content", as: :preview_confidentiality
    post "/templates/:template_id", to: "/survey_templates/sync#update"
    delete "/templates/:template_id", to: "/survey_templates/sync#remove"
    post "/delete_templates", to: "/survey_templates/sync#remove_older_than"
    post "/accounts/:account_id/templates/:template_id/duplicate", to: "template_clone#create", as: :duplicate_template
  end

  namespace :home do
    get "tasks", to: "legacy_tasks#index"
    get "completed_tasks", to: "legacy_tasks#completed_tasks"
    get "reports", to: "legacy_reports#index"
    get "/*front_end_route", to: "app#index"
  end

  namespace :my do
    get "notifications", to: "redirect_with_current_account#notifications"
    get "action_plans", to: "redirect_with_current_account#action_plans"
    get "performance", to: "redirect_with_current_account#performance"
    get "surveys", to: "redirect_with_current_account#surveys"
    get "reports", to: "redirect_with_current_account#reports"
    get "surveys_new", to: "redirect_with_current_account#surveys_new"
    get "account_admin", to: "redirect_with_current_account#account_admin_users"
    get "locale" => "locale#show"

    namespace :profile_data do
      get "intercom" => "intercom#show"
      get "amplitude" => "amplitude#show"
      scope "zendesk" do
        root to: "zendesk#show"
        get "authenticate" => "zendesk#authenticate"
      end
    end
    get "user-settings" => "user_settings#show"
    get "profile", to: redirect("my/user-settings", status: 301)
    put "profile" => "user_settings#update"
    put "upload_profile_image" => "user_settings#upload_profile_image"
    put "delete_profile_image" => "user_settings#delete_profile_image"
  end

  resources :profiles, only: [:show]

  namespace :streamlined_onboarding_survey do
    post "/create_and_launch", to: "surveys#create_and_launch"
  end

  # Security
  devise_scope :user do
    get "users/sign_in", to: "sessions#new"
    get "session/sign_in", to: "sessions#new"
    get "session/sign_out", to: "sessions#destroy", as: :sign_out
    get "session/subdomain", to: "sessions#subdomain"
    get "my/password", to: "registrations#edit", as: "change_password"
    put "my/password", to: "registrations#update"
    get "session/password/reset_instructions", to: "passwords#reset_instructions"
    get "session/password/expired_link", to: "passwords#expired_link"
    get "session/google_auth_error", to: "sessions#google_auth_error"
    get "session/new_sign_in", to: "sessions#new_sign_in"
    # For new set password with new auth-ui
    put "/private_api/set_password/:aggregate_id" => "passwords#set_password"
  end
  post "passwords/valid", to: "passwords_validator#valid"
  devise_for :users, path: "session", controllers: {omniauth_callbacks: "omniauth_callbacks", sessions: "sessions", registrations: "registrations", passwords: "passwords", unlocks: "unlocks"}

  use_doorkeeper do
    skip_controllers :applications, :authorized_applications
    controllers authorizations: "authorizations"
  end
  get "skilljar/user" => "skilljar_user#show", :constraints => {format: "json"}

  resources :users, only: [:show] do
    member do
      get "activate"
      post "clear_email_bounce"
      get "deactivate"
      get "reinstate"
      post "set_active"
      post "set_inactive"
      get "invite"
      post "send_invite" => "users#send_invite"
      get "demographics"
      post "demographics" => "users#update_demographics"
      get "permissions"
    end

    resources :grant_permissions, only: [:create, :destroy]
  end

  get "slack/bind_account", to: "slack_sessions#bind_account"

  get "identity" => "identity#show", :constraints => {format: "json"}
  post "identity/authorization" => "authorization#graphql"
  get "identity/query" => "identity_query#exists"
  get "hris/:adaptor/callback", to: "hris#oauth_redirect"

  get "survey_demographics/:survey_id/demographics" => "survey_demographics#index", :as => :survey_demographics, :constraints => {format: "json"}
  get "survey_demographics/:survey_id/demographics/:stq_id/options" => "survey_demographics#options", :as => :survey_demographics_options, :constraints => {format: "json"}

  resources :upload_employees_files, only: [:show, :update]

  match "saml/callback/:sd" => "saml#callback", :via => [:get, :post]
  get "saml/metadata/:sd" => "saml#metadata"
  get "saml/:sd" => "saml#show", :as => :saml_signin

  get "slack/:account_aggregate_id/add" => "slack#add", :as => :add_to_slack
  get "slack/:account_aggregate_id/add_test" => "slack#add_test", :as => :add_to_slack_test
  get "slack/callback/:sd" => "slack#callback", :as => :slack_callback

  # Survey Support Functions - Resending and Resetting Surveys
  resources :survey_support, only: [:show] do
    get "resend/:response_id" => "survey_support#resend", :as => :resend
    get "reset/:response_id" => "survey_support#reset", :as => :reset
    get "search"
  end

  # These routes are consumed by frontends in other codebases (eg.
  # big-frontend-repo or performance-ui), as well as Murmur itself for its own
  # FE components.
  # The intention here is to have a set of broadly-scoped APIs that are
  # specifically demarcated as being fine to use from the frontend, from a
  # browser making requests into the system via web-gateway. We're trying to
  # keep the APIs here to be those with a platform-level focus, with those
  # defined so far being useful for almost every page; for domain-specific APIs,
  # we'd instead recommend creating your own namespace like the other *_api routes.
  namespace :api do
    namespace :murmur do
      get "supported_products" => "supported_products#show"
      get "admin_sidebar" => "admin_sidebar#show"
      get "employee_imports" => "employee_imports#index"

      get "employee_integration/app_data" => "employee_integration#app_data"
      put "employee_integration/set_integration" => "employee_integration#set_integration"
      post "employee_integration/disable_integration" => "employee_integration#disable_integration"
      get "employee_integration/profile" => "employee_integration#profile"
      get "employee_integration/hris_report" => "employee_integration#hris_report"
      get "employee_integration/permitted" => "employee_integration#permitted"

      get "manager_learning" => "manager_learning#show"

      get "feature_flags" => "feature_flag#index"
    end
  end

  namespace :employees_api do
    get "/update_employee_page" => "employees#update_employee_page"
    get "/create_employee_page" => "employees#create_employee_page"
    get "/manager_selection" => "employees#manager_selection"

    get "/demographics/:id/values" => "employees#demographic_values"

    post "update_employee" => "employees#update"
    post "create_employee" => "employees#create_employee"
    get "/employees/:id/deactivate_info" => "employees#deactivate_info"
    post "/deactivate_employee" => "employees#deactivate"
    post "/activate_employee" => "employees#activate"

    namespace :admin do
      resources :account, only: [] do
        resources :employee, only: [:create] do
          post "/activate" => "employees#activate"
          post "/deactivate" => "employees#deactivate"
        end
      end
    end
  end

  ## APIs used by the Core services shim
  namespace :feature_flags_api do
    get "for_user" => "for_user#index"
  end

  namespace :tasks_api do
    get "for_user" => "for_user#index"
    put "for_user/:task_id" => "for_user#update"
  end

  namespace :unified_home_data_api do
    get "data" => "home_data#index"

    get "/superuser/account/:account_id/nav" => "superuser_nav_by_account#index", :as => "superuser_nav_by_account"
    get "/superuser/survey/:survey_id/nav" => "superuser_nav_by_survey#index", :as => "superuser_nav_by_survey"
  end

  namespace :recognition_api do
    get "user" => "users#show"
    post "users" => "users#index"
    get "reports" => "users#reports"
    post "search" => "users#search"
  end

  namespace :unified_home do
    get "surveys" => "surveys#index"
    get "employees" => "employees#index"
    get "survey_reports" => "survey_reports#show"
  end

  namespace :roles_api do
    resource :permissions, only: [:show]

    resources :accounts, only: [] do
      resources :account_admins, only: [:index]
      resources :departments, only: [:index]
      resources :demographics, only: [:index] do
        resources :demographic_values, path: "values", only: [:index]
      end
      resources :survey_data_analysts, only: [:index], controller: "accounts/survey_data_analysts"
      resources :survey_comment_repliers, only: [:index], controller: "accounts/survey_comment_repliers"
      resources :survey_creators, only: [:index], controller: "accounts/survey_creators"
    end

    resources :employees, only: [:show] do
      resource :account_admin, only: [:show, :update]
      resource :survey_data_analyst, only: [:show, :update]
      resource :survey_comment_replier, only: [:show, :update]
      resource :survey_creator, only: [:show, :update]
    end
  end

  namespace :sales_experience_api do
    post "survey" => "survey#create"
    post "survey/:survey_id/benchmarks" => "survey#connect_benchmarks"
    post "survey/:survey_id/enable_enps" => "survey#enable_enps"

    post "import" => "survey#import_responses"
    get "status" => "survey#import_status"
    delete "survey" => "survey#destroy"

    post "survey/:survey_id/reports" => "reports#create"
    post "survey/:survey_id/questions" => "survey#add_question"

    resources :features, only: [:update]
    put "features/:id/update_configuration" => "features#update_configuration"

    post "demographics_mapping" => "demographics_mapping#update"
    post "account/:account_id/demographics_mapping" => "demographics_mapping#update" # TODO Remove this route https://cultureamp.atlassian.net/browse/SD-1033

    put "upload_profile_images" => "profile_images#upload_profile_images"
  end

  # Routes not available in production. Note that these will be available
  # in staging, so take care.
  unless Rails.env.production?
    resources :test_records, only: [:index, :show] do
      post "generate"
    end
  end

  # Event and Metrics capture point
  # This should be just basically a proxy to splunk with some basic input validation
  resource :client_events, only: [:create]

  # Development and Utility Routes
  if Rails.env.development? || Rails.env.test? || Rails.env.preproduction?
    get "/dev" => "development#index"
    get "/dev/error" => "development#error"
    get "/dev/login/:id" => "development#login"
    get "/dev/forbidden" => "development#forbidden"
    get "/dev/links" => "development#links"
    get "/dev/redirect/:id" => "development#redirect", :as => :dev_redirect
    get "/dev/test-http-sigs" => "test_http_sigs#respond"
    post "/dev/test-http-sigs" => "test_http_sigs#respond"
    get "/dev/:name" => "development#subdomain"
  end

  # Demonstration Route
  get "/demo/capture" => "demo#capture"
  get "/demo" => "demo#index"
  post "/demo" => "demo#redirect"
  get "/demo/user", to: "demo#demo_as_employee", as: "demo_as_employee"

  resources :support_tickets, only: [:new, :create]

  get "slack_credentials/:user_aggregate_id" => "slack_credentials#show"
  get "slack_credentials/find_by_slack_id/:slack_id" => "slack_credentials#find_by_slack_id"

  # Homepage
  root to: "overview#index"
  get "/surveys" => "overview#index"

  # Placeholders for local development
  get "/app/home", to: "local_placeholder#unified_home", as: "unified_home"
  get "/app/auth", to: "local_placeholder#auth", as: "auth"
  get "/app/:app_name", to: "local_placeholder#index"
  get "/app/:app_name/*path", to: "local_placeholder#index"
  get "/performance", to: "local_placeholder#performance", as: "performance_product"
  get "/performance/1-1", to: "local_placeholder#performance", as: "one_on_ones"
  get "/performance/*path", to: "local_placeholder#performance"

  post "dismissible_block" => "dismissible#dismiss"

  # External micro-frontends
  direct :survey_overview do |account_id, survey_id|
    "/app/survey-configuration/#{account_id}/survey/#{survey_id}/overview"
  end

  direct :confidentiality do |account_id, survey_id|
    "/app/survey-configuration/#{account_id}/survey/#{survey_id}/confidentiality"
  end

  direct :reporting_factors do |account_id, survey_id|
    "/app/survey-config/#{account_id}/survey/#{survey_id}/reporting-factors"
  end

  direct :configure_factors do |account_id, survey_id|
    "/app/survey-config/#{account_id}/survey/#{survey_id}/reporting-factors#configure-factors"
  end

  direct :import_questions_select_file do |account_id, survey_id|
    "/app/survey-configuration/#{account_id}/survey/#{survey_id}/import-questions/select-file"
  end

  direct :survey_capture_app_code do |survey_aggregate_id, survey_capture_params|
    survey_capture_path = "/app/survey-capture/#{survey_aggregate_id}/code"
    [survey_capture_path, survey_capture_params.to_query].join("?")
  end

  # Redirect
  ## Training links redirect for region specific purposes
  get "/redirect/training" => "redirect#training_url"
  get "/redirect/training/*page" => "redirect#training_url"

  namespace :comparisons_api do
    get "/v1/report_comparisons/:survey_id" => "/comparisons_api_v1#get_report_comparisons", :as => "report_comparisons"
    get "/v1/auto_comparisons/:survey_id" => "/comparisons_api_v1#get_auto_comparisons", :as => "auto_comparisons"
    get "/v1/importable_comparisons/:survey_id" => "/comparisons_api_v1#importable_comparisons", :as => "importable_comparisons"
    get "/v1/comparison_sync_status/:survey_id/:job_id" => "/comparisons_api_v1#sync_status", :as => "sync_status"
    get "/v1/external_comparison_scores/:survey_aggregate_id" => "/comparisons_api/v1/external_comparisons_scores#survey_benchmarks_score"
    post "/v1/set_comparison_visibility/:survey_id/:comparison_id" => "/comparisons_api_v1#set_comparison_visibility", :as => "set_comparison_visibility", :constraints => {comparison_id: /[0-z.]+/}
    post "/v1/rename_comparison/:survey_id/:comparison_id" => "/comparisons_api_v1#rename_comparison", :as => "rename_comparison", :constraints => {comparison_id: /[0-z.]+/}
    delete "/v1/delete_comparison/:survey_id/:comparison_id" => "/comparisons_api_v1#delete_comparison", :as => "delete_comparison", :constraints => {comparison_id: /[0-z.]+/}
    post "/v1/reorder_comparisons/:survey_id" => "/comparisons_api_v1#reorder_comparisons", :as => "reorder_comparisons"
    post "/v1/set_default_comparison/:survey_id" => "/comparisons_api_v1#set_default_comparison", :as => "set_default_comparison"
    post "/v1/request_demographic_matching/:survey_id/:comparison_id" => "/comparisons_api_v1#request_demographic_matching", :as => "request_demographic_matching", :constraints => {comparison_id: /[0-z.]+/}
    put "/v1/edit_comparison/:survey_id/:comparison_id" => "/comparisons_api_v1#edit_comparison", :as => "edit_comparison", :constraints => {comparison_id: /[0-z.]+/}
    get "/v1/export_demographic_comparison/:survey_id/:comparison_id" => "/comparisons_api_v1#export_demographic_comparison", :as => "export_demographic_comparison", :constraints => {comparison_id: /[0-z.]+/}
    get "/v1/export_demographic_comparison_status/:survey_id/:job_id" => "/comparisons_api_v1#export_demographic_comparison_status", :as => "export_demographic_comparison_status"
    get "/v1/comparison_metadata/:survey_id/:comparison_id" => "/comparisons_api_v1#comparison_metadata", :as => "comparison_metadata", :constraints => {comparison_id: /[0-z.]+/}
    get "/v1/matched_demographics/:survey_id/:comparison_id" => "/comparisons_api_v1#matched_demographics", :as => "matched_demographics", :constraints => {comparison_id: /[0-z.]+/}
  end

  namespace :report_sharing_api do
    namespace :v1 do
      get "/:survey_id/create_report_page", to: "/report_sharing_api/v1/report_configuration_page#create_report_page", as: "create_report_page"
      get "/:survey_id/edit_report_page/:report_id", to: "/report_sharing_api/v1/report_configuration_page#edit_report_page", as: "edit_report_page"
      get "/:survey_id/demographics", to: "/report_sharing_api/v1/demographics#demographics", as: "demographics"
      get "/:survey_id/demographics/:demographic_stq_id/values", to: "/report_sharing_api/v1/demographics#demographic_values", as: "demographic_values"
    end
  end

  namespace :dimensions_api do
    namespace :v1 do
      get "/survey/:survey_aggregate_id/performance_rating_cycles" => "/dimensions_api/v1/performance_cycle#performance_rating_cycles"
      post "/survey/:survey_aggregate_id/link_performance_rating_cycle/:performance_cycle_aggregate_id" => "/dimensions_api/v1/performance_cycle#link_performance_rating_cycle"
      post "/survey/:survey_aggregate_id/unlink_performance_rating_cycle/:performance_cycle_aggregate_id" => "/dimensions_api/v1/performance_cycle#unlink_performance_rating_cycle"
    end
  end

  namespace :sms_service do
    namespace :api do
      namespace :v1 do
        post ":schedule_id/callbacks/status_update" => "/sms_service/api/v1/callbacks#status_update"
      end
    end
  end

  namespace :survey_capture_api do
    post "/:survey_aggregate_id/response_for_code" => "/survey_capture_api/kiosk_mode#response_for_code"
    get "/:survey_aggregate_id/response_for_code" => "/survey_capture_api/kiosk_mode#response_for_code"
    get "/:survey_aggregate_id/kiosk_page_data" => "/survey_capture_api/kiosk_mode#kiosk_page_data"
  end

  namespace :reporting_privacy_rules_api do
    get "/account/:account_aggregate_id/surveys/:survey_aggregate_id/rules" => "/reporting_privacy_rules_api/rules#rules"
  end

  namespace :multi_account_surveying_api do
    namespace :v1 do
      resources :org_surveys, only: [] do
        get :get_org_survey_data, on: :collection, path: "accounts/:account_aggregate_id/surveys/:survey_aggregate_id"
      end

      resources :subsidiary_surveys, only: [] do
        post :upsert_survey, on: :collection, path: "accounts/:account_aggregate_id/upsert_survey"
        post :upsert_section, on: :collection, path: "accounts/:account_aggregate_id/surveys/:survey_aggregate_id/upsert_section"
        post :upsert_factor, on: :collection, path: "accounts/:account_aggregate_id/surveys/:survey_aggregate_id/upsert_factor"
        post :delete_section, on: :collection, path: "accounts/:account_aggregate_id/surveys/:survey_aggregate_id/delete_section"
        post :delete_survey_question, on: :collection, path: "accounts/:account_aggregate_id/surveys/:survey_aggregate_id/delete_survey_question"
        post :upsert_question, on: :collection, path: "accounts/:account_aggregate_id/surveys/:survey_aggregate_id/upsert_question"
        post :upsert_question_display_condition, on: :collection, path: "accounts/:account_aggregate_id/surveys/:survey_aggregate_id/upsert_question_display_condition"
      end
    end
  end

  get "surveys/:survey_aggregate_id/kiosk_message_component_props" => "kiosk_configuration#kiosk_message_component_props"
  put "surveys/:survey_aggregate_id/kiosk_message_and_code_format" => "kiosk_configuration#update_kiosk_message_and_code_format"

  # CTA link - GTM marketing emails
  get "/latest_eligible_survey/performance_demographics_setup" => "marketing_experiments#redirect_to_latest_eligible_survey_for_performance_ratings"

  get "survey_participants_import_api/:survey_id/survey_data" => "participant_import#survey_data"

  # This is a stub for cucumber tests
  if Rails.env.test?
    get "/api/manager-lab/skills_coach_nav", to: proc { [200, {}, [""]] }
  end
end
