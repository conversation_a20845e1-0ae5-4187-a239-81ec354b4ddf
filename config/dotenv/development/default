# Fallback env file for development environment
MURMUR_SMTP_ADDRESS=localhost
SESSION_COOKIE_NAME=_murmur_session_all
MURMUR_SMTP_PORT=4278
AUTH_API_PASSWORD=password
PERFORMANCE_ROOT=localhost:4000/performance
RECAPTCHA_V2_SITE_KEY=6LdfG7YUAAAAAN13iiOHhZPvRk4W58UilcByJhuF
RECAPTCHA_V2_SECRET_KEY=6LdfG7YUAAAAAK_xvpwk6PCR3MKFdGzOZWlKCmTV
INTERCOM_APP_ID=elbskuxi
INTERCOM_API_SECRET=bCTT2XA9ofhgUzzSDZhGoF0OkQBsR0QRSz7O5U8J
ZENDESK_APP_ID=ccdf5ac4-57b6-446f-a1ae-59dbd43f301b
ZENDESK_JWT_SECRET=655C4CD3CAA90F2FA0DFDD84E98FBF1F6E545F6D2F3EB11B3E05420FAA7F7D09
IDENTITY_ROOT=localhost
SKILLJAR_TRAINING_LINK=https://www.cultureamptraining.com/
MURMUR_REGION=us
EMPLOYEE_DATA_SERVICE_HOST=http://localhost:3500
PERMISSIONS_SERVICE_HOST=http://localhost:8085
PROGRAMS_SERVICE_HOST=http://localhost:4321
FUSION_AUTH_HOST=http://localhost:9011
FUSION_AUTH_API_KEY=bf69486b-4733-4470-a592-f1bfce7af580
FARM=local
AUTH_HTTP_SIG_SURVEY_CONVERSATIONS_SERVICE_PUBKEY="$(cat .development-keys/survey-conversations-service/http-signatures/public.pem)"
AUTH_HTTP_SIG_SURVEY_TEMPLATE_LIBRARY_PUBKEY="$(cat .development-keys/survey-template-library/http-signatures/public.pem)"
AUTH_HTTP_SIG_MANAGER_LAB_API_PUBKEY=-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxB/Jv/3ugdRdctGRhZZQ\n4FpkejfZVk2AxKYc81Vd1eSzyUI5h9vTqC3c/cUSa0QG93SivOWWas+/F45J+hgJ\nftx79UYLDVFN1u0Dqlj5sm0ZIFUdWIriECjb+d9fUryjceuERFu5KjOtFUn3+nnv\nTyEkVKO//8aE4UjJnh+du8f2aayiIsEwTDm5nYbbpxOV65GU3Bwb6/WQPPXTz+yn\nxDGi5ikkXunW9CiG4yUqqmyQc/GI0zW95DKDB8MH5ufbxMSyFh3b5NBeV4L0c/Nu\nYerBmqthZaCW8yIrRDaSEFDfR75x9G5ElpQRXiwew2iipsMIkOGvV7qL6Lp88Aj6\n2QIDAQAB\n-----END PUBLIC KEY-----
AUTH_HTTP_SIG_SURVEY_SCORES_PUBKEY="$(cat .development-keys/survey-scores/http-signatures/public.pem)"
AUTH_HTTP_SIG_SURVEY_SUMMARY_PUBKEY="$(cat .development-keys/survey-summary/http-signatures/public.pem)"
AUTH_HTTP_SIG_COMMENT_PUBLISHER_PUBKEY="$(cat .development-keys/comment-publisher/http-signatures/public.pem)"
AUTH_HTTP_SIG_SURVEY_DESIGN_PUBKEY="$(cat .development-keys/survey-design/http-signatures/public.pem)"
AUTH_JWT_PUBKEY="$(cat .development-keys/web-gateway/jwt/public.pem)"
AUTH_JWT_PRIVKEY="$(cat .development-keys/web-gateway/jwt/private.pem)" # Only used for a dev rake task
VAULT_ADDR=http://localhost:8200
VAULT_TOKEN=""
SENT_EMAIL_DOCUMENTS_BUCKET_2_NAME="cultureamp-dev-sent-email-documents-2-us-west-2"
SENT_EMAIL_TRANSLATION_DOCUMENTS_BUCKET_NAME="cultureamp-dev-sent-email-translation-documents-us-west-2"
SMS_SERVICE_LOCKBOX_KEY=0000000000000000000000000000000000000000000000000000000000000000
